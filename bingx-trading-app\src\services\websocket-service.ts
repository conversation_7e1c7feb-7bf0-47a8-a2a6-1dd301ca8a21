import { useMarketStore } from '@/store/trading-store';
import { TickerWebSocketData, TradeWebSocketData } from '@/types/bingx';

export class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private subscribedSymbols = new Set<string>();
  private pingInterval: NodeJS.Timeout | null = null;

  constructor(private wsUrl: string = 'wss://open-api-ws.bingx.com/market') {}

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
        return;
      }

      this.isConnecting = true;
      
      try {
        this.ws = new WebSocket(this.wsUrl);
        
        this.ws.onopen = () => {
          console.log('WebSocket connecté à BingX');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          useMarketStore.getState().setConnectionStatus(true);
          
          // Démarrer le ping pour maintenir la connexion
          this.startPing();
          
          // Réabonner aux symboles précédents
          this.resubscribeToSymbols();
          
          resolve();
        };

        this.ws.onmessage = async (event) => {
          try {
            let messageData: string;

            // Gérer les différents types de données WebSocket
            if (event.data instanceof Blob) {
              messageData = await event.data.text();
            } else if (typeof event.data === 'string') {
              messageData = event.data;
            } else {
              console.warn('Type de données WebSocket non supporté:', typeof event.data);
              return;
            }

            // Ignorer les messages vides ou de ping
            if (!messageData || messageData === 'pong') {
              return;
            }

            const data = JSON.parse(messageData);
            this.handleMessage(data);
          } catch (error) {
            console.error('Erreur lors du parsing du message WebSocket:', error, event.data);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket fermé:', event.code, event.reason);
          this.isConnecting = false;
          useMarketStore.getState().setConnectionStatus(false);
          this.stopPing();
          
          // Tentative de reconnexion automatique
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('Erreur WebSocket:', error);
          this.isConnecting = false;
          useMarketStore.getState().setConnectionStatus(false);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect() {
    this.stopPing();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    useMarketStore.getState().setConnectionStatus(false);
  }

  private scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts} dans ${delay}ms`);
    
    setTimeout(() => {
      this.connect().catch(console.error);
    }, delay);
  }

  private startPing() {
    this.pingInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ method: 'PING' }));
      }
    }, 30000); // Ping toutes les 30 secondes
  }

  private stopPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private resubscribeToSymbols() {
    this.subscribedSymbols.forEach(symbol => {
      this.subscribeToTicker(symbol);
    });
  }

  subscribeToTicker(symbol: string) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket non connecté, impossible de s\'abonner à', symbol);
      return;
    }

    // Format BingX pour les tickers
    const symbolFormatted = symbol.toLowerCase().replace('-', '');
    const subscription = {
      method: 'SUBSCRIBE',
      params: [`${symbolFormatted}@ticker`],
      id: Date.now(),
    };

    this.ws.send(JSON.stringify(subscription));
    this.subscribedSymbols.add(symbol);
    console.log('Abonnement au ticker:', symbol, 'formaté:', symbolFormatted);
  }

  unsubscribeFromTicker(symbol: string) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    const symbolFormatted = symbol.toLowerCase().replace('-', '');
    const unsubscription = {
      method: 'UNSUBSCRIBE',
      params: [`${symbolFormatted}@ticker`],
      id: Date.now(),
    };

    this.ws.send(JSON.stringify(unsubscription));
    this.subscribedSymbols.delete(symbol);
    console.log('Désabonnement du ticker:', symbol);
  }

  subscribeToTrades(symbol: string) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    const symbolFormatted = symbol.toLowerCase().replace('-', '');
    const subscription = {
      method: 'SUBSCRIBE',
      params: [`${symbolFormatted}@trade`],
      id: Date.now(),
    };

    this.ws.send(JSON.stringify(subscription));
    console.log('Abonnement aux trades:', symbol);
  }

  private handleMessage(data: any) {
    // Ignorer les messages de confirmation et de ping
    if (data.result === null || data.method === 'PONG' || data.pong) {
      return;
    }

    // Messages de confirmation d'abonnement
    if (data.id && data.result === null) {
      console.log('Abonnement confirmé pour ID:', data.id);
      return;
    }

    // Messages d'erreur
    if (data.error) {
      console.error('Erreur WebSocket BingX:', data.error);
      return;
    }

    // Messages de stream de données
    if (data.stream && data.data) {
      const [symbol, dataType] = data.stream.split('@');
      const symbolUpper = symbol.toUpperCase().replace('usdt', '-USDT');

      switch (dataType) {
        case 'ticker':
          this.handleTickerUpdate(symbolUpper, data.data);
          break;
        case 'trade':
          this.handleTradeUpdate(symbolUpper, data.data);
          break;
        default:
          console.log('Type de données non géré:', dataType, data);
      }
    } else if (data.e) {
      // Format alternatif des messages BingX
      const eventType = data.e;
      const symbol = data.s;

      switch (eventType) {
        case '24hrTicker':
          this.handleTickerUpdate(symbol, data);
          break;
        case 'trade':
          this.handleTradeUpdate(symbol, data);
          break;
        default:
          console.log('Event type non géré:', eventType, data);
      }
    } else {
      console.log('Message WebSocket non reconnu:', data);
    }
  }

  private handleTickerUpdate(symbol: string, tickerData: TickerWebSocketData) {
    const ticker = {
      symbol,
      priceChange: tickerData.p,
      priceChangePercent: tickerData.P,
      weightedAvgPrice: '0',
      lastPrice: tickerData.c,
      lastQty: '0',
      openPrice: tickerData.o,
      highPrice: tickerData.h,
      lowPrice: tickerData.l,
      volume: tickerData.v,
      quoteVolume: tickerData.q,
      openTime: 0,
      closeTime: tickerData.E,
      count: 0,
    };

    useMarketStore.getState().updateTicker(symbol, ticker);
  }

  private handleTradeUpdate(symbol: string, tradeData: TradeWebSocketData) {
    // Ici on pourrait mettre à jour les trades récents
    console.log('Trade update pour', symbol, tradeData);
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'CLOSED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING';
      case WebSocket.OPEN:
        return 'OPEN';
      case WebSocket.CLOSING:
        return 'CLOSING';
      case WebSocket.CLOSED:
        return 'CLOSED';
      default:
        return 'UNKNOWN';
    }
  }
}

// Instance singleton du service WebSocket
export const webSocketService = new WebSocketService();
