{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/services/bingx-api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { AccountInfo, Position, Order, OrderRequest, Ticker24hr } from '@/types/bingx';\n\nconst API_BASE = '/api/bingx';\n\n// Configuration axios avec intercepteurs pour la gestion d'erreurs\nconst apiClient = axios.create({\n  baseURL: API_BASE,\n  timeout: 15000,\n});\n\n// Intercepteur pour les réponses d'erreur\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('Erreur API:', error.response?.data || error.message);\n    throw error;\n  }\n);\n\nexport class BingXApiService {\n  // Services pour les comptes\n  static async getAccountInfo(): Promise<AccountInfo> {\n    const response = await apiClient.get<AccountInfo>('/account');\n    return response.data;\n  }\n\n  // Services pour les positions\n  static async getPositions(symbol?: string): Promise<Position[]> {\n    const params = symbol ? { symbol } : {};\n    const response = await apiClient.get<Position[]>('/positions', { params });\n    return response.data;\n  }\n\n  // Services pour les ordres\n  static async getOpenOrders(symbol?: string): Promise<Order[]> {\n    const params = symbol ? { symbol } : {};\n    const response = await apiClient.get<Order[]>('/orders', { params });\n    return response.data;\n  }\n\n  static async getOrder(orderId: string, symbol?: string): Promise<Order> {\n    const params: any = { orderId };\n    if (symbol) params.symbol = symbol;\n    const response = await apiClient.get<Order>('/orders', { params });\n    return response.data;\n  }\n\n  static async createOrder(orderData: OrderRequest): Promise<Order> {\n    const response = await apiClient.post<Order>('/orders', orderData);\n    return response.data;\n  }\n\n  static async cancelOrder(orderId: string, symbol: string): Promise<Order> {\n    const response = await apiClient.delete<Order>('/orders', {\n      params: { orderId, symbol }\n    });\n    return response.data;\n  }\n\n  // Services pour les données de marché\n  static async getTicker(symbol?: string): Promise<Ticker24hr | Ticker24hr[]> {\n    const params = symbol ? { symbol } : {};\n    const response = await apiClient.get<Ticker24hr | Ticker24hr[]>('/market/ticker', { params });\n    return response.data;\n  }\n\n  // Utilitaires pour les ordres\n  static async createMarketOrder(\n    symbol: string,\n    side: 'BUY' | 'SELL',\n    quantity: string\n  ): Promise<Order> {\n    return this.createOrder({\n      symbol,\n      side,\n      type: 'MARKET',\n      quantity,\n    });\n  }\n\n  static async createLimitOrder(\n    symbol: string,\n    side: 'BUY' | 'SELL',\n    quantity: string,\n    price: string\n  ): Promise<Order> {\n    return this.createOrder({\n      symbol,\n      side,\n      type: 'LIMIT',\n      quantity,\n      price,\n      timeInForce: 'GTC',\n    });\n  }\n\n  static async createStopLossOrder(\n    symbol: string,\n    side: 'BUY' | 'SELL',\n    quantity: string,\n    stopPrice: string\n  ): Promise<Order> {\n    return this.createOrder({\n      symbol,\n      side,\n      type: 'STOP_MARKET',\n      quantity,\n      stopPrice,\n      reduceOnly: true,\n    });\n  }\n\n  static async createTakeProfitOrder(\n    symbol: string,\n    side: 'BUY' | 'SELL',\n    quantity: string,\n    stopPrice: string\n  ): Promise<Order> {\n    return this.createOrder({\n      symbol,\n      side,\n      type: 'TAKE_PROFIT_MARKET',\n      quantity,\n      stopPrice,\n      reduceOnly: true,\n    });\n  }\n\n  // Utilitaires pour fermer des positions\n  static async closePosition(symbol: string, positionSide: 'LONG' | 'SHORT'): Promise<Order> {\n    const side = positionSide === 'LONG' ? 'SELL' : 'BUY';\n    return this.createOrder({\n      symbol,\n      side,\n      type: 'MARKET',\n      closePosition: true,\n    });\n  }\n\n  // Gestion des erreurs avec retry\n  static async withRetry<T>(\n    operation: () => Promise<T>,\n    maxRetries: number = 3,\n    delay: number = 1000\n  ): Promise<T> {\n    let lastError: Error;\n    \n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        return await operation();\n      } catch (error) {\n        lastError = error as Error;\n        \n        if (attempt === maxRetries) {\n          throw lastError;\n        }\n        \n        // Attendre avant de réessayer\n        await new Promise(resolve => setTimeout(resolve, delay * attempt));\n      }\n    }\n    \n    throw lastError!;\n  }\n}\n\nexport default BingXApiService;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA,MAAM,WAAW;AAEjB,mEAAmE;AACnE,MAAM,YAAY,MAAM,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;AACX;AAEA,0CAA0C;AAC1C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,eAAe,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;IAClE,MAAM;AACR;AAGK,MAAM;IACX,4BAA4B;IAC5B,aAAa,iBAAuC;QAClD,MAAM,WAAW,MAAM,UAAU,GAAG,CAAc;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa,aAAa,MAAe,EAAuB;QAC9D,MAAM,SAAS,SAAS;YAAE;QAAO,IAAI,CAAC;QACtC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAa,cAAc;YAAE;QAAO;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,aAAa,cAAc,MAAe,EAAoB;QAC5D,MAAM,SAAS,SAAS;YAAE;QAAO,IAAI,CAAC;QACtC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,WAAW;YAAE;QAAO;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,SAAS,OAAe,EAAE,MAAe,EAAkB;QACtE,MAAM,SAAc;YAAE;QAAQ;QAC9B,IAAI,QAAQ,OAAO,MAAM,GAAG;QAC5B,MAAM,WAAW,MAAM,UAAU,GAAG,CAAQ,WAAW;YAAE;QAAO;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,YAAY,SAAuB,EAAkB;QAChE,MAAM,WAAW,MAAM,UAAU,IAAI,CAAQ,WAAW;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,YAAY,OAAe,EAAE,MAAc,EAAkB;QACxE,MAAM,WAAW,MAAM,UAAU,MAAM,CAAQ,WAAW;YACxD,QAAQ;gBAAE;gBAAS;YAAO;QAC5B;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,sCAAsC;IACtC,aAAa,UAAU,MAAe,EAAsC;QAC1E,MAAM,SAAS,SAAS;YAAE;QAAO,IAAI,CAAC;QACtC,MAAM,WAAW,MAAM,UAAU,GAAG,CAA4B,kBAAkB;YAAE;QAAO;QAC3F,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,aAAa,kBACX,MAAc,EACd,IAAoB,EACpB,QAAgB,EACA;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB;YACA;YACA,MAAM;YACN;QACF;IACF;IAEA,aAAa,iBACX,MAAc,EACd,IAAoB,EACpB,QAAgB,EAChB,KAAa,EACG;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB;YACA;YACA,MAAM;YACN;YACA;YACA,aAAa;QACf;IACF;IAEA,aAAa,oBACX,MAAc,EACd,IAAoB,EACpB,QAAgB,EAChB,SAAiB,EACD;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB;YACA;YACA,MAAM;YACN;YACA;YACA,YAAY;QACd;IACF;IAEA,aAAa,sBACX,MAAc,EACd,IAAoB,EACpB,QAAgB,EAChB,SAAiB,EACD;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB;YACA;YACA,MAAM;YACN;YACA;YACA,YAAY;QACd;IACF;IAEA,wCAAwC;IACxC,aAAa,cAAc,MAAc,EAAE,YAA8B,EAAkB;QACzF,MAAM,OAAO,iBAAiB,SAAS,SAAS;QAChD,OAAO,IAAI,CAAC,WAAW,CAAC;YACtB;YACA;YACA,MAAM;YACN,eAAe;QACjB;IACF;IAEA,iCAAiC;IACjC,aAAa,UACX,SAA2B,EAC3B,aAAqB,CAAC,EACtB,QAAgB,IAAI,EACR;QACZ,IAAI;QAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,OAAO,MAAM;YACf,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,IAAI,YAAY,YAAY;oBAC1B,MAAM;gBACR;gBAEA,8BAA8B;gBAC9B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ;YAC3D;QACF;QAEA,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/store/trading-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools } from 'zustand/middleware';\nimport { AccountInfo, Position, Order, Ticker24hr, TradingState, MarketData } from '@/types/bingx';\nimport BingXApiService from '@/services/bingx-api';\n\ninterface TradingStore extends TradingState {\n  // Actions pour les comptes\n  fetchAccountInfo: () => Promise<void>;\n  \n  // Actions pour les positions\n  fetchPositions: (symbol?: string) => Promise<void>;\n  \n  // Actions pour les ordres\n  fetchOrders: (symbol?: string) => Promise<void>;\n  createOrder: (orderData: any) => Promise<Order | null>;\n  cancelOrder: (orderId: string, symbol: string) => Promise<void>;\n  \n  // Actions pour l'état général\n  setSelectedSymbol: (symbol: string) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  setLoading: (loading: boolean) => void;\n  \n  // Actions pour rafraîchir toutes les données\n  refreshAllData: () => Promise<void>;\n}\n\ninterface MarketStore extends MarketData {\n  // Actions pour les données de marché\n  fetchTickers: (symbol?: string) => Promise<void>;\n  updateTicker: (symbol: string, ticker: Ticker24hr) => void;\n  setConnectionStatus: (connected: boolean) => void;\n}\n\n// Store principal pour les données de trading\nexport const useTradingStore = create<TradingStore>()(\n  devtools(\n    (set, get) => ({\n      // État initial\n      account: null,\n      positions: [],\n      orders: [],\n      selectedSymbol: 'BTC-USDT',\n      isLoading: false,\n      error: null,\n\n      // Actions pour les comptes\n      fetchAccountInfo: async () => {\n        try {\n          set({ isLoading: true, error: null });\n          const account = await BingXApiService.getAccountInfo();\n          set({ account, isLoading: false });\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Erreur lors de la récupération du compte';\n          set({ error: errorMessage, isLoading: false });\n          throw error;\n        }\n      },\n\n      // Actions pour les positions\n      fetchPositions: async (symbol?: string) => {\n        try {\n          set({ isLoading: true, error: null });\n          const positions = await BingXApiService.getPositions(symbol);\n          set({ positions, isLoading: false });\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Erreur lors de la récupération des positions';\n          set({ error: errorMessage, isLoading: false });\n          throw error;\n        }\n      },\n\n      // Actions pour les ordres\n      fetchOrders: async (symbol?: string) => {\n        try {\n          set({ isLoading: true, error: null });\n          const orders = await BingXApiService.getOpenOrders(symbol);\n          set({ orders, isLoading: false });\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Erreur lors de la récupération des ordres';\n          set({ error: errorMessage, isLoading: false });\n          throw error;\n        }\n      },\n\n      createOrder: async (orderData: any) => {\n        try {\n          set({ isLoading: true, error: null });\n          const newOrder = await BingXApiService.createOrder(orderData);\n          \n          // Rafraîchir les ordres et positions après création\n          await Promise.all([\n            get().fetchOrders(),\n            get().fetchPositions(),\n            get().fetchAccountInfo(),\n          ]);\n          \n          set({ isLoading: false });\n          return newOrder;\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Erreur lors de la création de l\\'ordre';\n          set({ error: errorMessage, isLoading: false });\n          return null;\n        }\n      },\n\n      cancelOrder: async (orderId: string, symbol: string) => {\n        try {\n          set({ isLoading: true, error: null });\n          await BingXApiService.cancelOrder(orderId, symbol);\n          \n          // Rafraîchir les ordres après annulation\n          await get().fetchOrders();\n          set({ isLoading: false });\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Erreur lors de l\\'annulation de l\\'ordre';\n          set({ error: errorMessage, isLoading: false });\n          throw error;\n        }\n      },\n\n      // Actions pour l'état général\n      setSelectedSymbol: (symbol: string) => {\n        set({ selectedSymbol: symbol });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n      },\n\n      clearError: () => {\n        set({ error: null });\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n\n      // Rafraîchir toutes les données\n      refreshAllData: async () => {\n        const { selectedSymbol } = get();\n        try {\n          set({ isLoading: true, error: null });\n          \n          await Promise.all([\n            get().fetchAccountInfo(),\n            get().fetchPositions(selectedSymbol),\n            get().fetchOrders(selectedSymbol),\n          ]);\n          \n          set({ isLoading: false });\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Erreur lors du rafraîchissement des données';\n          set({ error: errorMessage, isLoading: false });\n        }\n      },\n    }),\n    {\n      name: 'trading-store',\n    }\n  )\n);\n\n// Store pour les données de marché\nexport const useMarketStore = create<MarketStore>()(\n  devtools(\n    (set, get) => ({\n      // État initial\n      tickers: {},\n      orderBooks: {},\n      recentTrades: {},\n      isConnected: false,\n\n      // Actions pour les données de marché\n      fetchTickers: async (symbol?: string) => {\n        try {\n          const tickerData = await BingXApiService.getTicker(symbol);\n          \n          if (Array.isArray(tickerData)) {\n            // Plusieurs tickers\n            const tickersMap = tickerData.reduce((acc, ticker) => {\n              acc[ticker.symbol] = ticker;\n              return acc;\n            }, {} as Record<string, Ticker24hr>);\n            \n            set(state => ({\n              tickers: { ...state.tickers, ...tickersMap }\n            }));\n          } else {\n            // Un seul ticker\n            set(state => ({\n              tickers: { ...state.tickers, [tickerData.symbol]: tickerData }\n            }));\n          }\n        } catch (error) {\n          console.error('Erreur lors de la récupération des tickers:', error);\n        }\n      },\n\n      updateTicker: (symbol: string, ticker: Ticker24hr) => {\n        set(state => ({\n          tickers: { ...state.tickers, [symbol]: ticker }\n        }));\n      },\n\n      setConnectionStatus: (connected: boolean) => {\n        set({ isConnected: connected });\n      },\n    }),\n    {\n      name: 'market-store',\n    }\n  )\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAGA;;;;AAgCO,MAAM,kBAAkB,SAC7B,SACE,CAAC,KAAK,MAAQ,CAAC;QACb,eAAe;QACf,SAAS;QACT,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB;QAChB,WAAW;QACX,OAAO;QAEP,2BAA2B;QAC3B,kBAAkB;YAChB,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,UAAU,MAAM,0IAAe,CAAC,cAAc;gBACpD,IAAI;oBAAE;oBAAS,WAAW;gBAAM;YAClC,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,MAAM;YACR;QACF;QAEA,6BAA6B;QAC7B,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,YAAY,MAAM,0IAAe,CAAC,YAAY,CAAC;gBACrD,IAAI;oBAAE;oBAAW,WAAW;gBAAM;YACpC,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,MAAM;YACR;QACF;QAEA,0BAA0B;QAC1B,aAAa,OAAO;YAClB,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,SAAS,MAAM,0IAAe,CAAC,aAAa,CAAC;gBACnD,IAAI;oBAAE;oBAAQ,WAAW;gBAAM;YACjC,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,MAAM;YACR;QACF;QAEA,aAAa,OAAO;YAClB,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,WAAW,MAAM,0IAAe,CAAC,WAAW,CAAC;gBAEnD,oDAAoD;gBACpD,MAAM,QAAQ,GAAG,CAAC;oBAChB,MAAM,WAAW;oBACjB,MAAM,cAAc;oBACpB,MAAM,gBAAgB;iBACvB;gBAED,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,OAAO;YACT;QACF;QAEA,aAAa,OAAO,SAAiB;YACnC,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,0IAAe,CAAC,WAAW,CAAC,SAAS;gBAE3C,yCAAyC;gBACzC,MAAM,MAAM,WAAW;gBACvB,IAAI;oBAAE,WAAW;gBAAM;YACzB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,MAAM;YACR;QACF;QAEA,8BAA8B;QAC9B,mBAAmB,CAAC;YAClB,IAAI;gBAAE,gBAAgB;YAAO;QAC/B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,gCAAgC;QAChC,gBAAgB;YACd,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,MAAM,QAAQ,GAAG,CAAC;oBAChB,MAAM,gBAAgB;oBACtB,MAAM,cAAc,CAAC;oBACrB,MAAM,WAAW,CAAC;iBACnB;gBAED,IAAI;oBAAE,WAAW;gBAAM;YACzB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;YAC9C;QACF;IACF,CAAC,GACD;IACE,MAAM;AACR;AAKG,MAAM,iBAAiB,SAC5B,SACE,CAAC,KAAK,MAAQ,CAAC;QACb,eAAe;QACf,SAAS,CAAC;QACV,YAAY,CAAC;QACb,cAAc,CAAC;QACf,aAAa;QAEb,qCAAqC;QACrC,cAAc,OAAO;YACnB,IAAI;gBACF,MAAM,aAAa,MAAM,0IAAe,CAAC,SAAS,CAAC;gBAEnD,IAAI,MAAM,OAAO,CAAC,aAAa;oBAC7B,oBAAoB;oBACpB,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK;wBACzC,GAAG,CAAC,OAAO,MAAM,CAAC,GAAG;wBACrB,OAAO;oBACT,GAAG,CAAC;oBAEJ,IAAI,CAAA,QAAS,CAAC;4BACZ,SAAS;gCAAE,GAAG,MAAM,OAAO;gCAAE,GAAG,UAAU;4BAAC;wBAC7C,CAAC;gBACH,OAAO;oBACL,iBAAiB;oBACjB,IAAI,CAAA,QAAS,CAAC;4BACZ,SAAS;gCAAE,GAAG,MAAM,OAAO;gCAAE,CAAC,WAAW,MAAM,CAAC,EAAE;4BAAW;wBAC/D,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;QACF;QAEA,cAAc,CAAC,QAAgB;YAC7B,IAAI,CAAA,QAAS,CAAC;oBACZ,SAAS;wBAAE,GAAG,MAAM,OAAO;wBAAE,CAAC,OAAO,EAAE;oBAAO;gBAChD,CAAC;QACH;QAEA,qBAAqB,CAAC;YACpB,IAAI;gBAAE,aAAa;YAAU;QAC/B;IACF,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/services/websocket-service.ts"], "sourcesContent": ["import { useMarketStore } from '@/store/trading-store';\nimport { TickerWebSocketData, TradeWebSocketData } from '@/types/bingx';\n\nexport class WebSocketService {\n  private ws: WebSocket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000;\n  private isConnecting = false;\n  private subscribedSymbols = new Set<string>();\n  private pingInterval: NodeJS.Timeout | null = null;\n\n  constructor(private wsUrl: string = 'wss://open-api-ws.bingx.com/market') {}\n\n  connect(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {\n        return;\n      }\n\n      this.isConnecting = true;\n      \n      try {\n        this.ws = new WebSocket(this.wsUrl);\n        \n        this.ws.onopen = () => {\n          console.log('WebSocket connecté à BingX');\n          this.isConnecting = false;\n          this.reconnectAttempts = 0;\n          useMarketStore.getState().setConnectionStatus(true);\n          \n          // Démarrer le ping pour maintenir la connexion\n          this.startPing();\n          \n          // Réabonner aux symboles précédents\n          this.resubscribeToSymbols();\n          \n          resolve();\n        };\n\n        this.ws.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data);\n            this.handleMessage(data);\n          } catch (error) {\n            console.error('Erreur lors du parsing du message WebSocket:', error);\n          }\n        };\n\n        this.ws.onclose = (event) => {\n          console.log('WebSocket fermé:', event.code, event.reason);\n          this.isConnecting = false;\n          useMarketStore.getState().setConnectionStatus(false);\n          this.stopPing();\n          \n          // Tentative de reconnexion automatique\n          if (this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.scheduleReconnect();\n          }\n        };\n\n        this.ws.onerror = (error) => {\n          console.error('Erreur WebSocket:', error);\n          this.isConnecting = false;\n          useMarketStore.getState().setConnectionStatus(false);\n          reject(error);\n        };\n\n      } catch (error) {\n        this.isConnecting = false;\n        reject(error);\n      }\n    });\n  }\n\n  disconnect() {\n    this.stopPing();\n    if (this.ws) {\n      this.ws.close();\n      this.ws = null;\n    }\n    useMarketStore.getState().setConnectionStatus(false);\n  }\n\n  private scheduleReconnect() {\n    this.reconnectAttempts++;\n    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n    \n    console.log(`Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts} dans ${delay}ms`);\n    \n    setTimeout(() => {\n      this.connect().catch(console.error);\n    }, delay);\n  }\n\n  private startPing() {\n    this.pingInterval = setInterval(() => {\n      if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n        this.ws.send(JSON.stringify({ method: 'PING' }));\n      }\n    }, 30000); // Ping toutes les 30 secondes\n  }\n\n  private stopPing() {\n    if (this.pingInterval) {\n      clearInterval(this.pingInterval);\n      this.pingInterval = null;\n    }\n  }\n\n  private resubscribeToSymbols() {\n    this.subscribedSymbols.forEach(symbol => {\n      this.subscribeToTicker(symbol);\n    });\n  }\n\n  subscribeToTicker(symbol: string) {\n    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {\n      console.warn('WebSocket non connecté, impossible de s\\'abonner à', symbol);\n      return;\n    }\n\n    const subscription = {\n      method: 'SUBSCRIBE',\n      params: [`${symbol.toLowerCase()}@ticker`],\n      id: Date.now(),\n    };\n\n    this.ws.send(JSON.stringify(subscription));\n    this.subscribedSymbols.add(symbol);\n    console.log('Abonnement au ticker:', symbol);\n  }\n\n  unsubscribeFromTicker(symbol: string) {\n    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {\n      return;\n    }\n\n    const unsubscription = {\n      method: 'UNSUBSCRIBE',\n      params: [`${symbol.toLowerCase()}@ticker`],\n      id: Date.now(),\n    };\n\n    this.ws.send(JSON.stringify(unsubscription));\n    this.subscribedSymbols.delete(symbol);\n    console.log('Désabonnement du ticker:', symbol);\n  }\n\n  subscribeToTrades(symbol: string) {\n    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {\n      return;\n    }\n\n    const subscription = {\n      method: 'SUBSCRIBE',\n      params: [`${symbol.toLowerCase()}@trade`],\n      id: Date.now(),\n    };\n\n    this.ws.send(JSON.stringify(subscription));\n    console.log('Abonnement aux trades:', symbol);\n  }\n\n  private handleMessage(data: any) {\n    // Ignorer les messages de confirmation et de ping\n    if (data.result === null || data.method === 'PONG') {\n      return;\n    }\n\n    // Messages de stream de données\n    if (data.stream && data.data) {\n      const [symbol, dataType] = data.stream.split('@');\n      const symbolUpper = symbol.toUpperCase().replace('usdt', '-USDT');\n\n      switch (dataType) {\n        case 'ticker':\n          this.handleTickerUpdate(symbolUpper, data.data);\n          break;\n        case 'trade':\n          this.handleTradeUpdate(symbolUpper, data.data);\n          break;\n        default:\n          console.log('Type de données non géré:', dataType);\n      }\n    }\n  }\n\n  private handleTickerUpdate(symbol: string, tickerData: TickerWebSocketData) {\n    const ticker = {\n      symbol,\n      priceChange: tickerData.p,\n      priceChangePercent: tickerData.P,\n      weightedAvgPrice: '0',\n      lastPrice: tickerData.c,\n      lastQty: '0',\n      openPrice: tickerData.o,\n      highPrice: tickerData.h,\n      lowPrice: tickerData.l,\n      volume: tickerData.v,\n      quoteVolume: tickerData.q,\n      openTime: 0,\n      closeTime: tickerData.E,\n      count: 0,\n    };\n\n    useMarketStore.getState().updateTicker(symbol, ticker);\n  }\n\n  private handleTradeUpdate(symbol: string, tradeData: TradeWebSocketData) {\n    // Ici on pourrait mettre à jour les trades récents\n    console.log('Trade update pour', symbol, tradeData);\n  }\n\n  isConnected(): boolean {\n    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;\n  }\n\n  getConnectionState(): string {\n    if (!this.ws) return 'CLOSED';\n    \n    switch (this.ws.readyState) {\n      case WebSocket.CONNECTING:\n        return 'CONNECTING';\n      case WebSocket.OPEN:\n        return 'OPEN';\n      case WebSocket.CLOSING:\n        return 'CLOSING';\n      case WebSocket.CLOSED:\n        return 'CLOSED';\n      default:\n        return 'UNKNOWN';\n    }\n  }\n}\n\n// Instance singleton du service WebSocket\nexport const webSocketService = new WebSocketService();\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGO,MAAM;;IACH,GAA4B;IAC5B,kBAAsB;IACtB,qBAAyB;IACzB,eAAsB;IACtB,aAAqB;IACrB,kBAAsC;IACtC,aAA2C;IAEnD,YAAY,AAAQ,QAAgB,oCAAoC,CAAE;aAAtD,QAAA;aARZ,KAAuB;aACvB,oBAAoB;aACpB,uBAAuB;aACvB,iBAAiB;aACjB,eAAe;aACf,oBAAoB,IAAI;aACxB,eAAsC;IAE6B;IAE3E,UAAyB;QACvB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,IAAI,CAAC,YAAY,IAAK,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,UAAU,EAAG;gBACjF;YACF;YAEA,IAAI,CAAC,YAAY,GAAG;YAEpB,IAAI;gBACF,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,IAAI,CAAC,KAAK;gBAElC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;oBACf,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,YAAY,GAAG;oBACpB,IAAI,CAAC,iBAAiB,GAAG;oBACzB,kJAAc,CAAC,QAAQ,GAAG,mBAAmB,CAAC;oBAE9C,+CAA+C;oBAC/C,IAAI,CAAC,SAAS;oBAEd,oCAAoC;oBACpC,IAAI,CAAC,oBAAoB;oBAEzB;gBACF;gBAEA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;oBACnB,IAAI;wBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;wBAClC,IAAI,CAAC,aAAa,CAAC;oBACrB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gDAAgD;oBAChE;gBACF;gBAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC;oBACjB,QAAQ,GAAG,CAAC,oBAAoB,MAAM,IAAI,EAAE,MAAM,MAAM;oBACxD,IAAI,CAAC,YAAY,GAAG;oBACpB,kJAAc,CAAC,QAAQ,GAAG,mBAAmB,CAAC;oBAC9C,IAAI,CAAC,QAAQ;oBAEb,uCAAuC;oBACvC,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;wBACtD,IAAI,CAAC,iBAAiB;oBACxB;gBACF;gBAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC;oBACjB,QAAQ,KAAK,CAAC,qBAAqB;oBACnC,IAAI,CAAC,YAAY,GAAG;oBACpB,kJAAc,CAAC,QAAQ,GAAG,mBAAmB,CAAC;oBAC9C,OAAO;gBACT;YAEF,EAAE,OAAO,OAAO;gBACd,IAAI,CAAC,YAAY,GAAG;gBACpB,OAAO;YACT;QACF;IACF;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ;QACb,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACZ;QACA,kJAAc,CAAC,QAAQ,GAAG,mBAAmB,CAAC;IAChD;IAEQ,oBAAoB;QAC1B,IAAI,CAAC,iBAAiB;QACtB,MAAM,QAAQ,IAAI,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QAEzE,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QAE7G,WAAW;YACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,QAAQ,KAAK;QACpC,GAAG;IACL;IAEQ,YAAY;QAClB,IAAI,CAAC,YAAY,GAAG,YAAY;YAC9B,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;gBACpD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAO;YAC/C;QACF,GAAG,QAAQ,8BAA8B;IAC3C;IAEQ,WAAW;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,cAAc,IAAI,CAAC,YAAY;YAC/B,IAAI,CAAC,YAAY,GAAG;QACtB;IACF;IAEQ,uBAAuB;QAC7B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;YAC7B,IAAI,CAAC,iBAAiB,CAAC;QACzB;IACF;IAEA,kBAAkB,MAAc,EAAE;QAChC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACrD,QAAQ,IAAI,CAAC,sDAAsD;YACnE;QACF;QAEA,MAAM,eAAe;YACnB,QAAQ;YACR,QAAQ;gBAAC,GAAG,OAAO,WAAW,GAAG,OAAO,CAAC;aAAC;YAC1C,IAAI,KAAK,GAAG;QACd;QAEA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QAC3B,QAAQ,GAAG,CAAC,yBAAyB;IACvC;IAEA,sBAAsB,MAAc,EAAE;QACpC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACrD;QACF;QAEA,MAAM,iBAAiB;YACrB,QAAQ;YACR,QAAQ;gBAAC,GAAG,OAAO,WAAW,GAAG,OAAO,CAAC;aAAC;YAC1C,IAAI,KAAK,GAAG;QACd;QAEA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;QAC9B,QAAQ,GAAG,CAAC,4BAA4B;IAC1C;IAEA,kBAAkB,MAAc,EAAE;QAChC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACrD;QACF;QAEA,MAAM,eAAe;YACnB,QAAQ;YACR,QAAQ;gBAAC,GAAG,OAAO,WAAW,GAAG,MAAM,CAAC;aAAC;YACzC,IAAI,KAAK,GAAG;QACd;QAEA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QAC5B,QAAQ,GAAG,CAAC,0BAA0B;IACxC;IAEQ,cAAc,IAAS,EAAE;QAC/B,kDAAkD;QAClD,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,QAAQ;YAClD;QACF;QAEA,gCAAgC;QAChC,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE;YAC5B,MAAM,CAAC,QAAQ,SAAS,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC;YAC7C,MAAM,cAAc,OAAO,WAAW,GAAG,OAAO,CAAC,QAAQ;YAEzD,OAAQ;gBACN,KAAK;oBACH,IAAI,CAAC,kBAAkB,CAAC,aAAa,KAAK,IAAI;oBAC9C;gBACF,KAAK;oBACH,IAAI,CAAC,iBAAiB,CAAC,aAAa,KAAK,IAAI;oBAC7C;gBACF;oBACE,QAAQ,GAAG,CAAC,6BAA6B;YAC7C;QACF;IACF;IAEQ,mBAAmB,MAAc,EAAE,UAA+B,EAAE;QAC1E,MAAM,SAAS;YACb;YACA,aAAa,WAAW,CAAC;YACzB,oBAAoB,WAAW,CAAC;YAChC,kBAAkB;YAClB,WAAW,WAAW,CAAC;YACvB,SAAS;YACT,WAAW,WAAW,CAAC;YACvB,WAAW,WAAW,CAAC;YACvB,UAAU,WAAW,CAAC;YACtB,QAAQ,WAAW,CAAC;YACpB,aAAa,WAAW,CAAC;YACzB,UAAU;YACV,WAAW,WAAW,CAAC;YACvB,OAAO;QACT;QAEA,kJAAc,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ;IACjD;IAEQ,kBAAkB,MAAc,EAAE,SAA6B,EAAE;QACvE,mDAAmD;QACnD,QAAQ,GAAG,CAAC,qBAAqB,QAAQ;IAC3C;IAEA,cAAuB;QACrB,OAAO,IAAI,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,IAAI;IAClE;IAEA,qBAA6B;QAC3B,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO;QAErB,OAAQ,IAAI,CAAC,EAAE,CAAC,UAAU;YACxB,KAAK,UAAU,UAAU;gBACvB,OAAO;YACT,KAAK,UAAU,IAAI;gBACjB,OAAO;YACT,KAAK,UAAU,OAAO;gBACpB,OAAO;YACT,KAAK,UAAU,MAAM;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/hooks/useWebSocket.ts"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { webSocketService } from '@/services/websocket-service';\nimport { useMarketStore } from '@/store/trading-store';\n\nexport function useWebSocket() {\n  const { setConnectionStatus } = useMarketStore();\n  const isInitialized = useRef(false);\n\n  useEffect(() => {\n    if (isInitialized.current) return;\n    isInitialized.current = true;\n\n    const connectWebSocket = async () => {\n      try {\n        await webSocketService.connect();\n        \n        // S'abonner aux tickers populaires\n        const popularSymbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'SOL-USDT', 'DOGE-USDT'];\n        popularSymbols.forEach(symbol => {\n          webSocketService.subscribeToTicker(symbol);\n        });\n        \n      } catch (error) {\n        console.error('Erreur lors de la connexion WebSocket:', error);\n        setConnectionStatus(false);\n      }\n    };\n\n    connectWebSocket();\n\n    // Nettoyage lors du démontage\n    return () => {\n      webSocketService.disconnect();\n    };\n  }, [setConnectionStatus]);\n\n  return {\n    isConnected: webSocketService.isConnected(),\n    connectionState: webSocketService.getConnectionState(),\n    subscribeToTicker: (symbol: string) => webSocketService.subscribeToTicker(symbol),\n    unsubscribeFromTicker: (symbol: string) => webSocketService.unsubscribeFromTicker(symbol),\n    subscribeToTrades: (symbol: string) => webSocketService.subscribeToTrades(symbol),\n  };\n}\n\nexport function useSymbolWebSocket(symbol: string) {\n  const webSocket = useWebSocket();\n\n  useEffect(() => {\n    if (webSocket.isConnected && symbol) {\n      webSocket.subscribeToTicker(symbol);\n      \n      return () => {\n        webSocket.unsubscribeFromTicker(symbol);\n      };\n    }\n  }, [symbol, webSocket]);\n\n  return webSocket;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS;IACd,MAAM,EAAE,mBAAmB,EAAE,GAAG,IAAA,kJAAc;IAC9C,MAAM,gBAAgB,IAAA,+MAAM,EAAC;IAE7B,IAAA,kNAAS,EAAC;QACR,IAAI,cAAc,OAAO,EAAE;QAC3B,cAAc,OAAO,GAAG;QAExB,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,2JAAgB,CAAC,OAAO;gBAE9B,mCAAmC;gBACnC,MAAM,iBAAiB;oBAAC;oBAAY;oBAAY;oBAAY;oBAAY;oBAAY;iBAAY;gBAChG,eAAe,OAAO,CAAC,CAAA;oBACrB,2JAAgB,CAAC,iBAAiB,CAAC;gBACrC;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,oBAAoB;YACtB;QACF;QAEA;QAEA,8BAA8B;QAC9B,OAAO;YACL,2JAAgB,CAAC,UAAU;QAC7B;IACF,GAAG;QAAC;KAAoB;IAExB,OAAO;QACL,aAAa,2JAAgB,CAAC,WAAW;QACzC,iBAAiB,2JAAgB,CAAC,kBAAkB;QACpD,mBAAmB,CAAC,SAAmB,2JAAgB,CAAC,iBAAiB,CAAC;QAC1E,uBAAuB,CAAC,SAAmB,2JAAgB,CAAC,qBAAqB,CAAC;QAClF,mBAAmB,CAAC,SAAmB,2JAAgB,CAAC,iBAAiB,CAAC;IAC5E;AACF;AAEO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAY;IAElB,IAAA,kNAAS,EAAC;QACR,IAAI,UAAU,WAAW,IAAI,QAAQ;YACnC,UAAU,iBAAiB,CAAC;YAE5B,OAAO;gBACL,UAAU,qBAAqB,CAAC;YAClC;QACF;IACF,GAAG;QAAC;QAAQ;KAAU;IAEtB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/Header.tsx"], "sourcesContent": ["import { useMarketStore } from '@/store/trading-store';\nimport { Wifi, WifiOff } from 'lucide-react';\n\nexport default function Header() {\n  const { isConnected } = useMarketStore();\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          <div className=\"flex items-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              BingX Trading\n            </h1>\n            <div className=\"ml-4 flex items-center\">\n              {isConnected ? (\n                <div className=\"flex items-center text-green-600\">\n                  <Wifi className=\"w-4 h-4 mr-1\" />\n                  <span className=\"text-sm font-medium\">Connecté</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center text-red-600\">\n                  <WifiOff className=\"w-4 h-4 mr-1\" />\n                  <span className=\"text-sm font-medium\">Déconnecté</span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"text-sm text-gray-500\">\n              {new Date().toLocaleString('fr-FR', {\n                weekday: 'short',\n                year: 'numeric',\n                month: 'short',\n                day: 'numeric',\n                hour: '2-digit',\n                minute: '2-digit',\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;AAGe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,IAAA,kJAAc;IAEtC,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAI,WAAU;0CACZ,4BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;yDAGxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAQ,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,IAAI,OAAO,cAAc,CAAC,SAAS;gCAClC,SAAS;gCACT,MAAM;gCACN,OAAO;gCACP,KAAK;gCACL,MAAM;gCACN,QAAQ;4BACV;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/AccountSummary.tsx"], "sourcesContent": ["import { useTradingStore } from '@/store/trading-store';\nimport { DollarSign, TrendingUp, TrendingDown, Wallet } from 'lucide-react';\n\nexport default function AccountSummary() {\n  const { account } = useTradingStore();\n\n  if (!account) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {[...Array(4)].map((_, i) => (\n          <div key={i} className=\"bg-white rounded-lg shadow p-6 animate-pulse\">\n            <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n            <div className=\"h-8 bg-gray-200 rounded w-1/2\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  const totalBalance = parseFloat(account.totalWalletBalance || '0');\n  const unrealizedPnl = parseFloat(account.totalUnrealizedProfit || '0');\n  const availableBalance = parseFloat(account.availableBalance || '0');\n  const marginBalance = parseFloat(account.totalMarginBalance || '0');\n\n  const formatCurrency = (value: number) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n\n  const formatPercentage = (value: number, total: number) => {\n    if (total === 0) return '0.00%';\n    return `${((value / total) * 100).toFixed(2)}%`;\n  };\n\n  const cards = [\n    {\n      title: 'Solde Total',\n      value: formatCurrency(totalBalance),\n      icon: Wallet,\n      color: 'blue',\n      subtitle: 'Portefeuille complet',\n    },\n    {\n      title: 'P&L Non Réalisé',\n      value: formatCurrency(unrealizedPnl),\n      icon: unrealizedPnl >= 0 ? TrendingUp : TrendingDown,\n      color: unrealizedPnl >= 0 ? 'green' : 'red',\n      subtitle: formatPercentage(unrealizedPnl, totalBalance),\n    },\n    {\n      title: 'Solde Disponible',\n      value: formatCurrency(availableBalance),\n      icon: DollarSign,\n      color: 'emerald',\n      subtitle: 'Libre pour trading',\n    },\n    {\n      title: 'Marge Utilisée',\n      value: formatCurrency(marginBalance - availableBalance),\n      icon: DollarSign,\n      color: 'orange',\n      subtitle: formatPercentage(marginBalance - availableBalance, totalBalance),\n    },\n  ];\n\n  const colorClasses = {\n    blue: {\n      bg: 'bg-blue-50',\n      icon: 'text-blue-600',\n      text: 'text-blue-900',\n    },\n    green: {\n      bg: 'bg-green-50',\n      icon: 'text-green-600',\n      text: 'text-green-900',\n    },\n    red: {\n      bg: 'bg-red-50',\n      icon: 'text-red-600',\n      text: 'text-red-900',\n    },\n    emerald: {\n      bg: 'bg-emerald-50',\n      icon: 'text-emerald-600',\n      text: 'text-emerald-900',\n    },\n    orange: {\n      bg: 'bg-orange-50',\n      icon: 'text-orange-600',\n      text: 'text-orange-900',\n    },\n  };\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {cards.map((card, index) => {\n        const colors = colorClasses[card.color as keyof typeof colorClasses];\n        const Icon = card.icon;\n        \n        return (\n          <div key={index} className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className={`${colors.bg} rounded-md p-3`}>\n                <Icon className={`w-6 h-6 ${colors.icon}`} />\n              </div>\n              <div className=\"ml-4 flex-1\">\n                <p className=\"text-sm font-medium text-gray-500\">\n                  {card.title}\n                </p>\n                <p className={`text-2xl font-semibold ${colors.text}`}>\n                  {card.value}\n                </p>\n                <p className=\"text-xs text-gray-400 mt-1\">\n                  {card.subtitle}\n                </p>\n              </div>\n            </div>\n          </div>\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;AAGe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,mJAAe;IAEnC,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;mBAFP;;;;;;;;;;IAOlB;IAEA,MAAM,eAAe,WAAW,QAAQ,kBAAkB,IAAI;IAC9D,MAAM,gBAAgB,WAAW,QAAQ,qBAAqB,IAAI;IAClE,MAAM,mBAAmB,WAAW,QAAQ,gBAAgB,IAAI;IAChE,MAAM,gBAAgB,WAAW,QAAQ,kBAAkB,IAAI;IAE/D,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,mBAAmB,CAAC,OAAe;QACvC,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,GAAG,CAAC,AAAC,QAAQ,QAAS,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACjD;IAEA,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,eAAe;YACtB,MAAM;YACN,OAAO;YACP,UAAU;QACZ;QACA;YACE,OAAO;YACP,OAAO,eAAe;YACtB,MAAM,iBAAiB,IAAI,aAAa;YACxC,OAAO,iBAAiB,IAAI,UAAU;YACtC,UAAU,iBAAiB,eAAe;QAC5C;QACA;YACE,OAAO;YACP,OAAO,eAAe;YACtB,MAAM;YACN,OAAO;YACP,UAAU;QACZ;QACA;YACE,OAAO;YACP,OAAO,eAAe,gBAAgB;YACtC,MAAM;YACN,OAAO;YACP,UAAU,iBAAiB,gBAAgB,kBAAkB;QAC/D;KACD;IAED,MAAM,eAAe;QACnB,MAAM;YACJ,IAAI;YACJ,MAAM;YACN,MAAM;QACR;QACA,OAAO;YACL,IAAI;YACJ,MAAM;YACN,MAAM;QACR;QACA,KAAK;YACH,IAAI;YACJ,MAAM;YACN,MAAM;QACR;QACA,SAAS;YACP,IAAI;YACJ,MAAM;YACN,MAAM;QACR;QACA,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,SAAS,YAAY,CAAC,KAAK,KAAK,CAA8B;YACpE,MAAM,OAAO,KAAK,IAAI;YAEtB,qBACE,8OAAC;gBAAgB,WAAU;0BACzB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAW,GAAG,OAAO,EAAE,CAAC,eAAe,CAAC;sCAC3C,cAAA,8OAAC;gCAAK,WAAW,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;;;;;;;;;;;sCAE3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAW,CAAC,uBAAuB,EAAE,OAAO,IAAI,EAAE;8CAClD,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,KAAK,QAAQ;;;;;;;;;;;;;;;;;;eAbZ;;;;;QAmBd;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/PositionsList.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { useTradingStore } from '@/store/trading-store';\nimport { Position } from '@/types/bingx';\nimport { TrendingUp, TrendingDown, X } from 'lucide-react';\nimport BingXApiService from '@/services/bingx-api';\n\nexport default function PositionsList() {\n  const { positions, isLoading, refreshAllData } = useTradingStore();\n  const [closingPosition, setClosingPosition] = useState<string | null>(null);\n\n  const formatCurrency = (value: string | number) => {\n    const num = typeof value === 'string' ? parseFloat(value) : value;\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(num);\n  };\n\n  const formatPercentage = (pnl: string, initialMargin: string) => {\n    const pnlNum = parseFloat(pnl);\n    const marginNum = parseFloat(initialMargin);\n    if (marginNum === 0) return '0.00%';\n    return `${((pnlNum / marginNum) * 100).toFixed(2)}%`;\n  };\n\n  const handleClosePosition = async (position: Position) => {\n    if (closingPosition) return;\n    \n    try {\n      setClosingPosition(position.positionId);\n      await BingXApiService.closePosition(position.symbol, position.positionSide);\n      await refreshAllData();\n    } catch (error) {\n      console.error('Erreur lors de la fermeture de la position:', error);\n    } finally {\n      setClosingPosition(null);\n    }\n  };\n\n  if (isLoading && positions.length === 0) {\n    return (\n      <div className=\"space-y-4\">\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-20 bg-gray-200 rounded-lg\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (positions.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n          <TrendingUp className=\"w-12 h-12 text-gray-400\" />\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n          Aucune position ouverte\n        </h3>\n        <p className=\"text-gray-500\">\n          Vos positions actives apparaîtront ici une fois que vous aurez commencé à trader.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          Positions Ouvertes ({positions.length})\n        </h3>\n      </div>\n\n      <div className=\"space-y-3\">\n        {positions.map((position) => {\n          const pnl = parseFloat(position.unrealizedProfit);\n          const isProfit = pnl >= 0;\n          const positionSize = parseFloat(position.positionAmt);\n          const isLong = positionSize > 0;\n\n          return (\n            <div\n              key={position.positionId}\n              className=\"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"font-semibold text-gray-900\">\n                      {position.symbol}\n                    </span>\n                    <span\n                      className={`px-2 py-1 rounded-full text-xs font-medium ${\n                        isLong\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-red-100 text-red-800'\n                      }`}\n                    >\n                      {isLong ? 'LONG' : 'SHORT'}\n                    </span>\n                    <span className=\"text-sm text-gray-500\">\n                      {position.leverage}x\n                    </span>\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => handleClosePosition(position)}\n                  disabled={closingPosition === position.positionId}\n                  className=\"inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50\"\n                >\n                  {closingPosition === position.positionId ? (\n                    <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-red-300 border-t-red-600\" />\n                  ) : (\n                    <X className=\"w-4 h-4\" />\n                  )}\n                  <span className=\"ml-1\">Fermer</span>\n                </button>\n              </div>\n\n              <div className=\"mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                <div>\n                  <p className=\"text-gray-500\">Taille</p>\n                  <p className=\"font-medium\">\n                    {Math.abs(positionSize).toFixed(4)}\n                  </p>\n                </div>\n                <div>\n                  <p className=\"text-gray-500\">Prix d'entrée</p>\n                  <p className=\"font-medium\">\n                    {formatCurrency(position.entryPrice)}\n                  </p>\n                </div>\n                <div>\n                  <p className=\"text-gray-500\">Marge</p>\n                  <p className=\"font-medium\">\n                    {formatCurrency(position.initialMargin)}\n                  </p>\n                </div>\n                <div>\n                  <p className=\"text-gray-500\">P&L</p>\n                  <div className=\"flex items-center space-x-1\">\n                    {isProfit ? (\n                      <TrendingUp className=\"w-4 h-4 text-green-600\" />\n                    ) : (\n                      <TrendingDown className=\"w-4 h-4 text-red-600\" />\n                    )}\n                    <span\n                      className={`font-medium ${\n                        isProfit ? 'text-green-600' : 'text-red-600'\n                      }`}\n                    >\n                      {formatCurrency(pnl)}\n                    </span>\n                    <span\n                      className={`text-xs ${\n                        isProfit ? 'text-green-600' : 'text-red-600'\n                      }`}\n                    >\n                      ({formatPercentage(position.unrealizedProfit, position.initialMargin)})\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;;;AAGA;;;;;;AAEe,SAAS;IACtB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAA,mJAAe;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAgB;IAEtE,MAAM,iBAAiB,CAAC;QACtB,MAAM,MAAM,OAAO,UAAU,WAAW,WAAW,SAAS;QAC5D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,mBAAmB,CAAC,KAAa;QACrC,MAAM,SAAS,WAAW;QAC1B,MAAM,YAAY,WAAW;QAC7B,IAAI,cAAc,GAAG,OAAO;QAC5B,OAAO,GAAG,CAAC,AAAC,SAAS,YAAa,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACtD;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,iBAAiB;QAErB,IAAI;YACF,mBAAmB,SAAS,UAAU;YACtC,MAAM,0IAAe,CAAC,aAAa,CAAC,SAAS,MAAM,EAAE,SAAS,YAAY;YAC1E,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;QAC/D,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,IAAI,aAAa,UAAU,MAAM,KAAK,GAAG;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAW,WAAU;;;;;;;;;;;8BAExB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAoC;wBAC3B,UAAU,MAAM;wBAAC;;;;;;;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;oBACd,MAAM,MAAM,WAAW,SAAS,gBAAgB;oBAChD,MAAM,WAAW,OAAO;oBACxB,MAAM,eAAe,WAAW,SAAS,WAAW;oBACpD,MAAM,SAAS,eAAe;oBAE9B,qBACE,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM;;;;;;8DAElB,8OAAC;oDACC,WAAW,CAAC,2CAA2C,EACrD,SACI,gCACA,2BACJ;8DAED,SAAS,SAAS;;;;;;8DAErB,8OAAC;oDAAK,WAAU;;wDACb,SAAS,QAAQ;wDAAC;;;;;;;;;;;;;;;;;;kDAKzB,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,UAAU,oBAAoB,SAAS,UAAU;wCACjD,WAAU;;4CAET,oBAAoB,SAAS,UAAU,iBACtC,8OAAC;gDAAI,WAAU;;;;;qEAEf,8OAAC;gDAAE,WAAU;;;;;;0DAEf,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;0CAI3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DACV,KAAK,GAAG,CAAC,cAAc,OAAO,CAAC;;;;;;;;;;;;kDAGpC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DACV,eAAe,SAAS,UAAU;;;;;;;;;;;;kDAGvC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DACV,eAAe,SAAS,aAAa;;;;;;;;;;;;kDAG1C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;;oDACZ,yBACC,8OAAC;wDAAW,WAAU;;;;;6EAEtB,8OAAC;wDAAa,WAAU;;;;;;kEAE1B,8OAAC;wDACC,WAAW,CAAC,YAAY,EACtB,WAAW,mBAAmB,gBAC9B;kEAED,eAAe;;;;;;kEAElB,8OAAC;wDACC,WAAW,CAAC,QAAQ,EAClB,WAAW,mBAAmB,gBAC9B;;4DACH;4DACG,iBAAiB,SAAS,gBAAgB,EAAE,SAAS,aAAa;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;uBA7EzE,SAAS,UAAU;;;;;gBAoF9B;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/OrdersList.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { useTradingStore } from '@/store/trading-store';\nimport { Order } from '@/types/bingx';\nimport { Clock, X, CheckCircle } from 'lucide-react';\n\nexport default function OrdersList() {\n  const { orders, isLoading, cancelOrder } = useTradingStore();\n  const [cancellingOrder, setCancellingOrder] = useState<string | null>(null);\n\n  const formatCurrency = (value: string | number) => {\n    const num = typeof value === 'string' ? parseFloat(value) : value;\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(num);\n  };\n\n  const formatDate = (timestamp: number) => {\n    return new Date(timestamp).toLocaleString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const handleCancelOrder = async (order: Order) => {\n    if (cancellingOrder) return;\n    \n    try {\n      setCancellingOrder(order.orderId);\n      await cancelOrder(order.orderId, order.symbol);\n    } catch (error) {\n      console.error('Erreur lors de l\\'annulation de l\\'ordre:', error);\n    } finally {\n      setCancellingOrder(null);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'NEW':\n        return 'bg-blue-100 text-blue-800';\n      case 'PARTIALLY_FILLED':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'FILLED':\n        return 'bg-green-100 text-green-800';\n      case 'CANCELED':\n        return 'bg-gray-100 text-gray-800';\n      case 'REJECTED':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusLabel = (status: string) => {\n    switch (status) {\n      case 'NEW':\n        return 'Nouveau';\n      case 'PARTIALLY_FILLED':\n        return 'Partiellement exécuté';\n      case 'FILLED':\n        return 'Exécuté';\n      case 'CANCELED':\n        return 'Annulé';\n      case 'REJECTED':\n        return 'Rejeté';\n      default:\n        return status;\n    }\n  };\n\n  if (isLoading && orders.length === 0) {\n    return (\n      <div className=\"space-y-4\">\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-20 bg-gray-200 rounded-lg\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (orders.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n          <Clock className=\"w-12 h-12 text-gray-400\" />\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n          Aucun ordre en cours\n        </h3>\n        <p className=\"text-gray-500\">\n          Vos ordres actifs apparaîtront ici une fois que vous en aurez créé.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          Ordres Actifs ({orders.length})\n        </h3>\n      </div>\n\n      <div className=\"space-y-3\">\n        {orders.map((order) => {\n          const executedQty = parseFloat(order.executedQty);\n          const origQty = parseFloat(order.origQty);\n          const fillPercentage = origQty > 0 ? (executedQty / origQty) * 100 : 0;\n          const canCancel = ['NEW', 'PARTIALLY_FILLED'].includes(order.status);\n\n          return (\n            <div\n              key={order.orderId}\n              className=\"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"font-semibold text-gray-900\">\n                      {order.symbol}\n                    </span>\n                    <span\n                      className={`px-2 py-1 rounded-full text-xs font-medium ${\n                        order.side === 'BUY'\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-red-100 text-red-800'\n                      }`}\n                    >\n                      {order.side}\n                    </span>\n                    <span\n                      className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}\n                    >\n                      {getStatusLabel(order.status)}\n                    </span>\n                  </div>\n                </div>\n\n                {canCancel && (\n                  <button\n                    onClick={() => handleCancelOrder(order)}\n                    disabled={cancellingOrder === order.orderId}\n                    className=\"inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50\"\n                  >\n                    {cancellingOrder === order.orderId ? (\n                      <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-red-300 border-t-red-600\" />\n                    ) : (\n                      <X className=\"w-4 h-4\" />\n                    )}\n                    <span className=\"ml-1\">Annuler</span>\n                  </button>\n                )}\n              </div>\n\n              <div className=\"mt-3 grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\">\n                <div>\n                  <p className=\"text-gray-500\">Type</p>\n                  <p className=\"font-medium\">{order.type}</p>\n                </div>\n                <div>\n                  <p className=\"text-gray-500\">Quantité</p>\n                  <p className=\"font-medium\">\n                    {executedQty.toFixed(4)} / {origQty.toFixed(4)}\n                  </p>\n                  {fillPercentage > 0 && (\n                    <div className=\"w-full bg-gray-200 rounded-full h-1 mt-1\">\n                      <div\n                        className=\"bg-blue-600 h-1 rounded-full\"\n                        style={{ width: `${fillPercentage}%` }}\n                      />\n                    </div>\n                  )}\n                </div>\n                <div>\n                  <p className=\"text-gray-500\">Prix</p>\n                  <p className=\"font-medium\">\n                    {order.price === '0' ? 'Marché' : formatCurrency(order.price)}\n                  </p>\n                </div>\n                <div>\n                  <p className=\"text-gray-500\">Prix moyen</p>\n                  <p className=\"font-medium\">\n                    {order.avgPrice === '0' ? '-' : formatCurrency(order.avgPrice)}\n                  </p>\n                </div>\n                <div>\n                  <p className=\"text-gray-500\">Créé le</p>\n                  <p className=\"font-medium\">\n                    {formatDate(order.time)}\n                  </p>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;;;;;;;AAIe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAA,mJAAe;IAC1D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAgB;IAEtE,MAAM,iBAAiB,CAAC;QACtB,MAAM,MAAM,OAAO,UAAU,WAAW,WAAW,SAAS;QAC5D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,cAAc,CAAC,SAAS;YACjD,KAAK;YACL,OAAO;YACP,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,iBAAiB;QAErB,IAAI;YACF,mBAAmB,MAAM,OAAO;YAChC,MAAM,YAAY,MAAM,OAAO,EAAE,MAAM,MAAM;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;QAC7D,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,aAAa,OAAO,MAAM,KAAK,GAAG;QACpC,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;;;;;;;;;;8BAEnB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAoC;wBAChC,OAAO,MAAM;wBAAC;;;;;;;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC;oBACX,MAAM,cAAc,WAAW,MAAM,WAAW;oBAChD,MAAM,UAAU,WAAW,MAAM,OAAO;oBACxC,MAAM,iBAAiB,UAAU,IAAI,AAAC,cAAc,UAAW,MAAM;oBACrE,MAAM,YAAY;wBAAC;wBAAO;qBAAmB,CAAC,QAAQ,CAAC,MAAM,MAAM;oBAEnE,qBACE,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,MAAM,MAAM;;;;;;8DAEf,8OAAC;oDACC,WAAW,CAAC,2CAA2C,EACrD,MAAM,IAAI,KAAK,QACX,gCACA,2BACJ;8DAED,MAAM,IAAI;;;;;;8DAEb,8OAAC;oDACC,WAAW,CAAC,2CAA2C,EAAE,eAAe,MAAM,MAAM,GAAG;8DAEtF,eAAe,MAAM,MAAM;;;;;;;;;;;;;;;;;oCAKjC,2BACC,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU,oBAAoB,MAAM,OAAO;wCAC3C,WAAU;;4CAET,oBAAoB,MAAM,OAAO,iBAChC,8OAAC;gDAAI,WAAU;;;;;qEAEf,8OAAC;gDAAE,WAAU;;;;;;0DAEf,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAe,MAAM,IAAI;;;;;;;;;;;;kDAExC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;;oDACV,YAAY,OAAO,CAAC;oDAAG;oDAAI,QAAQ,OAAO,CAAC;;;;;;;4CAE7C,iBAAiB,mBAChB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;;kDAK7C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DACV,MAAM,KAAK,KAAK,MAAM,WAAW,eAAe,MAAM,KAAK;;;;;;;;;;;;kDAGhE,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DACV,MAAM,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,QAAQ;;;;;;;;;;;;kDAGjE,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,IAAI;;;;;;;;;;;;;;;;;;;uBA5EvB,MAAM,OAAO;;;;;gBAkFxB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 1868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/TradingForm.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { useTradingStore, useMarketStore } from '@/store/trading-store';\nimport { OrderSide, OrderType } from '@/types/bingx';\nimport { TrendingUp, TrendingDown } from 'lucide-react';\n\nexport default function TradingForm() {\n  const { createOrder, selectedSymbol, setSelectedSymbol, account } = useTradingStore();\n  const { tickers } = useMarketStore();\n  \n  const [orderData, setOrderData] = useState({\n    side: 'BUY' as OrderSide,\n    type: 'MARKET' as OrderType,\n    quantity: '',\n    price: '',\n    stopPrice: '',\n  });\n  \n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const currentTicker = tickers[selectedSymbol];\n  const currentPrice = currentTicker ? parseFloat(currentTicker.lastPrice) : 0;\n  const availableBalance = account ? parseFloat(account.availableBalance) : 0;\n\n  const symbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'SOL-USDT', 'DOGE-USDT'];\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (isSubmitting) return;\n\n    try {\n      setIsSubmitting(true);\n      \n      const order = {\n        symbol: selectedSymbol,\n        side: orderData.side,\n        type: orderData.type,\n        quantity: orderData.quantity,\n        ...(orderData.type === 'LIMIT' && { price: orderData.price }),\n        ...(orderData.type === 'STOP_MARKET' && { stopPrice: orderData.stopPrice }),\n        ...(orderData.type === 'TAKE_PROFIT_MARKET' && { stopPrice: orderData.stopPrice }),\n      };\n\n      await createOrder(order);\n      \n      // Réinitialiser le formulaire\n      setOrderData({\n        side: 'BUY',\n        type: 'MARKET',\n        quantity: '',\n        price: '',\n        stopPrice: '',\n      });\n      \n    } catch (error) {\n      console.error('Erreur lors de la création de l\\'ordre:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const calculateOrderValue = () => {\n    const qty = parseFloat(orderData.quantity) || 0;\n    const price = orderData.type === 'MARKET' ? currentPrice : parseFloat(orderData.price) || 0;\n    return qty * price;\n  };\n\n  const formatCurrency = (value: number) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-6\">\n          Passer un ordre\n        </h3>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Sélection du symbole */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Paire de trading\n            </label>\n            <select\n              value={selectedSymbol}\n              onChange={(e) => setSelectedSymbol(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            >\n              {symbols.map((symbol) => (\n                <option key={symbol} value={symbol}>\n                  {symbol} {tickers[symbol] && `- ${formatCurrency(parseFloat(tickers[symbol].lastPrice))}`}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Type d'ordre et côté */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Côté\n              </label>\n              <div className=\"flex rounded-md shadow-sm\">\n                <button\n                  type=\"button\"\n                  onClick={() => setOrderData({ ...orderData, side: 'BUY' })}\n                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-l-md border ${\n                    orderData.side === 'BUY'\n                      ? 'bg-green-50 border-green-500 text-green-700'\n                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <TrendingUp className=\"w-4 h-4 inline mr-1\" />\n                  Acheter\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => setOrderData({ ...orderData, side: 'SELL' })}\n                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${\n                    orderData.side === 'SELL'\n                      ? 'bg-red-50 border-red-500 text-red-700'\n                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <TrendingDown className=\"w-4 h-4 inline mr-1\" />\n                  Vendre\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Type d'ordre\n              </label>\n              <select\n                value={orderData.type}\n                onChange={(e) => setOrderData({ ...orderData, type: e.target.value as OrderType })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"MARKET\">Marché</option>\n                <option value=\"LIMIT\">Limite</option>\n                <option value=\"STOP_MARKET\">Stop Market</option>\n                <option value=\"TAKE_PROFIT_MARKET\">Take Profit Market</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Quantité */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Quantité\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.0001\"\n              value={orderData.quantity}\n              onChange={(e) => setOrderData({ ...orderData, quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"0.0000\"\n              required\n            />\n          </div>\n\n          {/* Prix (pour les ordres limite) */}\n          {orderData.type === 'LIMIT' && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Prix limite\n              </label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                value={orderData.price}\n                onChange={(e) => setOrderData({ ...orderData, price: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder={currentPrice.toString()}\n                required\n              />\n            </div>\n          )}\n\n          {/* Prix stop (pour les ordres stop) */}\n          {(orderData.type === 'STOP_MARKET' || orderData.type === 'TAKE_PROFIT_MARKET') && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Prix de déclenchement\n              </label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                value={orderData.stopPrice}\n                onChange={(e) => setOrderData({ ...orderData, stopPrice: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder={currentPrice.toString()}\n                required\n              />\n            </div>\n          )}\n\n          {/* Résumé de l'ordre */}\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h4 className=\"text-sm font-medium text-gray-900 mb-2\">\n              Résumé de l'ordre\n            </h4>\n            <div className=\"space-y-1 text-sm\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-500\">Valeur estimée:</span>\n                <span className=\"font-medium\">\n                  {formatCurrency(calculateOrderValue())}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-500\">Solde disponible:</span>\n                <span className=\"font-medium\">\n                  {formatCurrency(availableBalance)}\n                </span>\n              </div>\n              {currentTicker && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-500\">Prix actuel:</span>\n                  <span className=\"font-medium\">\n                    {formatCurrency(currentPrice)}\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Bouton de soumission */}\n          <button\n            type=\"submit\"\n            disabled={isSubmitting || !orderData.quantity}\n            className={`w-full px-4 py-3 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${\n              orderData.side === 'BUY'\n                ? 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'\n                : 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'\n            }`}\n          >\n            {isSubmitting ? (\n              <div className=\"flex items-center justify-center\">\n                <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent mr-2\" />\n                Création en cours...\n              </div>\n            ) : (\n              `${orderData.side === 'BUY' ? 'Acheter' : 'Vendre'} ${selectedSymbol}`\n            )}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;;;;;;;AAIe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,iBAAiB,EAAE,OAAO,EAAE,GAAG,IAAA,mJAAe;IACnF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,kJAAc;IAElC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QACzC,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW;IACb;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,MAAM,gBAAgB,OAAO,CAAC,eAAe;IAC7C,MAAM,eAAe,gBAAgB,WAAW,cAAc,SAAS,IAAI;IAC3E,MAAM,mBAAmB,UAAU,WAAW,QAAQ,gBAAgB,IAAI;IAE1E,MAAM,UAAU;QAAC;QAAY;QAAY;QAAY;QAAY;QAAY;KAAY;IAEzF,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,cAAc;QAElB,IAAI;YACF,gBAAgB;YAEhB,MAAM,QAAQ;gBACZ,QAAQ;gBACR,MAAM,UAAU,IAAI;gBACpB,MAAM,UAAU,IAAI;gBACpB,UAAU,UAAU,QAAQ;gBAC5B,GAAI,UAAU,IAAI,KAAK,WAAW;oBAAE,OAAO,UAAU,KAAK;gBAAC,CAAC;gBAC5D,GAAI,UAAU,IAAI,KAAK,iBAAiB;oBAAE,WAAW,UAAU,SAAS;gBAAC,CAAC;gBAC1E,GAAI,UAAU,IAAI,KAAK,wBAAwB;oBAAE,WAAW,UAAU,SAAS;gBAAC,CAAC;YACnF;YAEA,MAAM,YAAY;YAElB,8BAA8B;YAC9B,aAAa;gBACX,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,WAAW;YACb;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,MAAM,WAAW,UAAU,QAAQ,KAAK;QAC9C,MAAM,QAAQ,UAAU,IAAI,KAAK,WAAW,eAAe,WAAW,UAAU,KAAK,KAAK;QAC1F,OAAO,MAAM;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAIvD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAU;8CAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4CAAoB,OAAO;;gDACzB;gDAAO;gDAAE,OAAO,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,eAAe,WAAW,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI;;2CAD9E;;;;;;;;;;;;;;;;sCAQnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,MAAM;wDAAM;oDACxD,WAAW,CAAC,yDAAyD,EACnE,UAAU,IAAI,KAAK,QACf,gDACA,2DACJ;;sEAEF,8OAAC;4DAAW,WAAU;;;;;;wDAAwB;;;;;;;8DAGhD,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,MAAM;wDAAO;oDACzD,WAAW,CAAC,6EAA6E,EACvF,UAAU,IAAI,KAAK,SACf,0CACA,2DACJ;;sEAEF,8OAAC;4DAAa,WAAU;;;;;;wDAAwB;;;;;;;;;;;;;;;;;;;8CAMtD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,UAAU,IAAI;4CACrB,UAAU,CAAC,IAAM,aAAa;oDAAE,GAAG,SAAS;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAc;4CAChF,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,UAAU,QAAQ;oCACzB,UAAU,CAAC,IAAM,aAAa;4CAAE,GAAG,SAAS;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACvE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;wBAKX,UAAU,IAAI,KAAK,yBAClB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,UAAU,KAAK;oCACtB,UAAU,CAAC,IAAM,aAAa;4CAAE,GAAG,SAAS;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACpE,WAAU;oCACV,aAAa,aAAa,QAAQ;oCAClC,QAAQ;;;;;;;;;;;;wBAMb,CAAC,UAAU,IAAI,KAAK,iBAAiB,UAAU,IAAI,KAAK,oBAAoB,mBAC3E,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,UAAU,SAAS;oCAC1B,UAAU,CAAC,IAAM,aAAa;4CAAE,GAAG,SAAS;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACxE,WAAU;oCACV,aAAa,aAAa,QAAQ;oCAClC,QAAQ;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,eAAe;;;;;;;;;;;;sDAGpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,eAAe;;;;;;;;;;;;wCAGnB,+BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1B,8OAAC;4BACC,MAAK;4BACL,UAAU,gBAAgB,CAAC,UAAU,QAAQ;4BAC7C,WAAW,CAAC,oJAAoJ,EAC9J,UAAU,IAAI,KAAK,QACf,oEACA,6DACJ;sCAED,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;oCAAsF;;;;;;uCAIvG,GAAG,UAAU,IAAI,KAAK,QAAQ,YAAY,SAAS,CAAC,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAOpF", "debugId": null}}, {"offset": {"line": 2393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/MarketData.tsx"], "sourcesContent": ["import { useMarketStore } from '@/store/trading-store';\nimport { TrendingUp, TrendingDown } from 'lucide-react';\n\nexport default function MarketData() {\n  const { tickers } = useMarketStore();\n\n  const formatCurrency = (value: string | number) => {\n    const num = typeof value === 'string' ? parseFloat(value) : value;\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 6,\n    }).format(num);\n  };\n\n  const formatPercentage = (value: string | number) => {\n    const num = typeof value === 'string' ? parseFloat(value) : value;\n    return `${num >= 0 ? '+' : ''}${num.toFixed(2)}%`;\n  };\n\n  const popularSymbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'SOL-USDT', 'DOGE-USDT'];\n  const displayTickers = popularSymbols\n    .map(symbol => ({ symbol, data: tickers[symbol] }))\n    .filter(item => item.data);\n\n  if (displayTickers.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n          Données de marché\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {[...Array(6)].map((_, i) => (\n            <div key={i} className=\"animate-pulse\">\n              <div className=\"h-20 bg-gray-200 rounded-lg\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n        Données de marché\n      </h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {displayTickers.map(({ symbol, data }) => {\n          const priceChange = parseFloat(data.priceChangePercent);\n          const isPositive = priceChange >= 0;\n          \n          return (\n            <div\n              key={symbol}\n              className=\"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors\"\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <h4 className=\"font-semibold text-gray-900\">\n                  {symbol.replace('-', '/')}\n                </h4>\n                <div className=\"flex items-center\">\n                  {isPositive ? (\n                    <TrendingUp className=\"w-4 h-4 text-green-600\" />\n                  ) : (\n                    <TrendingDown className=\"w-4 h-4 text-red-600\" />\n                  )}\n                </div>\n              </div>\n              \n              <div className=\"space-y-1\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-500\">Prix</span>\n                  <span className=\"font-medium text-gray-900\">\n                    {formatCurrency(data.lastPrice)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-500\">24h</span>\n                  <span\n                    className={`font-medium ${\n                      isPositive ? 'text-green-600' : 'text-red-600'\n                    }`}\n                  >\n                    {formatPercentage(data.priceChangePercent)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-500\">Volume</span>\n                  <span className=\"text-sm font-medium text-gray-700\">\n                    {parseFloat(data.volume).toLocaleString('fr-FR', {\n                      maximumFractionDigits: 0,\n                    })}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-500\">Haut/Bas</span>\n                  <span className=\"text-sm font-medium text-gray-700\">\n                    {formatCurrency(data.highPrice)} / {formatCurrency(data.lowPrice)}\n                  </span>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;AAGe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,kJAAc;IAElC,MAAM,iBAAiB,CAAC;QACtB,MAAM,MAAM,OAAO,UAAU,WAAW,WAAW,SAAS;QAC5D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,MAAM,OAAO,UAAU,WAAW,WAAW,SAAS;QAC5D,OAAO,GAAG,OAAO,IAAI,MAAM,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC;IACnD;IAEA,MAAM,iBAAiB;QAAC;QAAY;QAAY;QAAY;QAAY;QAAY;KAAY;IAChG,MAAM,iBAAiB,eACpB,GAAG,CAAC,CAAA,SAAU,CAAC;YAAE;YAAQ,MAAM,OAAO,CAAC,OAAO;QAAC,CAAC,GAChD,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IAE3B,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;;;;;2BADP;;;;;;;;;;;;;;;;IAOpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAyC;;;;;;0BAIvD,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE;oBACnC,MAAM,cAAc,WAAW,KAAK,kBAAkB;oBACtD,MAAM,aAAa,eAAe;oBAElC,qBACE,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,OAAO,OAAO,CAAC,KAAK;;;;;;kDAEvB,8OAAC;wCAAI,WAAU;kDACZ,2BACC,8OAAC;4CAAW,WAAU;;;;;iEAEtB,8OAAC;4CAAa,WAAU;;;;;;;;;;;;;;;;;0CAK9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DACb,eAAe,KAAK,SAAS;;;;;;;;;;;;kDAIlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDACC,WAAW,CAAC,YAAY,EACtB,aAAa,mBAAmB,gBAChC;0DAED,iBAAiB,KAAK,kBAAkB;;;;;;;;;;;;kDAI7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DACb,WAAW,KAAK,MAAM,EAAE,cAAc,CAAC,SAAS;oDAC/C,uBAAuB;gDACzB;;;;;;;;;;;;kDAIJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;;oDACb,eAAe,KAAK,SAAS;oDAAE;oDAAI,eAAe,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;uBA/CjE;;;;;gBAqDX;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTradingStore } from '@/store/trading-store';\nimport Header from './Header';\nimport AccountSummary from './AccountSummary';\nimport PositionsList from './PositionsList';\nimport OrdersList from './OrdersList';\nimport TradingForm from './TradingForm';\nimport MarketData from './MarketData';\nimport { RefreshCw } from 'lucide-react';\n\nexport default function Dashboard() {\n  const { refreshAllData, isLoading } = useTradingStore();\n  const [activeTab, setActiveTab] = useState<'positions' | 'orders' | 'trading'>('positions');\n\n  const handleRefresh = async () => {\n    try {\n      await refreshAllData();\n    } catch (error) {\n      console.error('Erreur lors du rafraîchissement:', error);\n    }\n  };\n\n  const tabs = [\n    { id: 'positions', label: 'Positions', count: useTradingStore.getState().positions.length },\n    { id: 'orders', label: 'Ordres', count: useTradingStore.getState().orders.length },\n    { id: 'trading', label: 'Trading', count: null },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Résumé du compte */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">\n              Aperçu du compte\n            </h2>\n            <button\n              onClick={handleRefresh}\n              disabled={isLoading}\n              className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n            >\n              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />\n              Actualiser\n            </button>\n          </div>\n          <AccountSummary />\n        </div>\n\n        {/* Données de marché */}\n        <div className=\"mb-8\">\n          <MarketData />\n        </div>\n\n        {/* Onglets principaux */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex space-x-8\" aria-label=\"Tabs\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}\n                >\n                  {tab.label}\n                  {tab.count !== null && (\n                    <span\n                      className={`${\n                        activeTab === tab.id\n                          ? 'bg-blue-100 text-blue-600'\n                          : 'bg-gray-100 text-gray-900'\n                      } ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium`}\n                    >\n                      {tab.count}\n                    </span>\n                  )}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          <div className=\"p-6\">\n            {activeTab === 'positions' && <PositionsList />}\n            {activeTab === 'orders' && <OrdersList />}\n            {activeTab === 'trading' && <TradingForm />}\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AATA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,IAAA,mJAAe;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAqC;IAE/E,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAa,OAAO;YAAa,OAAO,mJAAe,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM;QAAC;QAC1F;YAAE,IAAI;YAAU,OAAO;YAAU,OAAO,mJAAe,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM;QAAC;QACjF;YAAE,IAAI;YAAW,OAAO;YAAW,OAAO;QAAK;KAChD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC;gDAAU,WAAW,CAAC,aAAa,EAAE,YAAY,iBAAiB,IAAI;;;;;;4CAAI;;;;;;;;;;;;;0CAI/E,8OAAC,+IAAc;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2IAAU;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAwB,cAAW;8CAC/C,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,GACT,cAAc,IAAI,EAAE,GAChB,kCACA,6EACL,6EAA6E,CAAC;;gDAE9E,IAAI,KAAK;gDACT,IAAI,KAAK,KAAK,sBACb,8OAAC;oDACC,WAAW,GACT,cAAc,IAAI,EAAE,GAChB,8BACA,4BACL,oDAAoD,CAAC;8DAErD,IAAI,KAAK;;;;;;;2CAjBT,IAAI,EAAE;;;;;;;;;;;;;;;0CAyBnB,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,6BAAe,8OAAC,8IAAa;;;;;oCAC3C,cAAc,0BAAY,8OAAC,2IAAU;;;;;oCACrC,cAAc,2BAAa,8OAAC,4IAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpD", "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/LoadingSpinner.tsx"], "sourcesContent": ["interface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n  };\n\n  return (\n    <div className={`flex items-center justify-center ${className}`}>\n      <div\n        className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-gray-300 border-t-blue-600`}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAKe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,8OAAC;YACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAI9G", "debugId": null}}, {"offset": {"line": 2914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/components/ErrorAlert.tsx"], "sourcesContent": ["import { X } from 'lucide-react';\nimport { useTradingStore } from '@/store/trading-store';\n\ninterface ErrorAlertProps {\n  message: string;\n}\n\nexport default function ErrorAlert({ message }: ErrorAlertProps) {\n  const { clearError } = useTradingStore();\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 max-w-md\">\n      <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg\">\n        <div className=\"flex items-start\">\n          <div className=\"flex-shrink-0\">\n            <div className=\"w-5 h-5 bg-red-100 rounded-full flex items-center justify-center\">\n              <div className=\"w-2 h-2 bg-red-600 rounded-full\" />\n            </div>\n          </div>\n          <div className=\"ml-3 flex-1\">\n            <h3 className=\"text-sm font-medium text-red-800\">\n              Erreur\n            </h3>\n            <p className=\"mt-1 text-sm text-red-700\">\n              {message}\n            </p>\n          </div>\n          <div className=\"ml-4 flex-shrink-0\">\n            <button\n              onClick={clearError}\n              className=\"inline-flex text-red-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-50 rounded-md\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;;;;AAMe,SAAS,WAAW,EAAE,OAAO,EAAmB;IAC7D,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,mJAAe;IAEtC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}, {"offset": {"line": 3026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useTradingStore, useMarketStore } from '@/store/trading-store';\nimport { useWebSocket } from '@/hooks/useWebSocket';\nimport Dashboard from '@/components/Dashboard';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorAlert from '@/components/ErrorAlert';\n\nexport default function Home() {\n  const { isLoading, error, refreshAllData } = useTradingStore();\n  const { fetchTickers } = useMarketStore();\n\n  // Initialiser les WebSockets\n  useWebSocket();\n\n  useEffect(() => {\n    // Charger les données initiales\n    const loadInitialData = async () => {\n      try {\n        await Promise.all([\n          refreshAllData(),\n          fetchTickers(),\n        ]);\n      } catch (error) {\n        console.error('Erreur lors du chargement initial:', error);\n      }\n    };\n\n    loadInitialData();\n\n    // Rafraîchir les données toutes les 30 secondes (moins fréquent car WebSocket fournit les prix en temps réel)\n    const interval = setInterval(() => {\n      refreshAllData();\n    }, 30000);\n\n    return () => clearInterval(interval);\n  }, [refreshAllData, fetchTickers]);\n\n  if (isLoading && !useTradingStore.getState().account) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {error && <ErrorAlert message={error} />}\n      <Dashboard />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,IAAA,mJAAe;IAC5D,MAAM,EAAE,YAAY,EAAE,GAAG,IAAA,kJAAc;IAEvC,6BAA6B;IAC7B,IAAA,4IAAY;IAEZ,IAAA,kNAAS,EAAC;QACR,gCAAgC;QAChC,MAAM,kBAAkB;YACtB,IAAI;gBACF,MAAM,QAAQ,GAAG,CAAC;oBAChB;oBACA;iBACD;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;QAEA;QAEA,8GAA8G;QAC9G,MAAM,WAAW,YAAY;YAC3B;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAgB;KAAa;IAEjC,IAAI,aAAa,CAAC,mJAAe,CAAC,QAAQ,GAAG,OAAO,EAAE;QACpD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,+IAAc;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBAAS,8OAAC,2IAAU;gBAAC,SAAS;;;;;;0BAC/B,8OAAC,0IAAS;;;;;;;;;;;AAGhB", "debugId": null}}]}