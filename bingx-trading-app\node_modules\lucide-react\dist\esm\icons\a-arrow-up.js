/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m14 11 4-4 4 4", key: "1pu57t" }],
  ["path", { d: "M18 16V7", key: "ty0viw" }],
  ["path", { d: "m2 16 4.039-9.69a.5.5 0 0 1 .923 0L11 16", key: "d5nyq2" }],
  ["path", { d: "M3.304 13h6.392", key: "1q3zxz" }]
];
const AArrowUp = createLucideIcon("a-arrow-up", __iconNode);

export { __iconNode, AArrowUp as default };
//# sourceMappingURL=a-arrow-up.js.map
