{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/app/api/bingx/market/ticker/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport axios from 'axios';\nimport { BingXResponse, Ticker24hr } from '@/types/bingx';\n\nconst BINGX_BASE_URL = process.env.BINGX_BASE_URL || 'https://open-api.bingx.com';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const symbol = searchParams.get('symbol');\n    \n    let endpoint: string;\n    let params: any = {};\n    \n    if (symbol) {\n      // Récupérer le ticker pour un symbole spécifique\n      endpoint = '/openApi/swap/v2/quote/ticker';\n      params.symbol = symbol;\n    } else {\n      // Récupérer tous les tickers\n      endpoint = '/openApi/swap/v2/quote/ticker';\n    }\n    \n    const url = new URL(endpoint, BINGX_BASE_URL);\n    if (symbol) {\n      url.searchParams.append('symbol', symbol);\n    }\n    \n    const response = await axios.get<BingXResponse<Ticker24hr[] | Ticker24hr>>(url.toString(), {\n      timeout: 10000,\n    });\n    \n    if (response.data.code !== 0) {\n      return NextResponse.json(\n        { error: response.data.msg },\n        { status: 400 }\n      );\n    }\n    \n    return NextResponse.json(response.data.data);\n    \n  } catch (error) {\n    console.error('Erreur lors de la récupération des tickers:', error);\n    \n    if (axios.isAxiosError(error)) {\n      const status = error.response?.status || 500;\n      const message = error.response?.data?.msg || error.message;\n      return NextResponse.json(\n        { error: `Erreur API BingX: ${message}` },\n        { status }\n      );\n    }\n    \n    return NextResponse.json(\n      { error: 'Erreur interne du serveur' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGA,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAE9C,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI;QACJ,IAAI,SAAc,CAAC;QAEnB,IAAI,QAAQ;YACV,iDAAiD;YACjD,WAAW;YACX,OAAO,MAAM,GAAG;QAClB,OAAO;YACL,6BAA6B;YAC7B,WAAW;QACb;QAEA,MAAM,MAAM,IAAI,IAAI,UAAU;QAC9B,IAAI,QAAQ;YACV,IAAI,YAAY,CAAC,MAAM,CAAC,UAAU;QACpC;QAEA,MAAM,WAAW,MAAM,kJAAK,CAAC,GAAG,CAA2C,IAAI,QAAQ,IAAI;YACzF,SAAS;QACX;QAEA,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,GAAG;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,SAAS,IAAI,CAAC,GAAG;YAAC,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI;IAE7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAE7D,IAAI,kJAAK,CAAC,YAAY,CAAC,QAAQ;YAC7B,MAAM,SAAS,MAAM,QAAQ,EAAE,UAAU;YACzC,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,OAAO,MAAM,OAAO;YAC1D,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,kBAAkB,EAAE,SAAS;YAAC,GACxC;gBAAE;YAAO;QAEb;QAEA,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}