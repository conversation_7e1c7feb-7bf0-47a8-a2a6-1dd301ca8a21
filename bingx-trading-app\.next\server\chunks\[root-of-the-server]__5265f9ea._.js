module.exports = [
"[project]/.next-internal/server/app/api/bingx/account/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/src/lib/bingx-auth.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "buildSignedUrl",
    ()=>buildSignedUrl,
    "createQueryString",
    ()=>createQueryString,
    "generateAuthHeaders",
    ()=>generateAuthHeaders,
    "generateSignature",
    ()=>generateSignature,
    "getBingXConfig",
    ()=>getBingXConfig,
    "validateBingXConfig",
    ()=>validateBingXConfig
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
function generateSignature(queryString, secretKey) {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHmac('sha256', secretKey).update(queryString).digest('hex');
}
function createQueryString(params) {
    const sortedParams = Object.keys(params).sort().reduce((result, key)=>{
        result[key] = params[key];
        return result;
    }, {});
    return new URLSearchParams(Object.entries(sortedParams).map(([key, value])=>[
            key,
            String(value)
        ])).toString();
}
function generateAuthHeaders(method, endpoint, params, config) {
    return {
        'X-BX-APIKEY': config.apiKey,
        'Content-Type': 'application/json',
        'User-Agent': 'BingX-Trading-App/1.0.0'
    };
}
function buildSignedUrl(endpoint, params, config) {
    const timestamp = Date.now();
    const paramsWithTimestamp = {
        ...params,
        timestamp
    };
    // Créer la query string pour la signature
    const queryString = createQueryString(paramsWithTimestamp);
    // Générer la signature avec la query string
    const signature = generateSignature(queryString, config.secretKey);
    // Ajouter la signature aux paramètres
    const finalParams = {
        ...paramsWithTimestamp,
        signature
    };
    const finalQueryString = createQueryString(finalParams);
    return `${config.baseUrl}${endpoint}?${finalQueryString}`;
}
function validateBingXConfig(config) {
    return !!(config.apiKey && config.secretKey && config.baseUrl && typeof config.apiKey === 'string' && typeof config.secretKey === 'string' && typeof config.baseUrl === 'string');
}
function getBingXConfig() {
    const config = {
        apiKey: process.env.BINGX_API_KEY,
        secretKey: process.env.BINGX_SECRET_KEY,
        baseUrl: process.env.BINGX_BASE_URL || 'https://open-api.bingx.com'
    };
    if (!validateBingXConfig(config)) {
        throw new Error('Configuration BingX invalide. Vérifiez vos variables d\'environnement.');
    }
    return config;
}
}),
"[project]/src/app/api/bingx/account/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/bingx-auth.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBingXConfig"])();
        // Endpoint pour récupérer les informations du compte futures
        const endpoint = '/openApi/swap/v2/user/balance';
        const params = {};
        const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildSignedUrl"])(endpoint, params, config);
        const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateAuthHeaders"])('POST', endpoint, params, config);
        console.log('Test URL:', url.substring(0, 100) + '...');
        console.log('Headers:', {
            ...headers,
            'X-BX-APIKEY': headers['X-BX-APIKEY'].substring(0, 8) + '...'
        });
        // BingX utilise POST pour les endpoints authentifiés
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(url, null, {
            headers,
            timeout: 10000
        });
        if (response.data.code !== 0) {
            console.log('Erreur BingX:', response.data);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: response.data.msg
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response.data.data);
    } catch (error) {
        console.error('Erreur lors de la récupération du compte:', error);
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
            const status = error.response?.status || 500;
            const message = error.response?.data?.msg || error.message;
            console.log('Détails erreur axios:', error.response?.data);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `Erreur API BingX: ${message}`
            }, {
                status
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Erreur interne du serveur'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__5265f9ea._.js.map