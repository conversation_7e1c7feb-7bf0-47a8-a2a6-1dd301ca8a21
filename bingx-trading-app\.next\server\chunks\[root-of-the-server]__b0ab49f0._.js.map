{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/lib/bingx-auth.ts"], "sourcesContent": ["import crypto from 'crypto';\n\nexport interface BingXConfig {\n  apiKey: string;\n  secretKey: string;\n  baseUrl: string;\n}\n\nexport interface BingXRequestParams {\n  [key: string]: string | number | boolean;\n}\n\n/**\n * <PERSON><PERSON><PERSON> la signature HMAC SHA256 requise par BingX\n */\nexport function generateSignature(\n  queryString: string,\n  secretKey: string\n): string {\n  return crypto\n    .createHmac('sha256', secretKey)\n    .update(queryString)\n    .digest('hex');\n}\n\n/**\n * Crée une query string à partir des paramètres\n */\nexport function createQueryString(params: BingXRequestParams): string {\n  const sortedParams = Object.keys(params)\n    .sort()\n    .reduce((result, key) => {\n      result[key] = params[key];\n      return result;\n    }, {} as BingXRequestParams);\n\n  return new URLSearchParams(\n    Object.entries(sortedParams).map(([key, value]) => [key, String(value)])\n  ).toString();\n}\n\n/**\n * G<PERSON>ère les headers d'authentification pour les requêtes BingX\n */\nexport function generateAuthHeaders(\n  method: string,\n  endpoint: string,\n  params: BingXRequestParams,\n  config: BingXConfig\n): Record<string, string> {\n  return {\n    'X-BX-APIKEY': config.apiKey,\n    'Content-Type': 'application/json',\n    'User-Agent': 'BingX-Trading-App/1.0.0',\n  };\n}\n\n/**\n * Construit l'URL complète avec les paramètres signés\n */\nexport function buildSignedUrl(\n  endpoint: string,\n  params: BingXRequestParams,\n  config: BingXConfig\n): string {\n  const timestamp = Date.now();\n  const paramsWithTimestamp = {\n    ...params,\n    timestamp,\n  };\n\n  // Créer la query string pour la signature\n  const queryString = createQueryString(paramsWithTimestamp);\n\n  // Générer la signature avec la query string\n  const signature = generateSignature(queryString, config.secretKey);\n\n  // Ajouter la signature aux paramètres\n  const finalParams = {\n    ...paramsWithTimestamp,\n    signature,\n  };\n\n  const finalQueryString = createQueryString(finalParams);\n  return `${config.baseUrl}${endpoint}?${finalQueryString}`;\n}\n\n/**\n * Valide la configuration BingX\n */\nexport function validateBingXConfig(config: Partial<BingXConfig>): config is BingXConfig {\n  return !!(\n    config.apiKey &&\n    config.secretKey &&\n    config.baseUrl &&\n    typeof config.apiKey === 'string' &&\n    typeof config.secretKey === 'string' &&\n    typeof config.baseUrl === 'string'\n  );\n}\n\n/**\n * Obtient la configuration BingX depuis les variables d'environnement\n */\nexport function getBingXConfig(): BingXConfig {\n  const config = {\n    apiKey: process.env.BINGX_API_KEY,\n    secretKey: process.env.BINGX_SECRET_KEY,\n    baseUrl: process.env.BINGX_BASE_URL || 'https://open-api.bingx.com',\n  };\n\n  if (!validateBingXConfig(config)) {\n    throw new Error('Configuration BingX invalide. Vérifiez vos variables d\\'environnement.');\n  }\n\n  return config;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAeO,SAAS,kBACd,WAAmB,EACnB,SAAiB;IAEjB,OAAO,gHAAM,CACV,UAAU,CAAC,UAAU,WACrB,MAAM,CAAC,aACP,MAAM,CAAC;AACZ;AAKO,SAAS,kBAAkB,MAA0B;IAC1D,MAAM,eAAe,OAAO,IAAI,CAAC,QAC9B,IAAI,GACJ,MAAM,CAAC,CAAC,QAAQ;QACf,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QACzB,OAAO;IACT,GAAG,CAAC;IAEN,OAAO,IAAI,gBACT,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK;YAAC;YAAK,OAAO;SAAO,GACvE,QAAQ;AACZ;AAKO,SAAS,oBACd,MAAc,EACd,QAAgB,EAChB,MAA0B,EAC1B,MAAmB;IAEnB,OAAO;QACL,eAAe,OAAO,MAAM;QAC5B,gBAAgB;QAChB,cAAc;IAChB;AACF;AAKO,SAAS,eACd,QAAgB,EAChB,MAA0B,EAC1B,MAAmB;IAEnB,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,sBAAsB;QAC1B,GAAG,MAAM;QACT;IACF;IAEA,0CAA0C;IAC1C,MAAM,cAAc,kBAAkB;IAEtC,4CAA4C;IAC5C,MAAM,YAAY,kBAAkB,aAAa,OAAO,SAAS;IAEjE,sCAAsC;IACtC,MAAM,cAAc;QAClB,GAAG,mBAAmB;QACtB;IACF;IAEA,MAAM,mBAAmB,kBAAkB;IAC3C,OAAO,GAAG,OAAO,OAAO,GAAG,SAAS,CAAC,EAAE,kBAAkB;AAC3D;AAKO,SAAS,oBAAoB,MAA4B;IAC9D,OAAO,CAAC,CAAC,CACP,OAAO,MAAM,IACb,OAAO,SAAS,IAChB,OAAO,OAAO,IACd,OAAO,OAAO,MAAM,KAAK,YACzB,OAAO,OAAO,SAAS,KAAK,YAC5B,OAAO,OAAO,OAAO,KAAK,QAC5B;AACF;AAKO,SAAS;IACd,MAAM,SAAS;QACb,QAAQ,QAAQ,GAAG,CAAC,aAAa;QACjC,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC;IAEA,IAAI,CAAC,oBAAoB,SAAS;QAChC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/app/api/bingx/positions/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport axios from 'axios';\nimport { getBingXConfig, buildSignedUrl, generateAuthHeaders } from '@/lib/bingx-auth';\nimport { BingXResponse, Position } from '@/types/bingx';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const config = getBingXConfig();\n    const { searchParams } = new URL(request.url);\n    const symbol = searchParams.get('symbol');\n    \n    // Endpoint pour récupérer les positions\n    const endpoint = '/openApi/swap/v2/user/positions';\n    const params = symbol ? { symbol } : {};\n    \n    const url = buildSignedUrl(endpoint, params, config);\n    const headers = generateAuthHeaders('GET', endpoint, params, config);\n    \n    const response = await axios.get<BingXResponse<Position[]>>(url, {\n      headers,\n      timeout: 10000,\n    });\n    \n    if (response.data.code !== 0) {\n      return NextResponse.json(\n        { error: response.data.msg },\n        { status: 400 }\n      );\n    }\n    \n    // Filtrer les positions avec une quantité > 0\n    const activePositions = response.data.data.filter(\n      position => parseFloat(position.positionAmt) !== 0\n    );\n    \n    return NextResponse.json(activePositions);\n    \n  } catch (error) {\n    console.error('Erreur lors de la récupération des positions:', error);\n    \n    if (axios.isAxiosError(error)) {\n      const status = error.response?.status || 500;\n      const message = error.response?.data?.msg || error.message;\n      return NextResponse.json(\n        { error: `Erreur API BingX: ${message}` },\n        { status }\n      );\n    }\n    \n    return NextResponse.json(\n      { error: 'Erreur interne du serveur' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,SAAS,IAAA,+IAAc;QAC7B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,wCAAwC;QACxC,MAAM,WAAW;QACjB,MAAM,SAAS,SAAS;YAAE;QAAO,IAAI,CAAC;QAEtC,MAAM,MAAM,IAAA,+IAAc,EAAC,UAAU,QAAQ;QAC7C,MAAM,UAAU,IAAA,oJAAmB,EAAC,OAAO,UAAU,QAAQ;QAE7D,MAAM,WAAW,MAAM,kJAAK,CAAC,GAAG,CAA4B,KAAK;YAC/D;YACA,SAAS;QACX;QAEA,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,GAAG;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,SAAS,IAAI,CAAC,GAAG;YAAC,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,8CAA8C;QAC9C,MAAM,kBAAkB,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAC/C,CAAA,WAAY,WAAW,SAAS,WAAW,MAAM;QAGnD,OAAO,gJAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAE/D,IAAI,kJAAK,CAAC,YAAY,CAAC,QAAQ;YAC7B,MAAM,SAAS,MAAM,QAAQ,EAAE,UAAU;YACzC,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,OAAO,MAAM,OAAO;YAC1D,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,kBAAkB,EAAE,SAAS;YAAC,GACxC;gBAAE;YAAO;QAEb;QAEA,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}