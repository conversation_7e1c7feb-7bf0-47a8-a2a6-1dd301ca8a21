import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { getBingXConfig, buildSignedUrl, generateAuthHeaders } from '@/lib/bingx-auth';
import { BingXResponse, AccountInfo } from '@/types/bingx';

export async function GET(request: NextRequest) {
  try {
    const config = getBingXConfig();

    // Endpoint pour récupérer les informations du compte futures
    const endpoint = '/openApi/swap/v2/user/balance';
    const params = {};

    const url = buildSignedUrl(endpoint, params, config);
    const headers = generateAuthHeaders('POST', endpoint, params, config);

    console.log('Test URL:', url.substring(0, 100) + '...');
    console.log('Headers:', { ...headers, 'X-BX-APIKEY': headers['X-BX-APIKEY'].substring(0, 8) + '...' });

    // BingX utilise POST pour les endpoints authentifiés
    const response = await axios.post<BingXResponse<AccountInfo>>(url, null, {
      headers,
      timeout: 10000,
    });

    if (response.data.code !== 0) {
      console.log('Erreur BingX:', response.data);
      return NextResponse.json(
        { error: response.data.msg },
        { status: 400 }
      );
    }

    return NextResponse.json(response.data.data);

  } catch (error) {
    console.error('Erreur lors de la récupération du compte:', error);

    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.msg || error.message;
      console.log('Détails erreur axios:', error.response?.data);
      return NextResponse.json(
        { error: `Erreur API BingX: ${message}` },
        { status }
      );
    }

    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}
