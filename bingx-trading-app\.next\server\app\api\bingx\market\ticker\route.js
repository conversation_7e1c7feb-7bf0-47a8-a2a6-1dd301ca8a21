var R=require("../../../../../chunks/[turbopack]_runtime.js")("server/app/api/bingx/market/ticker/route.js")
R.c("server/chunks/node_modules_77ed3afc._.js")
R.c("server/chunks/[root-of-the-server]__77ba03e5._.js")
R.m("[project]/.next-internal/server/app/api/bingx/market/ticker/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bingx/market/ticker/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bingx/market/ticker/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
