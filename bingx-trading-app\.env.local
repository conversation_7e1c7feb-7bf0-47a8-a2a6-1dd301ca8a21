PI BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
 GET /api/bingx/orders?symbol=BTC-USDT 400 in 9838ms
 GET / 200 in 13914ms
 GET /favicon.ico?favicon.0b3bf435.ico 200 in 11857ms
 GET /api/bingx/positions?symbol=BTC-USDT
 400 in 14091ms
 GET /api/bingx/account 400 in 14172ms
 GET /api/bingx/orders?symbol=BTC-USDT 200 in 15020ms
 GET /favicon.ico 200 in 15080ms
[2025-09-02T05:01:18.708Z] INFO: [API] Récupération des ordres { symbol: 'BTC-USDT', orderId: null }
 GET /api/bingx/market/ticker 200 in 15471ms
[2025-09-02T05:01:19.492Z] WARN: Erreur API BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
 GET /favicon.ico?favicon.0b3bf435.ico 200 in 7399ms
[2025-09-02T05:01:38.037Z] INFO: [API] Récupération des ordres { symbol: 'BTC-USDT', orderId: null }
 GET /api/bingx/positions?symbol=BTC-USDT
 400 in 4972ms
[2025-09-02T05:01:38.533Z] WARN: Erreur API BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
 GET /api/bingx/orders?symbol=BTC-USDT 400 in 5363ms
 GET /api/bingx/account 400 in 5504ms
[2025-09-02T05:02:07.785Z] INFO: [API] Récupération des ordres { symbol: 'BTC-USDT', orderId: null }
 GET /api/bingx/account 400 in 4747ms
 GET /api/bingx/positions?symbol=BTC-USDT
 400 in 4965ms
[2025-09-02T05:02:09.355Z] WARN: Erreur API BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
 GET /api/bingx/orders?symbol=BTC-USDT 400 in 6134ms
 GET /api/bingx/account 200 in 16618ms
[2025-09-02T05:03:23.231Z] INFO: [API] Récupération des ordres { symbol: 'BTC-USDT', orderId: null }
[2025-09-02T05:03:23.972Z] WARN: Erreur API BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
[2025-09-02T05:03:50.910Z] INFO: [API] Récupération des ordres { symbol: 'BTC-USDT', orderId: null }
[2025-09-02T05:03:53.797Z] WARN: Erreur API BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
[2025-09-02T05:04:20.738Z] INFO: [API] Récupération des ordres { symbol: 'BTC-USDT', orderId: null }
[2025-09-02T05:04:22.990Z] WARN: Erreur API BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
[2025-09-02T05:04:47.258Z] INFO: [API] Récupération des ordres { symbol: 'BTC-USDT', orderId: null }
[2025-09-02T05:04:50.193Z] WARN: Erreur API BingX lors de la récupération des ordres {
  code: 100001,
  message: 'Signature verification failed due to signature mismatch,please verify our authentication signature and try to  run our sample code  from the link https://bingx-api.github.io/docs/#/en-us/spot/account-api.html#Query%20Assets'
}
