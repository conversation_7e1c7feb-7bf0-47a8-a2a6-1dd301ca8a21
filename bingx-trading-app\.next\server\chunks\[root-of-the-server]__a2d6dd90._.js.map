{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/lib/bingx-auth.ts"], "sourcesContent": ["import crypto from 'crypto';\n\nexport interface BingXConfig {\n  apiKey: string;\n  secretKey: string;\n  baseUrl: string;\n}\n\nexport interface BingXRequestParams {\n  [key: string]: string | number | boolean;\n}\n\n/**\n * <PERSON><PERSON><PERSON> la signature HMAC SHA256 requise par BingX\n */\nexport function generateSignature(\n  queryString: string,\n  secretKey: string\n): string {\n  return crypto\n    .createHmac('sha256', secretKey)\n    .update(queryString)\n    .digest('hex');\n}\n\n/**\n * Crée une query string à partir des paramètres\n */\nexport function createQueryString(params: BingXRequestParams): string {\n  const sortedParams = Object.keys(params)\n    .sort()\n    .reduce((result, key) => {\n      result[key] = params[key];\n      return result;\n    }, {} as BingXRequestParams);\n\n  return new URLSearchParams(\n    Object.entries(sortedParams).map(([key, value]) => [key, String(value)])\n  ).toString();\n}\n\n/**\n * G<PERSON>ère les headers d'authentification pour les requêtes BingX\n */\nexport function generateAuthHeaders(\n  method: string,\n  endpoint: string,\n  params: BingXRequestParams,\n  config: BingXConfig\n): Record<string, string> {\n  return {\n    'X-BX-APIKEY': config.apiKey,\n    'Content-Type': 'application/json',\n    'User-Agent': 'BingX-Trading-App/1.0.0',\n  };\n}\n\n/**\n * Construit l'URL complète avec les paramètres signés\n */\nexport function buildSignedUrl(\n  endpoint: string,\n  params: BingXRequestParams,\n  config: BingXConfig\n): string {\n  const timestamp = Date.now();\n  const paramsWithTimestamp = {\n    ...params,\n    timestamp,\n  };\n\n  // Créer la query string pour la signature\n  const queryString = createQueryString(paramsWithTimestamp);\n\n  // Générer la signature avec la query string\n  const signature = generateSignature(queryString, config.secretKey);\n\n  // Ajouter la signature aux paramètres\n  const finalParams = {\n    ...paramsWithTimestamp,\n    signature,\n  };\n\n  const finalQueryString = createQueryString(finalParams);\n  return `${config.baseUrl}${endpoint}?${finalQueryString}`;\n}\n\n/**\n * Valide la configuration BingX\n */\nexport function validateBingXConfig(config: Partial<BingXConfig>): config is BingXConfig {\n  return !!(\n    config.apiKey &&\n    config.secretKey &&\n    config.baseUrl &&\n    typeof config.apiKey === 'string' &&\n    typeof config.secretKey === 'string' &&\n    typeof config.baseUrl === 'string'\n  );\n}\n\n/**\n * Obtient la configuration BingX depuis les variables d'environnement\n */\nexport function getBingXConfig(): BingXConfig {\n  const config = {\n    apiKey: process.env.BINGX_API_KEY,\n    secretKey: process.env.BINGX_SECRET_KEY,\n    baseUrl: process.env.BINGX_BASE_URL || 'https://open-api.bingx.com',\n  };\n\n  if (!validateBingXConfig(config)) {\n    throw new Error('Configuration BingX invalide. Vérifiez vos variables d\\'environnement.');\n  }\n\n  return config;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAeO,SAAS,kBACd,WAAmB,EACnB,SAAiB;IAEjB,OAAO,gHAAM,CACV,UAAU,CAAC,UAAU,WACrB,MAAM,CAAC,aACP,MAAM,CAAC;AACZ;AAKO,SAAS,kBAAkB,MAA0B;IAC1D,MAAM,eAAe,OAAO,IAAI,CAAC,QAC9B,IAAI,GACJ,MAAM,CAAC,CAAC,QAAQ;QACf,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QACzB,OAAO;IACT,GAAG,CAAC;IAEN,OAAO,IAAI,gBACT,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK;YAAC;YAAK,OAAO;SAAO,GACvE,QAAQ;AACZ;AAKO,SAAS,oBACd,MAAc,EACd,QAAgB,EAChB,MAA0B,EAC1B,MAAmB;IAEnB,OAAO;QACL,eAAe,OAAO,MAAM;QAC5B,gBAAgB;QAChB,cAAc;IAChB;AACF;AAKO,SAAS,eACd,QAAgB,EAChB,MAA0B,EAC1B,MAAmB;IAEnB,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,sBAAsB;QAC1B,GAAG,MAAM;QACT;IACF;IAEA,0CAA0C;IAC1C,MAAM,cAAc,kBAAkB;IAEtC,4CAA4C;IAC5C,MAAM,YAAY,kBAAkB,aAAa,OAAO,SAAS;IAEjE,sCAAsC;IACtC,MAAM,cAAc;QAClB,GAAG,mBAAmB;QACtB;IACF;IAEA,MAAM,mBAAmB,kBAAkB;IAC3C,OAAO,GAAG,OAAO,OAAO,GAAG,SAAS,CAAC,EAAE,kBAAkB;AAC3D;AAKO,SAAS,oBAAoB,MAA4B;IAC9D,OAAO,CAAC,CAAC,CACP,OAAO,MAAM,IACb,OAAO,SAAS,IAChB,OAAO,OAAO,IACd,OAAO,OAAO,MAAM,KAAK,YACzB,OAAO,OAAO,SAAS,KAAK,YAC5B,OAAO,OAAO,OAAO,KAAK,QAC5B;AACF;AAKO,SAAS;IACd,MAAM,SAAS;QACb,QAAQ,QAAQ,GAAG,CAAC,aAAa;QACjC,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QACvC,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC;IAEA,IAAI,CAAC,oBAAoB,SAAS;QAChC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/lib/rate-limiter.ts"], "sourcesContent": ["interface RateLimitEntry {\n  count: number;\n  resetTime: number;\n}\n\nclass RateLimiter {\n  private requests = new Map<string, RateLimitEntry>();\n  private maxRequests: number;\n  private windowMs: number;\n\n  constructor(maxRequests: number = 100, windowMs: number = 60000) {\n    this.maxRequests = maxRequests;\n    this.windowMs = windowMs;\n  }\n\n  isAllowed(identifier: string): boolean {\n    const now = Date.now();\n    const entry = this.requests.get(identifier);\n\n    if (!entry) {\n      // Première requête pour cet identifiant\n      this.requests.set(identifier, {\n        count: 1,\n        resetTime: now + this.windowMs,\n      });\n      return true;\n    }\n\n    if (now > entry.resetTime) {\n      // La fenêtre de temps est expirée, réinitialiser\n      entry.count = 1;\n      entry.resetTime = now + this.windowMs;\n      return true;\n    }\n\n    if (entry.count >= this.maxRequests) {\n      // Limite atteinte\n      return false;\n    }\n\n    // Incrémenter le compteur\n    entry.count++;\n    return true;\n  }\n\n  getRemainingRequests(identifier: string): number {\n    const entry = this.requests.get(identifier);\n    if (!entry) return this.maxRequests;\n\n    const now = Date.now();\n    if (now > entry.resetTime) {\n      return this.maxRequests;\n    }\n\n    return Math.max(0, this.maxRequests - entry.count);\n  }\n\n  getResetTime(identifier: string): number {\n    const entry = this.requests.get(identifier);\n    if (!entry) return 0;\n\n    const now = Date.now();\n    if (now > entry.resetTime) {\n      return 0;\n    }\n\n    return entry.resetTime - now;\n  }\n\n  // Nettoyer les entrées expirées\n  cleanup(): void {\n    const now = Date.now();\n    for (const [key, entry] of this.requests.entries()) {\n      if (now > entry.resetTime) {\n        this.requests.delete(key);\n      }\n    }\n  }\n}\n\n// Instance globale du rate limiter\nexport const rateLimiter = new RateLimiter(\n  parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),\n  parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000')\n);\n\n// Nettoyer les entrées expirées toutes les 5 minutes\nsetInterval(() => {\n  rateLimiter.cleanup();\n}, 5 * 60 * 1000);\n\nexport function getRateLimitHeaders(identifier: string) {\n  return {\n    'X-RateLimit-Limit': rateLimiter['maxRequests'].toString(),\n    'X-RateLimit-Remaining': rateLimiter.getRemainingRequests(identifier).toString(),\n    'X-RateLimit-Reset': Math.ceil((Date.now() + rateLimiter.getResetTime(identifier)) / 1000).toString(),\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAKA,MAAM;IACI,WAAW,IAAI,MAA8B;IAC7C,YAAoB;IACpB,SAAiB;IAEzB,YAAY,cAAsB,GAAG,EAAE,WAAmB,KAAK,CAAE;QAC/D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,UAAU,UAAkB,EAAW;QACrC,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAEhC,IAAI,CAAC,OAAO;YACV,wCAAwC;YACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY;gBAC5B,OAAO;gBACP,WAAW,MAAM,IAAI,CAAC,QAAQ;YAChC;YACA,OAAO;QACT;QAEA,IAAI,MAAM,MAAM,SAAS,EAAE;YACzB,iDAAiD;YACjD,MAAM,KAAK,GAAG;YACd,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ;YACrC,OAAO;QACT;QAEA,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC,kBAAkB;YAClB,OAAO;QACT;QAEA,0BAA0B;QAC1B,MAAM,KAAK;QACX,OAAO;IACT;IAEA,qBAAqB,UAAkB,EAAU;QAC/C,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,WAAW;QAEnC,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,MAAM,SAAS,EAAE;YACzB,OAAO,IAAI,CAAC,WAAW;QACzB;QAEA,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,MAAM,KAAK;IACnD;IAEA,aAAa,UAAkB,EAAU;QACvC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,MAAM,SAAS,EAAE;YACzB,OAAO;QACT;QAEA,OAAO,MAAM,SAAS,GAAG;IAC3B;IAEA,gCAAgC;IAChC,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAI;YAClD,IAAI,MAAM,MAAM,SAAS,EAAE;gBACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvB;QACF;IACF;AACF;AAGO,MAAM,cAAc,IAAI,YAC7B,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI,QAChD,SAAS,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AAG/C,qDAAqD;AACrD,YAAY;IACV,YAAY,OAAO;AACrB,GAAG,IAAI,KAAK;AAEL,SAAS,oBAAoB,UAAkB;IACpD,OAAO;QACL,qBAAqB,WAAW,CAAC,cAAc,CAAC,QAAQ;QACxD,yBAAyB,YAAY,oBAAoB,CAAC,YAAY,QAAQ;QAC9E,qBAAqB,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,YAAY,YAAY,CAAC,WAAW,IAAI,MAAM,QAAQ;IACrG;AACF", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/lib/api-middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { rateLimiter, getRateLimitHeaders } from './rate-limiter';\n\nexport interface ApiError {\n  code: string;\n  message: string;\n  details?: any;\n}\n\nexport class ApiException extends Error {\n  constructor(\n    public statusCode: number,\n    public code: string,\n    message: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'ApiException';\n  }\n}\n\n// Obtenir l'identifiant client pour le rate limiting\nfunction getClientIdentifier(request: NextRequest): string {\n  // En production, utiliser l'IP réelle\n  const forwarded = request.headers.get('x-forwarded-for');\n  const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown';\n  return ip;\n}\n\n// Middleware de rate limiting\nexport function withRateLimit(handler: Function) {\n  return async (request: NextRequest, ...args: any[]) => {\n    const identifier = getClientIdentifier(request);\n    \n    if (!rateLimiter.isAllowed(identifier)) {\n      const headers = getRateLimitHeaders(identifier);\n      return NextResponse.json(\n        {\n          error: 'Trop de requêtes',\n          message: 'Limite de taux dépassée. Veuillez réessayer plus tard.',\n          code: 'RATE_LIMIT_EXCEEDED',\n        },\n        { \n          status: 429,\n          headers,\n        }\n      );\n    }\n\n    const response = await handler(request, ...args);\n    \n    // Ajouter les headers de rate limiting à la réponse\n    const headers = getRateLimitHeaders(identifier);\n    Object.entries(headers).forEach(([key, value]) => {\n      response.headers.set(key, value);\n    });\n\n    return response;\n  };\n}\n\n// Middleware de gestion d'erreurs\nexport function withErrorHandling(handler: Function) {\n  return async (request: NextRequest, ...args: any[]) => {\n    try {\n      return await handler(request, ...args);\n    } catch (error) {\n      console.error('Erreur API:', error);\n\n      if (error instanceof ApiException) {\n        return NextResponse.json(\n          {\n            error: error.code,\n            message: error.message,\n            details: error.details,\n          },\n          { status: error.statusCode }\n        );\n      }\n\n      // Erreur générique\n      return NextResponse.json(\n        {\n          error: 'INTERNAL_SERVER_ERROR',\n          message: 'Une erreur interne s\\'est produite',\n        },\n        { status: 500 }\n      );\n    }\n  };\n}\n\n// Middleware de validation des paramètres\nexport function withValidation(schema: any) {\n  return function(handler: Function) {\n    return async (request: NextRequest, ...args: any[]) => {\n      try {\n        let data: any = {};\n\n        if (request.method === 'GET') {\n          const { searchParams } = new URL(request.url);\n          data = Object.fromEntries(searchParams.entries());\n        } else if (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH') {\n          data = await request.json();\n        }\n\n        // Validation basique (peut être étendue avec une bibliothèque comme Zod)\n        const validationResult = validateData(data, schema);\n        if (!validationResult.isValid) {\n          throw new ApiException(\n            400,\n            'VALIDATION_ERROR',\n            'Données invalides',\n            validationResult.errors\n          );\n        }\n\n        return await handler(request, ...args);\n      } catch (error) {\n        if (error instanceof ApiException) {\n          throw error;\n        }\n        throw new ApiException(400, 'INVALID_REQUEST', 'Requête invalide');\n      }\n    };\n  };\n}\n\n// Fonction de validation simple\nfunction validateData(data: any, schema: any): { isValid: boolean; errors?: string[] } {\n  const errors: string[] = [];\n\n  for (const [field, rules] of Object.entries(schema)) {\n    const value = data[field];\n    const fieldRules = rules as any;\n\n    if (fieldRules.required && (value === undefined || value === null || value === '')) {\n      errors.push(`Le champ ${field} est requis`);\n      continue;\n    }\n\n    if (value !== undefined && value !== null && value !== '') {\n      if (fieldRules.type && typeof value !== fieldRules.type) {\n        errors.push(`Le champ ${field} doit être de type ${fieldRules.type}`);\n      }\n\n      if (fieldRules.min && typeof value === 'number' && value < fieldRules.min) {\n        errors.push(`Le champ ${field} doit être supérieur ou égal à ${fieldRules.min}`);\n      }\n\n      if (fieldRules.max && typeof value === 'number' && value > fieldRules.max) {\n        errors.push(`Le champ ${field} doit être inférieur ou égal à ${fieldRules.max}`);\n      }\n\n      if (fieldRules.pattern && typeof value === 'string' && !fieldRules.pattern.test(value)) {\n        errors.push(`Le champ ${field} ne respecte pas le format requis`);\n      }\n\n      if (fieldRules.enum && !fieldRules.enum.includes(value)) {\n        errors.push(`Le champ ${field} doit être l'une des valeurs: ${fieldRules.enum.join(', ')}`);\n      }\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors: errors.length > 0 ? errors : undefined,\n  };\n}\n\n// Middleware combiné\nexport function withApiMiddleware(handler: Function, options: { \n  rateLimit?: boolean; \n  validation?: any; \n} = {}) {\n  let wrappedHandler = handler;\n\n  // Appliquer la validation si fournie\n  if (options.validation) {\n    wrappedHandler = withValidation(options.validation)(wrappedHandler);\n  }\n\n  // Appliquer le rate limiting si activé\n  if (options.rateLimit !== false) {\n    wrappedHandler = withRateLimit(wrappedHandler);\n  }\n\n  // Toujours appliquer la gestion d'erreurs\n  wrappedHandler = withErrorHandling(wrappedHandler);\n\n  return wrappedHandler;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAQO,MAAM,qBAAqB;;;;IAChC,YACE,AAAO,UAAkB,EACzB,AAAO,IAAY,EACnB,OAAe,EACf,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eALC,aAAA,iBACA,OAAA,WAEA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,qDAAqD;AACrD,SAAS,oBAAoB,OAAoB;IAC/C,sCAAsC;IACtC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,KAAK,YAAY,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,EAAE,IAAI;IAC/D,OAAO;AACT;AAGO,SAAS,cAAc,OAAiB;IAC7C,OAAO,OAAO,SAAsB,GAAG;QACrC,MAAM,aAAa,oBAAoB;QAEvC,IAAI,CAAC,8IAAW,CAAC,SAAS,CAAC,aAAa;YACtC,MAAM,UAAU,IAAA,sJAAmB,EAAC;YACpC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS;gBACT,MAAM;YACR,GACA;gBACE,QAAQ;gBACR;YACF;QAEJ;QAEA,MAAM,WAAW,MAAM,QAAQ,YAAY;QAE3C,oDAAoD;QACpD,MAAM,UAAU,IAAA,sJAAmB,EAAC;QACpC,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;QAC5B;QAEA,OAAO;IACT;AACF;AAGO,SAAS,kBAAkB,OAAiB;IACjD,OAAO,OAAO,SAAsB,GAAG;QACrC,IAAI;YACF,OAAO,MAAM,QAAQ,YAAY;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAE7B,IAAI,iBAAiB,cAAc;gBACjC,OAAO,gJAAY,CAAC,IAAI,CACtB;oBACE,OAAO,MAAM,IAAI;oBACjB,SAAS,MAAM,OAAO;oBACtB,SAAS,MAAM,OAAO;gBACxB,GACA;oBAAE,QAAQ,MAAM,UAAU;gBAAC;YAE/B;YAEA,mBAAmB;YACnB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAGO,SAAS,eAAe,MAAW;IACxC,OAAO,SAAS,OAAiB;QAC/B,OAAO,OAAO,SAAsB,GAAG;YACrC,IAAI;gBACF,IAAI,OAAY,CAAC;gBAEjB,IAAI,QAAQ,MAAM,KAAK,OAAO;oBAC5B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;oBAC5C,OAAO,OAAO,WAAW,CAAC,aAAa,OAAO;gBAChD,OAAO,IAAI,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,SAAS;oBAC9F,OAAO,MAAM,QAAQ,IAAI;gBAC3B;gBAEA,yEAAyE;gBACzE,MAAM,mBAAmB,aAAa,MAAM;gBAC5C,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC7B,MAAM,IAAI,aACR,KACA,oBACA,qBACA,iBAAiB,MAAM;gBAE3B;gBAEA,OAAO,MAAM,QAAQ,YAAY;YACnC,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,cAAc;oBACjC,MAAM;gBACR;gBACA,MAAM,IAAI,aAAa,KAAK,mBAAmB;YACjD;QACF;IACF;AACF;AAEA,gCAAgC;AAChC,SAAS,aAAa,IAAS,EAAE,MAAW;IAC1C,MAAM,SAAmB,EAAE;IAE3B,KAAK,MAAM,CAAC,OAAO,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACnD,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,aAAa;QAEnB,IAAI,WAAW,QAAQ,IAAI,CAAC,UAAU,aAAa,UAAU,QAAQ,UAAU,EAAE,GAAG;YAClF,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC;YAC1C;QACF;QAEA,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,WAAW,IAAI,IAAI,OAAO,UAAU,WAAW,IAAI,EAAE;gBACvD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM,mBAAmB,EAAE,WAAW,IAAI,EAAE;YACtE;YAEA,IAAI,WAAW,GAAG,IAAI,OAAO,UAAU,YAAY,QAAQ,WAAW,GAAG,EAAE;gBACzE,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM,+BAA+B,EAAE,WAAW,GAAG,EAAE;YACjF;YAEA,IAAI,WAAW,GAAG,IAAI,OAAO,UAAU,YAAY,QAAQ,WAAW,GAAG,EAAE;gBACzE,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM,+BAA+B,EAAE,WAAW,GAAG,EAAE;YACjF;YAEA,IAAI,WAAW,OAAO,IAAI,OAAO,UAAU,YAAY,CAAC,WAAW,OAAO,CAAC,IAAI,CAAC,QAAQ;gBACtF,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM,iCAAiC,CAAC;YAClE;YAEA,IAAI,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBACvD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM,8BAA8B,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO;YAC5F;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;IACvC;AACF;AAGO,SAAS,kBAAkB,OAAiB,EAAE,UAGjD,CAAC,CAAC;IACJ,IAAI,iBAAiB;IAErB,qCAAqC;IACrC,IAAI,QAAQ,UAAU,EAAE;QACtB,iBAAiB,eAAe,QAAQ,UAAU,EAAE;IACtD;IAEA,uCAAuC;IACvC,IAAI,QAAQ,SAAS,KAAK,OAAO;QAC/B,iBAAiB,cAAc;IACjC;IAEA,0CAA0C;IAC1C,iBAAiB,kBAAkB;IAEnC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/lib/logger.ts"], "sourcesContent": ["export enum LogLevel {\n  ERROR = 0,\n  WARN = 1,\n  INFO = 2,\n  DEBUG = 3,\n}\n\nexport interface LogEntry {\n  timestamp: string;\n  level: LogLevel;\n  message: string;\n  data?: any;\n  userId?: string;\n  ip?: string;\n  userAgent?: string;\n  endpoint?: string;\n  method?: string;\n}\n\nclass Logger {\n  private logLevel: LogLevel;\n\n  constructor() {\n    this.logLevel = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG;\n  }\n\n  private formatTimestamp(): string {\n    return new Date().toISOString();\n  }\n\n  private shouldLog(level: LogLevel): boolean {\n    return level <= this.logLevel;\n  }\n\n  private createLogEntry(\n    level: LogLevel,\n    message: string,\n    data?: any,\n    context?: {\n      userId?: string;\n      ip?: string;\n      userAgent?: string;\n      endpoint?: string;\n      method?: string;\n    }\n  ): LogEntry {\n    return {\n      timestamp: this.formatTimestamp(),\n      level,\n      message,\n      data,\n      ...context,\n    };\n  }\n\n  private writeLog(entry: LogEntry): void {\n    if (!this.shouldLog(entry.level)) return;\n\n    const levelName = LogLevel[entry.level];\n    const logMessage = `[${entry.timestamp}] ${levelName}: ${entry.message}`;\n\n    switch (entry.level) {\n      case LogLevel.ERROR:\n        console.error(logMessage, entry.data || '');\n        break;\n      case LogLevel.WARN:\n        console.warn(logMessage, entry.data || '');\n        break;\n      case LogLevel.INFO:\n        console.info(logMessage, entry.data || '');\n        break;\n      case LogLevel.DEBUG:\n        console.debug(logMessage, entry.data || '');\n        break;\n    }\n\n    // En production, ici on pourrait envoyer les logs vers un service externe\n    // comme CloudWatch, Datadog, etc.\n    if (process.env.NODE_ENV === 'production') {\n      this.sendToExternalService(entry);\n    }\n  }\n\n  private sendToExternalService(entry: LogEntry): void {\n    // Implémentation pour envoyer vers un service de logging externe\n    // Par exemple: CloudWatch, Datadog, LogRocket, etc.\n  }\n\n  error(message: string, data?: any, context?: any): void {\n    const entry = this.createLogEntry(LogLevel.ERROR, message, data, context);\n    this.writeLog(entry);\n  }\n\n  warn(message: string, data?: any, context?: any): void {\n    const entry = this.createLogEntry(LogLevel.WARN, message, data, context);\n    this.writeLog(entry);\n  }\n\n  info(message: string, data?: any, context?: any): void {\n    const entry = this.createLogEntry(LogLevel.INFO, message, data, context);\n    this.writeLog(entry);\n  }\n\n  debug(message: string, data?: any, context?: any): void {\n    const entry = this.createLogEntry(LogLevel.DEBUG, message, data, context);\n    this.writeLog(entry);\n  }\n\n  // Méthodes spécialisées pour la sécurité\n  security(message: string, data?: any, context?: any): void {\n    const entry = this.createLogEntry(LogLevel.WARN, `[SECURITY] ${message}`, data, context);\n    this.writeLog(entry);\n  }\n\n  trading(message: string, data?: any, context?: any): void {\n    const entry = this.createLogEntry(LogLevel.INFO, `[TRADING] ${message}`, data, context);\n    this.writeLog(entry);\n  }\n\n  api(message: string, data?: any, context?: any): void {\n    const entry = this.createLogEntry(LogLevel.INFO, `[API] ${message}`, data, context);\n    this.writeLog(entry);\n  }\n}\n\n// Instance singleton du logger\nexport const logger = new Logger();\n\n// Helper pour extraire le contexte d'une requête\nexport function getRequestContext(request: Request): {\n  ip?: string;\n  userAgent?: string;\n  endpoint?: string;\n  method?: string;\n} {\n  const url = new URL(request.url);\n  \n  return {\n    ip: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',\n    userAgent: request.headers.get('user-agent') || 'unknown',\n    endpoint: url.pathname,\n    method: request.method,\n  };\n}\n\n// Middleware de logging pour les API routes\nexport function withLogging(handler: Function) {\n  return async (request: Request, ...args: any[]) => {\n    const context = getRequestContext(request);\n    const startTime = Date.now();\n\n    logger.api(`Requête reçue: ${context.method} ${context.endpoint}`, null, context);\n\n    try {\n      const response = await handler(request, ...args);\n      const duration = Date.now() - startTime;\n      \n      logger.api(\n        `Requête traitée avec succès: ${context.method} ${context.endpoint}`,\n        { duration: `${duration}ms`, status: response.status },\n        context\n      );\n\n      return response;\n    } catch (error) {\n      const duration = Date.now() - startTime;\n      \n      logger.error(\n        `Erreur lors du traitement de la requête: ${context.method} ${context.endpoint}`,\n        { \n          error: error instanceof Error ? error.message : 'Erreur inconnue',\n          duration: `${duration}ms`,\n          stack: error instanceof Error ? error.stack : undefined,\n        },\n        context\n      );\n\n      throw error;\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAO,IAAA,AAAK,kCAAA;;;;;WAAA;;AAmBZ,MAAM;IACI,SAAmB;IAE3B,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEQ,kBAA0B;QAChC,OAAO,IAAI,OAAO,WAAW;IAC/B;IAEQ,UAAU,KAAe,EAAW;QAC1C,OAAO,SAAS,IAAI,CAAC,QAAQ;IAC/B;IAEQ,eACN,KAAe,EACf,OAAe,EACf,IAAU,EACV,OAMC,EACS;QACV,OAAO;YACL,WAAW,IAAI,CAAC,eAAe;YAC/B;YACA;YACA;YACA,GAAG,OAAO;QACZ;IACF;IAEQ,SAAS,KAAe,EAAQ;QACtC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;QAElC,MAAM,YAAY,QAAQ,CAAC,MAAM,KAAK,CAAC;QACvC,MAAM,aAAa,CAAC,CAAC,EAAE,MAAM,SAAS,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE,MAAM,OAAO,EAAE;QAExE,OAAQ,MAAM,KAAK;YACjB;gBACE,QAAQ,KAAK,CAAC,YAAY,MAAM,IAAI,IAAI;gBACxC;YACF;gBACE,QAAQ,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI;gBACvC;YACF;gBACE,QAAQ,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI;gBACvC;YACF;gBACE,QAAQ,KAAK,CAAC,YAAY,MAAM,IAAI,IAAI;gBACxC;QACJ;QAEA,0EAA0E;QAC1E,kCAAkC;QAClC;;IAGF;IAEQ,sBAAsB,KAAe,EAAQ;IACnD,iEAAiE;IACjE,oDAAoD;IACtD;IAEA,MAAM,OAAe,EAAE,IAAU,EAAE,OAAa,EAAQ;QACtD,MAAM,QAAQ,IAAI,CAAC,cAAc,IAAiB,SAAS,MAAM;QACjE,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,KAAK,OAAe,EAAE,IAAU,EAAE,OAAa,EAAQ;QACrD,MAAM,QAAQ,IAAI,CAAC,cAAc,IAAgB,SAAS,MAAM;QAChE,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,KAAK,OAAe,EAAE,IAAU,EAAE,OAAa,EAAQ;QACrD,MAAM,QAAQ,IAAI,CAAC,cAAc,IAAgB,SAAS,MAAM;QAChE,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,MAAM,OAAe,EAAE,IAAU,EAAE,OAAa,EAAQ;QACtD,MAAM,QAAQ,IAAI,CAAC,cAAc,IAAiB,SAAS,MAAM;QACjE,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,yCAAyC;IACzC,SAAS,OAAe,EAAE,IAAU,EAAE,OAAa,EAAQ;QACzD,MAAM,QAAQ,IAAI,CAAC,cAAc,IAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM;QAChF,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,QAAQ,OAAe,EAAE,IAAU,EAAE,OAAa,EAAQ;QACxD,MAAM,QAAQ,IAAI,CAAC,cAAc,IAAgB,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM;QAC/E,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,IAAI,OAAe,EAAE,IAAU,EAAE,OAAa,EAAQ;QACpD,MAAM,QAAQ,IAAI,CAAC,cAAc,IAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM;QAC3E,IAAI,CAAC,QAAQ,CAAC;IAChB;AACF;AAGO,MAAM,SAAS,IAAI;AAGnB,SAAS,kBAAkB,OAAgB;IAMhD,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;IAE/B,OAAO;QACL,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,IAAI,CAAC,EAAE,IAAI;QAC7D,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAChD,UAAU,IAAI,QAAQ;QACtB,QAAQ,QAAQ,MAAM;IACxB;AACF;AAGO,SAAS,YAAY,OAAiB;IAC3C,OAAO,OAAO,SAAkB,GAAG;QACjC,MAAM,UAAU,kBAAkB;QAClC,MAAM,YAAY,KAAK,GAAG;QAE1B,OAAO,GAAG,CAAC,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,QAAQ,EAAE,EAAE,MAAM;QAEzE,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,YAAY;YAC3C,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,OAAO,GAAG,CACR,CAAC,6BAA6B,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,QAAQ,EAAE,EACpE;gBAAE,UAAU,GAAG,SAAS,EAAE,CAAC;gBAAE,QAAQ,SAAS,MAAM;YAAC,GACrD;YAGF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,OAAO,KAAK,CACV,CAAC,yCAAyC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,QAAQ,EAAE,EAChF;gBACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,UAAU,GAAG,SAAS,EAAE,CAAC;gBACzB,OAAO,iBAAiB,QAAQ,MAAM,KAAK,GAAG;YAChD,GACA;YAGF,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/src/app/api/bingx/orders/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport axios from 'axios';\nimport { getBingXConfig, buildSignedUrl, generateAuthHeaders } from '@/lib/bingx-auth';\nimport { BingXResponse, Order, OrderRequest } from '@/types/bingx';\nimport { withApiMiddleware } from '@/lib/api-middleware';\nimport { logger, getRequestContext } from '@/lib/logger';\n\n// Récupérer les ordres\nconst getOrdersHandler = async (request: NextRequest) => {\n  const context = getRequestContext(request);\n  const config = getBingXConfig();\n  const { searchParams } = new URL(request.url);\n  const symbol = searchParams.get('symbol');\n  const orderId = searchParams.get('orderId');\n\n  logger.api('Récupération des ordres', { symbol, orderId }, context);\n\n  let endpoint: string;\n  let params: any = {};\n\n  if (orderId) {\n    // Récupérer un ordre spécifique\n    endpoint = '/openApi/swap/v2/trade/order';\n    params = { orderId };\n    if (symbol) params.symbol = symbol;\n  } else {\n    // Récupérer tous les ordres ouverts\n    endpoint = '/openApi/swap/v2/trade/openOrders';\n    if (symbol) params.symbol = symbol;\n  }\n\n  const url = buildSignedUrl(endpoint, params, config);\n  const headers = generateAuthHeaders('GET', endpoint, params, config);\n\n  const response = await axios.get<BingXResponse<Order[] | Order>>(url, {\n    headers,\n    timeout: 10000,\n  });\n\n  if (response.data.code !== 0) {\n    logger.warn('Erreur API BingX lors de la récupération des ordres', {\n      code: response.data.code,\n      message: response.data.msg\n    }, context);\n    return NextResponse.json(\n      { error: response.data.msg },\n      { status: 400 }\n    );\n  }\n\n  logger.api('Ordres récupérés avec succès', {\n    count: Array.isArray(response.data.data) ? response.data.data.length : 1\n  }, context);\n\n  return NextResponse.json(response.data.data);\n};\n\nexport const GET = withApiMiddleware(getOrdersHandler);\n\n// Schéma de validation pour la création d'ordres\nconst createOrderValidation = {\n  symbol: { required: true, type: 'string', pattern: /^[A-Z]+-[A-Z]+$/ },\n  side: { required: true, type: 'string', enum: ['BUY', 'SELL'] },\n  type: { required: true, type: 'string', enum: ['MARKET', 'LIMIT', 'STOP_MARKET', 'TAKE_PROFIT_MARKET'] },\n  quantity: { type: 'string', pattern: /^\\d+(\\.\\d+)?$/ },\n  price: { type: 'string', pattern: /^\\d+(\\.\\d+)?$/ },\n  stopPrice: { type: 'string', pattern: /^\\d+(\\.\\d+)?$/ },\n};\n\n// Créer un nouvel ordre\nconst createOrderHandler = async (request: NextRequest) => {\n  const context = getRequestContext(request);\n  const config = getBingXConfig();\n  const orderData: OrderRequest = await request.json();\n\n  logger.trading('Création d\\'un nouvel ordre', {\n    symbol: orderData.symbol,\n    side: orderData.side,\n    type: orderData.type,\n    quantity: orderData.quantity\n  }, context);\n\n  // Validation de la quantité pour les ordres non-market\n  if (orderData.type !== 'MARKET' && !orderData.quantity && !orderData.quoteOrderQty) {\n    logger.warn('Tentative de création d\\'ordre sans quantité', orderData, context);\n    return NextResponse.json(\n      { error: 'Quantité requise pour les ordres non-market' },\n      { status: 400 }\n    );\n  }\n\n  // Validation du prix pour les ordres limit\n  if (orderData.type === 'LIMIT' && !orderData.price) {\n    logger.warn('Tentative de création d\\'ordre limit sans prix', orderData, context);\n    return NextResponse.json(\n      { error: 'Prix requis pour les ordres limit' },\n      { status: 400 }\n    );\n  }\n\n  const endpoint = '/openApi/swap/v2/trade/order';\n  const params = {\n    ...orderData,\n    timestamp: Date.now(),\n  };\n\n  const url = buildSignedUrl(endpoint, params, config);\n  const headers = generateAuthHeaders('POST', endpoint, params, config);\n\n  const response = await axios.post<BingXResponse<Order>>(url, null, {\n    headers,\n    timeout: 10000,\n  });\n\n  if (response.data.code !== 0) {\n    logger.warn('Erreur API BingX lors de la création d\\'ordre', {\n      code: response.data.code,\n      message: response.data.msg,\n      orderData\n    }, context);\n    return NextResponse.json(\n      { error: response.data.msg },\n      { status: 400 }\n    );\n  }\n\n  logger.trading('Ordre créé avec succès', {\n    orderId: response.data.data.orderId,\n    symbol: response.data.data.symbol,\n    side: response.data.data.side,\n    type: response.data.data.type\n  }, context);\n\n  return NextResponse.json(response.data.data);\n};\n\nexport const POST = withApiMiddleware(createOrderHandler, {\n  validation: createOrderValidation\n});\n\n// Annuler un ordre\nconst cancelOrderHandler = async (request: NextRequest) => {\n  const context = getRequestContext(request);\n  const config = getBingXConfig();\n  const { searchParams } = new URL(request.url);\n  const symbol = searchParams.get('symbol');\n  const orderId = searchParams.get('orderId');\n\n  if (!symbol || !orderId) {\n    logger.warn('Tentative d\\'annulation d\\'ordre sans paramètres requis', { symbol, orderId }, context);\n    return NextResponse.json(\n      { error: 'Symbol et orderId sont requis' },\n      { status: 400 }\n    );\n  }\n\n  logger.trading('Annulation d\\'ordre', { symbol, orderId }, context);\n\n  const endpoint = '/openApi/swap/v2/trade/order';\n  const params = { symbol, orderId };\n\n  const url = buildSignedUrl(endpoint, params, config);\n  const headers = generateAuthHeaders('DELETE', endpoint, params, config);\n\n  const response = await axios.delete<BingXResponse<Order>>(url, {\n    headers,\n    timeout: 10000,\n  });\n\n  if (response.data.code !== 0) {\n    logger.warn('Erreur API BingX lors de l\\'annulation d\\'ordre', {\n      code: response.data.code,\n      message: response.data.msg,\n      symbol,\n      orderId\n    }, context);\n    return NextResponse.json(\n      { error: response.data.msg },\n      { status: 400 }\n    );\n  }\n\n  logger.trading('Ordre annulé avec succès', {\n    orderId: response.data.data.orderId,\n    symbol: response.data.data.symbol,\n    status: response.data.data.status\n  }, context);\n\n  return NextResponse.json(response.data.data);\n};\n\nexport const DELETE = withApiMiddleware(cancelOrderHandler);\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AAEA;AACA;;;;;;AAEA,uBAAuB;AACvB,MAAM,mBAAmB,OAAO;IAC9B,MAAM,UAAU,IAAA,2IAAiB,EAAC;IAClC,MAAM,SAAS,IAAA,+IAAc;IAC7B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,UAAU,aAAa,GAAG,CAAC;IAEjC,gIAAM,CAAC,GAAG,CAAC,2BAA2B;QAAE;QAAQ;IAAQ,GAAG;IAE3D,IAAI;IACJ,IAAI,SAAc,CAAC;IAEnB,IAAI,SAAS;QACX,gCAAgC;QAChC,WAAW;QACX,SAAS;YAAE;QAAQ;QACnB,IAAI,QAAQ,OAAO,MAAM,GAAG;IAC9B,OAAO;QACL,oCAAoC;QACpC,WAAW;QACX,IAAI,QAAQ,OAAO,MAAM,GAAG;IAC9B;IAEA,MAAM,MAAM,IAAA,+IAAc,EAAC,UAAU,QAAQ;IAC7C,MAAM,UAAU,IAAA,oJAAmB,EAAC,OAAO,UAAU,QAAQ;IAE7D,MAAM,WAAW,MAAM,kJAAK,CAAC,GAAG,CAAiC,KAAK;QACpE;QACA,SAAS;IACX;IAEA,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,GAAG;QAC5B,gIAAM,CAAC,IAAI,CAAC,uDAAuD;YACjE,MAAM,SAAS,IAAI,CAAC,IAAI;YACxB,SAAS,SAAS,IAAI,CAAC,GAAG;QAC5B,GAAG;QACH,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO,SAAS,IAAI,CAAC,GAAG;QAAC,GAC3B;YAAE,QAAQ;QAAI;IAElB;IAEA,gIAAM,CAAC,GAAG,CAAC,gCAAgC;QACzC,OAAO,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;IACzE,GAAG;IAEH,OAAO,gJAAY,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI;AAC7C;AAEO,MAAM,MAAM,IAAA,sJAAiB,EAAC;AAErC,iDAAiD;AACjD,MAAM,wBAAwB;IAC5B,QAAQ;QAAE,UAAU;QAAM,MAAM;QAAU,SAAS;IAAkB;IACrE,MAAM;QAAE,UAAU;QAAM,MAAM;QAAU,MAAM;YAAC;YAAO;SAAO;IAAC;IAC9D,MAAM;QAAE,UAAU;QAAM,MAAM;QAAU,MAAM;YAAC;YAAU;YAAS;YAAe;SAAqB;IAAC;IACvG,UAAU;QAAE,MAAM;QAAU,SAAS;IAAgB;IACrD,OAAO;QAAE,MAAM;QAAU,SAAS;IAAgB;IAClD,WAAW;QAAE,MAAM;QAAU,SAAS;IAAgB;AACxD;AAEA,wBAAwB;AACxB,MAAM,qBAAqB,OAAO;IAChC,MAAM,UAAU,IAAA,2IAAiB,EAAC;IAClC,MAAM,SAAS,IAAA,+IAAc;IAC7B,MAAM,YAA0B,MAAM,QAAQ,IAAI;IAElD,gIAAM,CAAC,OAAO,CAAC,+BAA+B;QAC5C,QAAQ,UAAU,MAAM;QACxB,MAAM,UAAU,IAAI;QACpB,MAAM,UAAU,IAAI;QACpB,UAAU,UAAU,QAAQ;IAC9B,GAAG;IAEH,uDAAuD;IACvD,IAAI,UAAU,IAAI,KAAK,YAAY,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,aAAa,EAAE;QAClF,gIAAM,CAAC,IAAI,CAAC,gDAAgD,WAAW;QACvE,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8C,GACvD;YAAE,QAAQ;QAAI;IAElB;IAEA,2CAA2C;IAC3C,IAAI,UAAU,IAAI,KAAK,WAAW,CAAC,UAAU,KAAK,EAAE;QAClD,gIAAM,CAAC,IAAI,CAAC,kDAAkD,WAAW;QACzE,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoC,GAC7C;YAAE,QAAQ;QAAI;IAElB;IAEA,MAAM,WAAW;IACjB,MAAM,SAAS;QACb,GAAG,SAAS;QACZ,WAAW,KAAK,GAAG;IACrB;IAEA,MAAM,MAAM,IAAA,+IAAc,EAAC,UAAU,QAAQ;IAC7C,MAAM,UAAU,IAAA,oJAAmB,EAAC,QAAQ,UAAU,QAAQ;IAE9D,MAAM,WAAW,MAAM,kJAAK,CAAC,IAAI,CAAuB,KAAK,MAAM;QACjE;QACA,SAAS;IACX;IAEA,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,GAAG;QAC5B,gIAAM,CAAC,IAAI,CAAC,iDAAiD;YAC3D,MAAM,SAAS,IAAI,CAAC,IAAI;YACxB,SAAS,SAAS,IAAI,CAAC,GAAG;YAC1B;QACF,GAAG;QACH,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO,SAAS,IAAI,CAAC,GAAG;QAAC,GAC3B;YAAE,QAAQ;QAAI;IAElB;IAEA,gIAAM,CAAC,OAAO,CAAC,0BAA0B;QACvC,SAAS,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;QACnC,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM;QACjC,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;QAC7B,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;IAC/B,GAAG;IAEH,OAAO,gJAAY,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI;AAC7C;AAEO,MAAM,OAAO,IAAA,sJAAiB,EAAC,oBAAoB;IACxD,YAAY;AACd;AAEA,mBAAmB;AACnB,MAAM,qBAAqB,OAAO;IAChC,MAAM,UAAU,IAAA,2IAAiB,EAAC;IAClC,MAAM,SAAS,IAAA,+IAAc;IAC7B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,UAAU,aAAa,GAAG,CAAC;IAEjC,IAAI,CAAC,UAAU,CAAC,SAAS;QACvB,gIAAM,CAAC,IAAI,CAAC,2DAA2D;YAAE;YAAQ;QAAQ,GAAG;QAC5F,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgC,GACzC;YAAE,QAAQ;QAAI;IAElB;IAEA,gIAAM,CAAC,OAAO,CAAC,uBAAuB;QAAE;QAAQ;IAAQ,GAAG;IAE3D,MAAM,WAAW;IACjB,MAAM,SAAS;QAAE;QAAQ;IAAQ;IAEjC,MAAM,MAAM,IAAA,+IAAc,EAAC,UAAU,QAAQ;IAC7C,MAAM,UAAU,IAAA,oJAAmB,EAAC,UAAU,UAAU,QAAQ;IAEhE,MAAM,WAAW,MAAM,kJAAK,CAAC,MAAM,CAAuB,KAAK;QAC7D;QACA,SAAS;IACX;IAEA,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,GAAG;QAC5B,gIAAM,CAAC,IAAI,CAAC,mDAAmD;YAC7D,MAAM,SAAS,IAAI,CAAC,IAAI;YACxB,SAAS,SAAS,IAAI,CAAC,GAAG;YAC1B;YACA;QACF,GAAG;QACH,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO,SAAS,IAAI,CAAC,GAAG;QAAC,GAC3B;YAAE,QAAQ;QAAI;IAElB;IAEA,gIAAM,CAAC,OAAO,CAAC,4BAA4B;QACzC,SAAS,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;QACnC,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM;QACjC,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM;IACnC,GAAG;IAEH,OAAO,gJAAY,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI;AAC7C;AAEO,MAAM,SAAS,IAAA,sJAAiB,EAAC", "debugId": null}}]}