var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/bingx/orders/route.js")
R.c("server/chunks/node_modules_78906b6a._.js")
R.c("server/chunks/[root-of-the-server]__a2d6dd90._.js")
R.m("[project]/.next-internal/server/app/api/bingx/orders/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bingx/orders/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bingx/orders/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
