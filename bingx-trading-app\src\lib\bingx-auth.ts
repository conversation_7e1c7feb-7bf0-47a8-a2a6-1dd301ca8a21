import crypto from 'crypto';

export interface BingXConfig {
  apiKey: string;
  secretKey: string;
  baseUrl: string;
}

export interface BingXRequestParams {
  [key: string]: string | number | boolean;
}

/**
 * <PERSON><PERSON><PERSON> la signature HMAC SHA256 requise par BingX
 */
export function generateSignature(
  queryString: string,
  secretKey: string
): string {
  return crypto
    .createHmac('sha256', secretKey)
    .update(queryString)
    .digest('hex');
}

/**
 * Crée une query string à partir des paramètres
 */
export function createQueryString(params: BingXRequestParams): string {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {} as BingXRequestParams);

  return new URLSearchParams(
    Object.entries(sortedParams).map(([key, value]) => [key, String(value)])
  ).toString();
}

/**
 * G<PERSON>ère les headers d'authentification pour les requêtes BingX
 */
export function generateAuthHeaders(
  method: string,
  endpoint: string,
  params: BingXRequestParams,
  config: BingXConfig
): Record<string, string> {
  return {
    'X-BX-APIKEY': config.apiKey,
    'Content-Type': 'application/json',
    'User-Agent': 'BingX-Trading-App/1.0.0',
  };
}

/**
 * Construit l'URL complète avec les paramètres signés
 */
export function buildSignedUrl(
  endpoint: string,
  params: BingXRequestParams,
  config: BingXConfig
): string {
  const timestamp = Date.now();
  const paramsWithTimestamp = {
    ...params,
    timestamp,
  };

  // Créer la query string pour la signature
  const queryString = createQueryString(paramsWithTimestamp);

  // Générer la signature avec la query string
  const signature = generateSignature(queryString, config.secretKey);

  // Ajouter la signature aux paramètres
  const finalParams = {
    ...paramsWithTimestamp,
    signature,
  };

  const finalQueryString = createQueryString(finalParams);
  return `${config.baseUrl}${endpoint}?${finalQueryString}`;
}

/**
 * Valide la configuration BingX
 */
export function validateBingXConfig(config: Partial<BingXConfig>): config is BingXConfig {
  return !!(
    config.apiKey &&
    config.secretKey &&
    config.baseUrl &&
    typeof config.apiKey === 'string' &&
    typeof config.secretKey === 'string' &&
    typeof config.baseUrl === 'string'
  );
}

/**
 * Obtient la configuration BingX depuis les variables d'environnement
 */
export function getBingXConfig(): BingXConfig {
  const config = {
    apiKey: process.env.BINGX_API_KEY,
    secretKey: process.env.BINGX_SECRET_KEY,
    baseUrl: process.env.BINGX_BASE_URL || 'https://open-api.bingx.com',
  };

  if (!validateBingXConfig(config)) {
    throw new Error('Configuration BingX invalide. Vérifiez vos variables d\'environnement.');
  }

  return config;
}
