{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = ((createState) => createState ? createStoreImpl(createState) : createStoreImpl);\n\nexport { createStore };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;IAAU;IAC7D,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAe,CAAC,cAAgB,cAAc,gBAAgB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/zustand/esm/react.mjs"], "sourcesContent": ["import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    React.useCallback(() => selector(api.getState()), [api, selector]),\n    React.useCallback(() => selector(api.getInitialState()), [api, selector])\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = ((createState) => createState ? createImpl(createState) : createImpl);\n\nexport { create, useStore };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,SAAS,GAAG,EAAE,WAAW,QAAQ;IACxC,MAAM,QAAQ,gNAAK,CAAC,oBAAoB,CACtC,IAAI,SAAS,EACb,gNAAK,CAAC,WAAW,CAAC,IAAM,SAAS,IAAI,QAAQ,KAAK;QAAC;QAAK;KAAS,GACjE,gNAAK,CAAC,WAAW,CAAC,IAAM,SAAS,IAAI,eAAe,KAAK;QAAC;QAAK;KAAS;IAE1E,gNAAK,CAAC,aAAa,CAAC;IACpB,OAAO;AACT;AACA,MAAM,aAAa,CAAC;IAClB,MAAM,MAAM,IAAA,yJAAW,EAAC;IACxB,MAAM,gBAAgB,CAAC,WAAa,SAAS,KAAK;IAClD,OAAO,MAAM,CAAC,eAAe;IAC7B,OAAO;AACT;AACA,MAAM,SAAU,CAAC,cAAgB,cAAc,WAAW,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/zustand/esm/middleware.mjs"], "sourcesContent": ["const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...args) => api.dispatch(...args), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst removeStoreFromTrackedConnections = (name, store) => {\n  if (store === void 0) return;\n  const connectionInfo = trackedConnections.get(name);\n  if (!connectionInfo) return;\n  delete connectionInfo.stores[store];\n  if (Object.keys(connectionInfo.stores).length === 0) {\n    trackedConnections.delete(name);\n  }\n};\nconst findCallerName = (stack) => {\n  var _a, _b;\n  if (!stack) return void 0;\n  const traceLines = stack.split(\"\\n\");\n  const apiSetStateLineIndex = traceLines.findIndex(\n    (traceLine) => traceLine.includes(\"api.setState\")\n  );\n  if (apiSetStateLineIndex < 0) return void 0;\n  const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || \"\";\n  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = ((state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? {\n      type: anonymousActionType || findCallerName(new Error().stack) || \"anonymous\"\n    } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  });\n  api.devtools = {\n    cleanup: () => {\n      if (connection && typeof connection.unsubscribe === \"function\") {\n        connection.unsubscribe();\n      }\n      removeStoreFromTrackedConnections(options.name, store);\n    }\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...args) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && args[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...args);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, fn) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) fn(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = ((selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  });\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nfunction combine(initialState, create) {\n  return (...args) => Object.assign({}, initialState, create(...args));\n}\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    return setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      return setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,MAAM,YAAY,CAAC,SAAS,UAAY,CAAC,KAAK,MAAM;QAClD,IAAI,QAAQ,GAAG,CAAC;YACd,IAAI,CAAC,QAAU,QAAQ,OAAO,SAAS,OAAO;YAC9C,OAAO;QACT;QACA,IAAI,oBAAoB,GAAG;QAC3B,OAAO;YAAE,UAAU,CAAC,GAAG,OAAS,IAAI,QAAQ,IAAI;YAAO,GAAG,OAAO;QAAC;IACpE;AACA,MAAM,QAAQ;AAEd,MAAM,qBAAqB,aAAa,GAAG,IAAI;AAC/C,MAAM,4BAA4B,CAAC;IACjC,MAAM,MAAM,mBAAmB,GAAG,CAAC;IACnC,IAAI,CAAC,KAAK,OAAO,CAAC;IAClB,OAAO,OAAO,WAAW,CACvB,OAAO,OAAO,CAAC,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,GAAK;YAAC;YAAK,KAAK,QAAQ;SAAG;AAE1E;AACA,MAAM,+BAA+B,CAAC,OAAO,oBAAoB;IAC/D,IAAI,UAAU,KAAK,GAAG;QACpB,OAAO;YACL,MAAM;YACN,YAAY,mBAAmB,OAAO,CAAC;QACzC;IACF;IACA,MAAM,qBAAqB,mBAAmB,GAAG,CAAC,QAAQ,IAAI;IAC9D,IAAI,oBAAoB;QACtB,OAAO;YAAE,MAAM;YAAW;YAAO,GAAG,kBAAkB;QAAC;IACzD;IACA,MAAM,gBAAgB;QACpB,YAAY,mBAAmB,OAAO,CAAC;QACvC,QAAQ,CAAC;IACX;IACA,mBAAmB,GAAG,CAAC,QAAQ,IAAI,EAAE;IACrC,OAAO;QAAE,MAAM;QAAW;QAAO,GAAG,aAAa;IAAC;AACpD;AACA,MAAM,oCAAoC,CAAC,MAAM;IAC/C,IAAI,UAAU,KAAK,GAAG;IACtB,MAAM,iBAAiB,mBAAmB,GAAG,CAAC;IAC9C,IAAI,CAAC,gBAAgB;IACrB,OAAO,eAAe,MAAM,CAAC,MAAM;IACnC,IAAI,OAAO,IAAI,CAAC,eAAe,MAAM,EAAE,MAAM,KAAK,GAAG;QACnD,mBAAmB,MAAM,CAAC;IAC5B;AACF;AACA,MAAM,iBAAiB,CAAC;IACtB,IAAI,IAAI;IACR,IAAI,CAAC,OAAO,OAAO,KAAK;IACxB,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,uBAAuB,WAAW,SAAS,CAC/C,CAAC,YAAc,UAAU,QAAQ,CAAC;IAEpC,IAAI,uBAAuB,GAAG,OAAO,KAAK;IAC1C,MAAM,aAAa,CAAC,CAAC,KAAK,UAAU,CAAC,uBAAuB,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,EAAE,KAAK;IACjG,OAAO,CAAC,KAAK,aAAa,IAAI,CAAC,WAAW,KAAK,OAAO,KAAK,IAAI,EAAE,CAAC,EAAE;AACtE;AACA,MAAM,eAAe,CAAC,IAAI,kBAAkB,CAAC,CAAC,GAAK,CAAC,KAAK,KAAK;QAC5D,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,GAAG,SAAS,GAAG;QAC5D,IAAI;QACJ,IAAI;YACF,qBAAqB,CAAC,WAAW,OAAO,UAAU,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,KAAK,OAAO,4BAA4B;QAC9J,EAAE,OAAO,GAAG,CACZ;QACA,IAAI,CAAC,oBAAoB;YACvB,OAAO,GAAG,KAAK,KAAK;QACtB;QACA,MAAM,EAAE,UAAU,EAAE,GAAG,uBAAuB,GAAG,6BAA6B,OAAO,oBAAoB;QACzG,IAAI,cAAc;QAClB,IAAI,QAAQ,GAAI,CAAC,OAAO,SAAS;YAC/B,MAAM,IAAI,IAAI,OAAO;YACrB,IAAI,CAAC,aAAa,OAAO;YACzB,MAAM,SAAS,iBAAiB,KAAK,IAAI;gBACvC,MAAM,uBAAuB,eAAe,IAAI,QAAQ,KAAK,KAAK;YACpE,IAAI,OAAO,iBAAiB,WAAW;gBAAE,MAAM;YAAa,IAAI;YAChE,IAAI,UAAU,KAAK,GAAG;gBACpB,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,QAAQ;gBACtD,OAAO;YACT;YACA,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAC3C;gBACE,GAAG,MAAM;gBACT,MAAM,GAAG,MAAM,CAAC,EAAE,OAAO,IAAI,EAAE;YACjC,GACA;gBACE,GAAG,0BAA0B,QAAQ,IAAI,CAAC;gBAC1C,CAAC,MAAM,EAAE,IAAI,QAAQ;YACvB;YAEF,OAAO;QACT;QACA,IAAI,QAAQ,GAAG;YACb,SAAS;gBACP,IAAI,cAAc,OAAO,WAAW,WAAW,KAAK,YAAY;oBAC9D,WAAW,WAAW;gBACxB;gBACA,kCAAkC,QAAQ,IAAI,EAAE;YAClD;QACF;QACA,MAAM,uBAAuB,CAAC,GAAG;YAC/B,MAAM,sBAAsB;YAC5B,cAAc;YACd,OAAO;YACP,cAAc;QAChB;QACA,MAAM,eAAe,GAAG,IAAI,QAAQ,EAAE,KAAK;QAC3C,IAAI,sBAAsB,IAAI,KAAK,aAAa;YAC9C,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC;QAChD,OAAO;YACL,sBAAsB,MAAM,CAAC,sBAAsB,KAAK,CAAC,GAAG;YAC5D,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAC3C,OAAO,WAAW,CAChB,OAAO,OAAO,CAAC,sBAAsB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,GAAK;oBAClE;oBACA,QAAQ,sBAAsB,KAAK,GAAG,eAAe,OAAO,QAAQ;iBACrE;QAGP;QACA,IAAI,IAAI,oBAAoB,IAAI,OAAO,IAAI,QAAQ,KAAK,YAAY;YAClE,IAAI,iCAAiC;YACrC,MAAM,mBAAmB,IAAI,QAAQ;YACrC,IAAI,QAAQ,GAAG,CAAC,GAAG;gBACjB,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,gBAAgB,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,gCAAgC;oBAC1I,QAAQ,IAAI,CACV;oBAEF,iCAAiC;gBACnC;gBACA,oBAAoB;YACtB;QACF;QACA,WAAW,SAAS,CAAC,CAAC;YACpB,IAAI;YACJ,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;wBACvC,QAAQ,KAAK,CACX;wBAEF;oBACF;oBACA,OAAO,cACL,QAAQ,OAAO,EACf,CAAC;wBACC,IAAI,OAAO,IAAI,KAAK,cAAc;4BAChC,IAAI,UAAU,KAAK,GAAG;gCACpB,qBAAqB,OAAO,KAAK;gCACjC;4BACF;4BACA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,EAAE,MAAM,KAAK,GAAG;gCAC1C,QAAQ,KAAK,CACX,CAAC;;;;oBAIC,CAAC;4BAEP;4BACA,MAAM,oBAAoB,OAAO,KAAK,CAAC,MAAM;4BAC7C,IAAI,sBAAsB,KAAK,KAAK,sBAAsB,MAAM;gCAC9D;4BACF;4BACA,IAAI,KAAK,SAAS,CAAC,IAAI,QAAQ,QAAQ,KAAK,SAAS,CAAC,oBAAoB;gCACxE,qBAAqB;4BACvB;4BACA;wBACF;wBACA,IAAI,CAAC,IAAI,oBAAoB,EAAE;wBAC/B,IAAI,OAAO,IAAI,QAAQ,KAAK,YAAY;wBACxC,IAAI,QAAQ,CAAC;oBACf;gBAEJ,KAAK;oBACH,OAAQ,QAAQ,OAAO,CAAC,IAAI;wBAC1B,KAAK;4BACH,qBAAqB;4BACrB,IAAI,UAAU,KAAK,GAAG;gCACpB,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI,QAAQ;4BACnE;4BACA,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;wBAC7F,KAAK;4BACH,IAAI,UAAU,KAAK,GAAG;gCACpB,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI,QAAQ;gCAC1D;4BACF;4BACA,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;wBAC7F,KAAK;4BACH,OAAO,cAAc,QAAQ,KAAK,EAAE,CAAC;gCACnC,IAAI,UAAU,KAAK,GAAG;oCACpB,qBAAqB;oCACrB,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI,QAAQ;oCAC1D;gCACF;gCACA,qBAAqB,KAAK,CAAC,MAAM;gCACjC,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;4BACtF;wBACF,KAAK;wBACL,KAAK;4BACH,OAAO,cAAc,QAAQ,KAAK,EAAE,CAAC;gCACnC,IAAI,UAAU,KAAK,GAAG;oCACpB,qBAAqB;oCACrB;gCACF;gCACA,IAAI,KAAK,SAAS,CAAC,IAAI,QAAQ,QAAQ,KAAK,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG;oCACnE,qBAAqB,KAAK,CAAC,MAAM;gCACnC;4BACF;wBACF,KAAK;4BAAgB;gCACnB,MAAM,EAAE,eAAe,EAAE,GAAG,QAAQ,OAAO;gCAC3C,MAAM,oBAAoB,CAAC,KAAK,gBAAgB,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK;gCACxG,IAAI,CAAC,mBAAmB;gCACxB,IAAI,UAAU,KAAK,GAAG;oCACpB,qBAAqB;gCACvB,OAAO;oCACL,qBAAqB,iBAAiB,CAAC,MAAM;gCAC/C;gCACA,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAC3C,MACA,eAAe;gCACf;gCAEF;4BACF;wBACA,KAAK;4BACH,OAAO,cAAc,CAAC;oBAC1B;oBACA;YACJ;QACF;QACA,OAAO;IACT;AACA,MAAM,WAAW;AACjB,MAAM,gBAAgB,CAAC,aAAa;IAClC,IAAI;IACJ,IAAI;QACF,SAAS,KAAK,KAAK,CAAC;IACtB,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CACX,mEACA;IAEJ;IACA,IAAI,WAAW,KAAK,GAAG,GAAG;AAC5B;AAEA,MAAM,4BAA4B,CAAC,KAAO,CAAC,KAAK,KAAK;QACnD,MAAM,gBAAgB,IAAI,SAAS;QACnC,IAAI,SAAS,GAAI,CAAC,UAAU,aAAa;YACvC,IAAI,WAAW;YACf,IAAI,aAAa;gBACf,MAAM,aAAa,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,UAAU,KAAK,OAAO,EAAE;gBAC/E,IAAI,eAAe,SAAS,IAAI,QAAQ;gBACxC,WAAW,CAAC;oBACV,MAAM,YAAY,SAAS;oBAC3B,IAAI,CAAC,WAAW,cAAc,YAAY;wBACxC,MAAM,gBAAgB;wBACtB,YAAY,eAAe,WAAW;oBACxC;gBACF;gBACA,IAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,eAAe,EAAE;oBACtD,YAAY,cAAc;gBAC5B;YACF;YACA,OAAO,cAAc;QACvB;QACA,MAAM,eAAe,GAAG,KAAK,KAAK;QAClC,OAAO;IACT;AACA,MAAM,wBAAwB;AAE9B,SAAS,QAAQ,YAAY,EAAE,MAAM;IACnC,OAAO,CAAC,GAAG,OAAS,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,UAAU;AAChE;AAEA,SAAS,kBAAkB,UAAU,EAAE,OAAO;IAC5C,IAAI;IACJ,IAAI;QACF,UAAU;IACZ,EAAE,OAAO,GAAG;QACV;IACF;IACA,MAAM,iBAAiB;QACrB,SAAS,CAAC;YACR,IAAI;YACJ,MAAM,QAAQ,CAAC;gBACb,IAAI,SAAS,MAAM;oBACjB,OAAO;gBACT;gBACA,OAAO,KAAK,KAAK,CAAC,MAAM,WAAW,OAAO,KAAK,IAAI,QAAQ,OAAO;YACpE;YACA,MAAM,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,KAAK,KAAK,OAAO,KAAK;YACxD,IAAI,eAAe,SAAS;gBAC1B,OAAO,IAAI,IAAI,CAAC;YAClB;YACA,OAAO,MAAM;QACf;QACA,SAAS,CAAC,MAAM,WAAa,QAAQ,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,UAAU,WAAW,OAAO,KAAK,IAAI,QAAQ,QAAQ;QACvH,YAAY,CAAC,OAAS,QAAQ,UAAU,CAAC;IAC3C;IACA,OAAO;AACT;AACA,MAAM,aAAa,CAAC,KAAO,CAAC;QAC1B,IAAI;YACF,MAAM,SAAS,GAAG;YAClB,IAAI,kBAAkB,SAAS;gBAC7B,OAAO;YACT;YACA,OAAO;gBACL,MAAK,WAAW;oBACd,OAAO,WAAW,aAAa;gBACjC;gBACA,OAAM,WAAW;oBACf,OAAO,IAAI;gBACb;YACF;QACF,EAAE,OAAO,GAAG;YACV,OAAO;gBACL,MAAK,YAAY;oBACf,OAAO,IAAI;gBACb;gBACA,OAAM,UAAU;oBACd,OAAO,WAAW,YAAY;gBAChC;YACF;QACF;IACF;AACA,MAAM,cAAc,CAAC,QAAQ,cAAgB,CAAC,KAAK,KAAK;QACtD,IAAI,UAAU;YACZ,SAAS,kBAAkB,IAAM;YACjC,YAAY,CAAC,QAAU;YACvB,SAAS;YACT,OAAO,CAAC,gBAAgB,eAAiB,CAAC;oBACxC,GAAG,YAAY;oBACf,GAAG,cAAc;gBACnB,CAAC;YACD,GAAG,WAAW;QAChB;QACA,IAAI,cAAc;QAClB,MAAM,qBAAqB,aAAa,GAAG,IAAI;QAC/C,MAAM,2BAA2B,aAAa,GAAG,IAAI;QACrD,IAAI,UAAU,QAAQ,OAAO;QAC7B,IAAI,CAAC,SAAS;YACZ,OAAO,OACL,CAAC,GAAG;gBACF,QAAQ,IAAI,CACV,CAAC,oDAAoD,EAAE,QAAQ,IAAI,CAAC,8CAA8C,CAAC;gBAErH,OAAO;YACT,GACA,KACA;QAEJ;QACA,MAAM,UAAU;YACd,MAAM,QAAQ,QAAQ,UAAU,CAAC;gBAAE,GAAG,KAAK;YAAC;YAC5C,OAAO,QAAQ,OAAO,CAAC,QAAQ,IAAI,EAAE;gBACnC;gBACA,SAAS,QAAQ,OAAO;YAC1B;QACF;QACA,MAAM,gBAAgB,IAAI,QAAQ;QAClC,IAAI,QAAQ,GAAG,CAAC,OAAO;YACrB,cAAc,OAAO;YACrB,OAAO;QACT;QACA,MAAM,eAAe,OACnB,CAAC,GAAG;YACF,OAAO;YACP,OAAO;QACT,GACA,KACA;QAEF,IAAI,eAAe,GAAG,IAAM;QAC5B,IAAI;QACJ,MAAM,UAAU;YACd,IAAI,IAAI;YACR,IAAI,CAAC,SAAS;YACd,cAAc;YACd,mBAAmB,OAAO,CAAC,CAAC;gBAC1B,IAAI;gBACJ,OAAO,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,MAAM;YAC1C;YACA,MAAM,0BAA0B,CAAC,CAAC,KAAK,QAAQ,kBAAkB,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,KAAK,aAAa,KAAK,KAAK;YAC1J,OAAO,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnE,IAAI,0BAA0B;oBAC5B,IAAI,OAAO,yBAAyB,OAAO,KAAK,YAAY,yBAAyB,OAAO,KAAK,QAAQ,OAAO,EAAE;wBAChH,IAAI,QAAQ,OAAO,EAAE;4BACnB,MAAM,YAAY,QAAQ,OAAO,CAC/B,yBAAyB,KAAK,EAC9B,yBAAyB,OAAO;4BAElC,IAAI,qBAAqB,SAAS;gCAChC,OAAO,UAAU,IAAI,CAAC,CAAC,SAAW;wCAAC;wCAAM;qCAAO;4BAClD;4BACA,OAAO;gCAAC;gCAAM;6BAAU;wBAC1B;wBACA,QAAQ,KAAK,CACX,CAAC,qFAAqF,CAAC;oBAE3F,OAAO;wBACL,OAAO;4BAAC;4BAAO,yBAAyB,KAAK;yBAAC;oBAChD;gBACF;gBACA,OAAO;oBAAC;oBAAO,KAAK;iBAAE;YACxB,GAAG,IAAI,CAAC,CAAC;gBACP,IAAI;gBACJ,MAAM,CAAC,UAAU,cAAc,GAAG;gBAClC,mBAAmB,QAAQ,KAAK,CAC9B,eACA,CAAC,MAAM,KAAK,KAAK,OAAO,MAAM;gBAEhC,IAAI,kBAAkB;gBACtB,IAAI,UAAU;oBACZ,OAAO;gBACT;YACF,GAAG,IAAI,CAAC;gBACN,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,kBAAkB,KAAK;gBAC1F,mBAAmB;gBACnB,cAAc;gBACd,yBAAyB,OAAO,CAAC,CAAC,KAAO,GAAG;YAC9C,GAAG,KAAK,CAAC,CAAC;gBACR,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,KAAK,GAAG;YAC7E;QACF;QACA,IAAI,OAAO,GAAG;YACZ,YAAY,CAAC;gBACX,UAAU;oBACR,GAAG,OAAO;oBACV,GAAG,UAAU;gBACf;gBACA,IAAI,WAAW,OAAO,EAAE;oBACtB,UAAU,WAAW,OAAO;gBAC9B;YACF;YACA,cAAc;gBACZ,WAAW,OAAO,KAAK,IAAI,QAAQ,UAAU,CAAC,QAAQ,IAAI;YAC5D;YACA,YAAY,IAAM;YAClB,WAAW,IAAM;YACjB,aAAa,IAAM;YACnB,WAAW,CAAC;gBACV,mBAAmB,GAAG,CAAC;gBACvB,OAAO;oBACL,mBAAmB,MAAM,CAAC;gBAC5B;YACF;YACA,mBAAmB,CAAC;gBAClB,yBAAyB,GAAG,CAAC;gBAC7B,OAAO;oBACL,yBAAyB,MAAM,CAAC;gBAClC;YACF;QACF;QACA,IAAI,CAAC,QAAQ,aAAa,EAAE;YAC1B;QACF;QACA,OAAO,oBAAoB;IAC7B;AACA,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/bind.js"], "sourcesContent": ["'use strict';\n\nexport default function bind(fn, thisArg) {\n  return function wrap() {\n    return fn.apply(thisArg, arguments);\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEe,SAAS,KAAK,EAAE,EAAE,OAAO;IACtC,OAAO,SAAS;QACd,OAAO,GAAG,KAAK,CAAC,SAAS;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/utils.js"], "sourcesContent": ["'use strict';\n\nimport bind from './helpers/bind.js';\n\n// utils is a library of generic helper functions non-specific to axios\n\nconst {toString} = Object.prototype;\nconst {getPrototypeOf} = Object;\nconst {iterator, toStringTag} = Symbol;\n\nconst kindOf = (cache => thing => {\n    const str = toString.call(thing);\n    return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n})(Object.create(null));\n\nconst kindOfTest = (type) => {\n  type = type.toLowerCase();\n  return (thing) => kindOf(thing) === type\n}\n\nconst typeOfTest = type => thing => typeof thing === type;\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n *\n * @returns {boolean} True if value is an Array, otherwise false\n */\nconst {isArray} = Array;\n\n/**\n * Determine if a value is undefined\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nconst isUndefined = typeOfTest('undefined');\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)\n    && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nconst isArrayBuffer = kindOfTest('ArrayBuffer');\n\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  let result;\n  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = (val) && (val.buffer) && (isArrayBuffer(val.buffer));\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a String, otherwise false\n */\nconst isString = typeOfTest('string');\n\n/**\n * Determine if a value is a Function\n *\n * @param {*} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nconst isFunction = typeOfTest('function');\n\n/**\n * Determine if a value is a Number\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Number, otherwise false\n */\nconst isNumber = typeOfTest('number');\n\n/**\n * Determine if a value is an Object\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an Object, otherwise false\n */\nconst isObject = (thing) => thing !== null && typeof thing === 'object';\n\n/**\n * Determine if a value is a Boolean\n *\n * @param {*} thing The value to test\n * @returns {boolean} True if value is a Boolean, otherwise false\n */\nconst isBoolean = thing => thing === true || thing === false;\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a plain Object, otherwise false\n */\nconst isPlainObject = (val) => {\n  if (kindOf(val) !== 'object') {\n    return false;\n  }\n\n  const prototype = getPrototypeOf(val);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(toStringTag in val) && !(iterator in val);\n}\n\n/**\n * Determine if a value is an empty object (safely handles Buffers)\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an empty object, otherwise false\n */\nconst isEmptyObject = (val) => {\n  // Early return for non-objects or Buffers to prevent RangeError\n  if (!isObject(val) || isBuffer(val)) {\n    return false;\n  }\n  \n  try {\n    return Object.keys(val).length === 0 && Object.getPrototypeOf(val) === Object.prototype;\n  } catch (e) {\n    // Fallback for any other objects that might cause RangeError with Object.keys()\n    return false;\n  }\n}\n\n/**\n * Determine if a value is a Date\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Date, otherwise false\n */\nconst isDate = kindOfTest('Date');\n\n/**\n * Determine if a value is a File\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nconst isFile = kindOfTest('File');\n\n/**\n * Determine if a value is a Blob\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nconst isBlob = kindOfTest('Blob');\n\n/**\n * Determine if a value is a FileList\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nconst isFileList = kindOfTest('FileList');\n\n/**\n * Determine if a value is a Stream\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nconst isStream = (val) => isObject(val) && isFunction(val.pipe);\n\n/**\n * Determine if a value is a FormData\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nconst isFormData = (thing) => {\n  let kind;\n  return thing && (\n    (typeof FormData === 'function' && thing instanceof FormData) || (\n      isFunction(thing.append) && (\n        (kind = kindOf(thing)) === 'formdata' ||\n        // detect form-data instance\n        (kind === 'object' && isFunction(thing.toString) && thing.toString() === '[object FormData]')\n      )\n    )\n  )\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nconst isURLSearchParams = kindOfTest('URLSearchParams');\n\nconst [isReadableStream, isRequest, isResponse, isHeaders] = ['ReadableStream', 'Request', 'Response', 'Headers'].map(kindOfTest);\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n *\n * @returns {String} The String freed of excess whitespace\n */\nconst trim = (str) => str.trim ?\n  str.trim() : str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n *\n * @param {Boolean} [allOwnKeys = false]\n * @returns {any}\n */\nfunction forEach(obj, fn, {allOwnKeys = false} = {}) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  let i;\n  let l;\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Buffer check\n    if (isBuffer(obj)) {\n      return;\n    }\n\n    // Iterate over object keys\n    const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);\n    const len = keys.length;\n    let key;\n\n    for (i = 0; i < len; i++) {\n      key = keys[i];\n      fn.call(null, obj[key], key, obj);\n    }\n  }\n}\n\nfunction findKey(obj, key) {\n  if (isBuffer(obj)){\n    return null;\n  }\n\n  key = key.toLowerCase();\n  const keys = Object.keys(obj);\n  let i = keys.length;\n  let _key;\n  while (i-- > 0) {\n    _key = keys[i];\n    if (key === _key.toLowerCase()) {\n      return _key;\n    }\n  }\n  return null;\n}\n\nconst _global = (() => {\n  /*eslint no-undef:0*/\n  if (typeof globalThis !== \"undefined\") return globalThis;\n  return typeof self !== \"undefined\" ? self : (typeof window !== 'undefined' ? window : global)\n})();\n\nconst isContextDefined = (context) => !isUndefined(context) && context !== _global;\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n *\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */) {\n  const {caseless} = isContextDefined(this) && this || {};\n  const result = {};\n  const assignValue = (val, key) => {\n    const targetKey = caseless && findKey(result, key) || key;\n    if (isPlainObject(result[targetKey]) && isPlainObject(val)) {\n      result[targetKey] = merge(result[targetKey], val);\n    } else if (isPlainObject(val)) {\n      result[targetKey] = merge({}, val);\n    } else if (isArray(val)) {\n      result[targetKey] = val.slice();\n    } else {\n      result[targetKey] = val;\n    }\n  }\n\n  for (let i = 0, l = arguments.length; i < l; i++) {\n    arguments[i] && forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n *\n * @param {Boolean} [allOwnKeys]\n * @returns {Object} The resulting value of object a\n */\nconst extend = (a, b, thisArg, {allOwnKeys}= {}) => {\n  forEach(b, (val, key) => {\n    if (thisArg && isFunction(val)) {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  }, {allOwnKeys});\n  return a;\n}\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n *\n * @returns {string} content value without BOM\n */\nconst stripBOM = (content) => {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n}\n\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n *\n * @returns {void}\n */\nconst inherits = (constructor, superConstructor, props, descriptors) => {\n  constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n  constructor.prototype.constructor = constructor;\n  Object.defineProperty(constructor, 'super', {\n    value: superConstructor.prototype\n  });\n  props && Object.assign(constructor.prototype, props);\n}\n\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function|Boolean} [filter]\n * @param {Function} [propFilter]\n *\n * @returns {Object}\n */\nconst toFlatObject = (sourceObj, destObj, filter, propFilter) => {\n  let props;\n  let i;\n  let prop;\n  const merged = {};\n\n  destObj = destObj || {};\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  if (sourceObj == null) return destObj;\n\n  do {\n    props = Object.getOwnPropertyNames(sourceObj);\n    i = props.length;\n    while (i-- > 0) {\n      prop = props[i];\n      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {\n        destObj[prop] = sourceObj[prop];\n        merged[prop] = true;\n      }\n    }\n    sourceObj = filter !== false && getPrototypeOf(sourceObj);\n  } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n\n  return destObj;\n}\n\n/**\n * Determines whether a string ends with the characters of a specified string\n *\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n *\n * @returns {boolean}\n */\nconst endsWith = (str, searchString, position) => {\n  str = String(str);\n  if (position === undefined || position > str.length) {\n    position = str.length;\n  }\n  position -= searchString.length;\n  const lastIndex = str.indexOf(searchString, position);\n  return lastIndex !== -1 && lastIndex === position;\n}\n\n\n/**\n * Returns new array from array like object or null if failed\n *\n * @param {*} [thing]\n *\n * @returns {?Array}\n */\nconst toArray = (thing) => {\n  if (!thing) return null;\n  if (isArray(thing)) return thing;\n  let i = thing.length;\n  if (!isNumber(i)) return null;\n  const arr = new Array(i);\n  while (i-- > 0) {\n    arr[i] = thing[i];\n  }\n  return arr;\n}\n\n/**\n * Checking if the Uint8Array exists and if it does, it returns a function that checks if the\n * thing passed in is an instance of Uint8Array\n *\n * @param {TypedArray}\n *\n * @returns {Array}\n */\n// eslint-disable-next-line func-names\nconst isTypedArray = (TypedArray => {\n  // eslint-disable-next-line func-names\n  return thing => {\n    return TypedArray && thing instanceof TypedArray;\n  };\n})(typeof Uint8Array !== 'undefined' && getPrototypeOf(Uint8Array));\n\n/**\n * For each entry in the object, call the function with the key and value.\n *\n * @param {Object<any, any>} obj - The object to iterate over.\n * @param {Function} fn - The function to call for each entry.\n *\n * @returns {void}\n */\nconst forEachEntry = (obj, fn) => {\n  const generator = obj && obj[iterator];\n\n  const _iterator = generator.call(obj);\n\n  let result;\n\n  while ((result = _iterator.next()) && !result.done) {\n    const pair = result.value;\n    fn.call(obj, pair[0], pair[1]);\n  }\n}\n\n/**\n * It takes a regular expression and a string, and returns an array of all the matches\n *\n * @param {string} regExp - The regular expression to match against.\n * @param {string} str - The string to search.\n *\n * @returns {Array<boolean>}\n */\nconst matchAll = (regExp, str) => {\n  let matches;\n  const arr = [];\n\n  while ((matches = regExp.exec(str)) !== null) {\n    arr.push(matches);\n  }\n\n  return arr;\n}\n\n/* Checking if the kindOfTest function returns true when passed an HTMLFormElement. */\nconst isHTMLForm = kindOfTest('HTMLFormElement');\n\nconst toCamelCase = str => {\n  return str.toLowerCase().replace(/[-_\\s]([a-z\\d])(\\w*)/g,\n    function replacer(m, p1, p2) {\n      return p1.toUpperCase() + p2;\n    }\n  );\n};\n\n/* Creating a function that will check if an object has a property. */\nconst hasOwnProperty = (({hasOwnProperty}) => (obj, prop) => hasOwnProperty.call(obj, prop))(Object.prototype);\n\n/**\n * Determine if a value is a RegExp object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a RegExp object, otherwise false\n */\nconst isRegExp = kindOfTest('RegExp');\n\nconst reduceDescriptors = (obj, reducer) => {\n  const descriptors = Object.getOwnPropertyDescriptors(obj);\n  const reducedDescriptors = {};\n\n  forEach(descriptors, (descriptor, name) => {\n    let ret;\n    if ((ret = reducer(descriptor, name, obj)) !== false) {\n      reducedDescriptors[name] = ret || descriptor;\n    }\n  });\n\n  Object.defineProperties(obj, reducedDescriptors);\n}\n\n/**\n * Makes all methods read-only\n * @param {Object} obj\n */\n\nconst freezeMethods = (obj) => {\n  reduceDescriptors(obj, (descriptor, name) => {\n    // skip restricted props in strict mode\n    if (isFunction(obj) && ['arguments', 'caller', 'callee'].indexOf(name) !== -1) {\n      return false;\n    }\n\n    const value = obj[name];\n\n    if (!isFunction(value)) return;\n\n    descriptor.enumerable = false;\n\n    if ('writable' in descriptor) {\n      descriptor.writable = false;\n      return;\n    }\n\n    if (!descriptor.set) {\n      descriptor.set = () => {\n        throw Error('Can not rewrite read-only method \\'' + name + '\\'');\n      };\n    }\n  });\n}\n\nconst toObjectSet = (arrayOrString, delimiter) => {\n  const obj = {};\n\n  const define = (arr) => {\n    arr.forEach(value => {\n      obj[value] = true;\n    });\n  }\n\n  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));\n\n  return obj;\n}\n\nconst noop = () => {}\n\nconst toFiniteNumber = (value, defaultValue) => {\n  return value != null && Number.isFinite(value = +value) ? value : defaultValue;\n}\n\n/**\n * If the thing is a FormData object, return true, otherwise return false.\n *\n * @param {unknown} thing - The thing to check.\n *\n * @returns {boolean}\n */\nfunction isSpecCompliantForm(thing) {\n  return !!(thing && isFunction(thing.append) && thing[toStringTag] === 'FormData' && thing[iterator]);\n}\n\nconst toJSONObject = (obj) => {\n  const stack = new Array(10);\n\n  const visit = (source, i) => {\n\n    if (isObject(source)) {\n      if (stack.indexOf(source) >= 0) {\n        return;\n      }\n\n      //Buffer check\n      if (isBuffer(source)) {\n        return source;\n      }\n\n      if(!('toJSON' in source)) {\n        stack[i] = source;\n        const target = isArray(source) ? [] : {};\n\n        forEach(source, (value, key) => {\n          const reducedValue = visit(value, i + 1);\n          !isUndefined(reducedValue) && (target[key] = reducedValue);\n        });\n\n        stack[i] = undefined;\n\n        return target;\n      }\n    }\n\n    return source;\n  }\n\n  return visit(obj, 0);\n}\n\nconst isAsyncFn = kindOfTest('AsyncFunction');\n\nconst isThenable = (thing) =>\n  thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);\n\n// original code\n// https://github.com/DigitalBrainJS/AxiosPromise/blob/16deab13710ec09779922131f3fa5954320f83ab/lib/utils.js#L11-L34\n\nconst _setImmediate = ((setImmediateSupported, postMessageSupported) => {\n  if (setImmediateSupported) {\n    return setImmediate;\n  }\n\n  return postMessageSupported ? ((token, callbacks) => {\n    _global.addEventListener(\"message\", ({source, data}) => {\n      if (source === _global && data === token) {\n        callbacks.length && callbacks.shift()();\n      }\n    }, false);\n\n    return (cb) => {\n      callbacks.push(cb);\n      _global.postMessage(token, \"*\");\n    }\n  })(`axios@${Math.random()}`, []) : (cb) => setTimeout(cb);\n})(\n  typeof setImmediate === 'function',\n  isFunction(_global.postMessage)\n);\n\nconst asap = typeof queueMicrotask !== 'undefined' ?\n  queueMicrotask.bind(_global) : ( typeof process !== 'undefined' && process.nextTick || _setImmediate);\n\n// *********************\n\n\nconst isIterable = (thing) => thing != null && isFunction(thing[iterator]);\n\n\nexport default {\n  isArray,\n  isArrayBuffer,\n  isBuffer,\n  isFormData,\n  isArrayBufferView,\n  isString,\n  isNumber,\n  isBoolean,\n  isObject,\n  isPlainObject,\n  isEmptyObject,\n  isReadableStream,\n  isRequest,\n  isResponse,\n  isHeaders,\n  isUndefined,\n  isDate,\n  isFile,\n  isBlob,\n  isRegExp,\n  isFunction,\n  isStream,\n  isURLSearchParams,\n  isTypedArray,\n  isFileList,\n  forEach,\n  merge,\n  extend,\n  trim,\n  stripBOM,\n  inherits,\n  toFlatObject,\n  kindOf,\n  kindOfTest,\n  endsWith,\n  toArray,\n  forEachEntry,\n  matchAll,\n  isHTMLForm,\n  hasOwnProperty,\n  hasOwnProp: hasOwnProperty, // an alias to avoid ESLint no-prototype-builtins detection\n  reduceDescriptors,\n  freezeMethods,\n  toObjectSet,\n  toCamelCase,\n  noop,\n  toFiniteNumber,\n  findKey,\n  global: _global,\n  isContextDefined,\n  isSpecCompliantForm,\n  toJSONObject,\n  isAsyncFn,\n  isThenable,\n  setImmediate: _setImmediate,\n  asap,\n  isIterable\n};\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA,uEAAuE;AAEvE,MAAM,EAAC,QAAQ,EAAC,GAAG,OAAO,SAAS;AACnC,MAAM,EAAC,cAAc,EAAC,GAAG;AACzB,MAAM,EAAC,QAAQ,EAAE,WAAW,EAAC,GAAG;AAEhC,MAAM,SAAS,CAAC,CAAA,QAAS,CAAA;QACrB,MAAM,MAAM,SAAS,IAAI,CAAC;QAC1B,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE;IACrE,CAAC,EAAE,OAAO,MAAM,CAAC;AAEjB,MAAM,aAAa,CAAC;IAClB,OAAO,KAAK,WAAW;IACvB,OAAO,CAAC,QAAU,OAAO,WAAW;AACtC;AAEA,MAAM,aAAa,CAAA,OAAQ,CAAA,QAAS,OAAO,UAAU;AAErD;;;;;;CAMC,GACD,MAAM,EAAC,OAAO,EAAC,GAAG;AAElB;;;;;;CAMC,GACD,MAAM,cAAc,WAAW;AAE/B;;;;;;CAMC,GACD,SAAS,SAAS,GAAG;IACnB,OAAO,QAAQ,QAAQ,CAAC,YAAY,QAAQ,IAAI,WAAW,KAAK,QAAQ,CAAC,YAAY,IAAI,WAAW,KAC/F,WAAW,IAAI,WAAW,CAAC,QAAQ,KAAK,IAAI,WAAW,CAAC,QAAQ,CAAC;AACxE;AAEA;;;;;;CAMC,GACD,MAAM,gBAAgB,WAAW;AAGjC;;;;;;CAMC,GACD,SAAS,kBAAkB,GAAG;IAC5B,IAAI;IACJ,IAAI,AAAC,OAAO,gBAAgB,eAAiB,YAAY,MAAM,EAAG;QAChE,SAAS,YAAY,MAAM,CAAC;IAC9B,OAAO;QACL,SAAS,AAAC,OAAS,IAAI,MAAM,IAAM,cAAc,IAAI,MAAM;IAC7D;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,MAAM,WAAW,WAAW;AAE5B;;;;;CAKC,GACD,MAAM,aAAa,WAAW;AAE9B;;;;;;CAMC,GACD,MAAM,WAAW,WAAW;AAE5B;;;;;;CAMC,GACD,MAAM,WAAW,CAAC,QAAU,UAAU,QAAQ,OAAO,UAAU;AAE/D;;;;;CAKC,GACD,MAAM,YAAY,CAAA,QAAS,UAAU,QAAQ,UAAU;AAEvD;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC;IACrB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,YAAY,eAAe;IACjC,OAAO,CAAC,cAAc,QAAQ,cAAc,OAAO,SAAS,IAAI,OAAO,cAAc,CAAC,eAAe,IAAI,KAAK,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,YAAY,GAAG;AAC1J;AAEA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC;IACrB,gEAAgE;IAChE,IAAI,CAAC,SAAS,QAAQ,SAAS,MAAM;QACnC,OAAO;IACT;IAEA,IAAI;QACF,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK,KAAK,OAAO,cAAc,CAAC,SAAS,OAAO,SAAS;IACzF,EAAE,OAAO,GAAG;QACV,gFAAgF;QAChF,OAAO;IACT;AACF;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,WAAW;AAE1B;;;;;;CAMC,GACD,MAAM,SAAS,WAAW;AAE1B;;;;;;CAMC,GACD,MAAM,SAAS,WAAW;AAE1B;;;;;;CAMC,GACD,MAAM,aAAa,WAAW;AAE9B;;;;;;CAMC,GACD,MAAM,WAAW,CAAC,MAAQ,SAAS,QAAQ,WAAW,IAAI,IAAI;AAE9D;;;;;;CAMC,GACD,MAAM,aAAa,CAAC;IAClB,IAAI;IACJ,OAAO,SAAS,CACd,AAAC,OAAO,aAAa,cAAc,iBAAiB,YAClD,WAAW,MAAM,MAAM,KAAK,CAC1B,CAAC,OAAO,OAAO,MAAM,MAAM,cAE1B,SAAS,YAAY,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ,OAAO,mBAC3E,CAEJ;AACF;AAEA;;;;;;CAMC,GACD,MAAM,oBAAoB,WAAW;AAErC,MAAM,CAAC,kBAAkB,WAAW,YAAY,UAAU,GAAG;IAAC;IAAkB;IAAW;IAAY;CAAU,CAAC,GAAG,CAAC;AAEtH;;;;;;CAMC,GACD,MAAM,OAAO,CAAC,MAAQ,IAAI,IAAI,GAC5B,IAAI,IAAI,KAAK,IAAI,OAAO,CAAC,sCAAsC;AAEjE;;;;;;;;;;;;;;CAcC,GACD,SAAS,QAAQ,GAAG,EAAE,EAAE,EAAE,EAAC,aAAa,KAAK,EAAC,GAAG,CAAC,CAAC;IACjD,oCAAoC;IACpC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;QAC9C;IACF;IAEA,IAAI;IACJ,IAAI;IAEJ,mDAAmD;IACnD,IAAI,OAAO,QAAQ,UAAU;QAC3B,4BAA4B,GAC5B,MAAM;YAAC;SAAI;IACb;IAEA,IAAI,QAAQ,MAAM;QAChB,4BAA4B;QAC5B,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,IAAK;YACtC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,GAAG;QAC3B;IACF,OAAO;QACL,eAAe;QACf,IAAI,SAAS,MAAM;YACjB;QACF;QAEA,2BAA2B;QAC3B,MAAM,OAAO,aAAa,OAAO,mBAAmB,CAAC,OAAO,OAAO,IAAI,CAAC;QACxE,MAAM,MAAM,KAAK,MAAM;QACvB,IAAI;QAEJ,IAAK,IAAI,GAAG,IAAI,KAAK,IAAK;YACxB,MAAM,IAAI,CAAC,EAAE;YACb,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,KAAK;QAC/B;IACF;AACF;AAEA,SAAS,QAAQ,GAAG,EAAE,GAAG;IACvB,IAAI,SAAS,MAAK;QAChB,OAAO;IACT;IAEA,MAAM,IAAI,WAAW;IACrB,MAAM,OAAO,OAAO,IAAI,CAAC;IACzB,IAAI,IAAI,KAAK,MAAM;IACnB,IAAI;IACJ,MAAO,MAAM,EAAG;QACd,OAAO,IAAI,CAAC,EAAE;QACd,IAAI,QAAQ,KAAK,WAAW,IAAI;YAC9B,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,MAAM,UAAU,CAAC;IACf,mBAAmB,GACnB,IAAI,OAAO,eAAe,aAAa,OAAO;IAC9C,OAAO,OAAO,SAAS,cAAc,OAAQ,sCAAgC;AAC/E,CAAC;AAED,MAAM,mBAAmB,CAAC,UAAY,CAAC,YAAY,YAAY,YAAY;AAE3E;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS;IACP,MAAM,EAAC,QAAQ,EAAC,GAAG,iBAAiB,IAAI,KAAK,IAAI,IAAI,CAAC;IACtD,MAAM,SAAS,CAAC;IAChB,MAAM,cAAc,CAAC,KAAK;QACxB,MAAM,YAAY,YAAY,QAAQ,QAAQ,QAAQ;QACtD,IAAI,cAAc,MAAM,CAAC,UAAU,KAAK,cAAc,MAAM;YAC1D,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU,EAAE;QAC/C,OAAO,IAAI,cAAc,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,GAAG;QAChC,OAAO,IAAI,QAAQ,MAAM;YACvB,MAAM,CAAC,UAAU,GAAG,IAAI,KAAK;QAC/B,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;QAChD,SAAS,CAAC,EAAE,IAAI,QAAQ,SAAS,CAAC,EAAE,EAAE;IACxC;IACA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,MAAM,SAAS,CAAC,GAAG,GAAG,SAAS,EAAC,UAAU,EAAC,GAAE,CAAC,CAAC;IAC7C,QAAQ,GAAG,CAAC,KAAK;QACf,IAAI,WAAW,WAAW,MAAM;YAC9B,CAAC,CAAC,IAAI,GAAG,IAAA,0JAAI,EAAC,KAAK;QACrB,OAAO;YACL,CAAC,CAAC,IAAI,GAAG;QACX;IACF,GAAG;QAAC;IAAU;IACd,OAAO;AACT;AAEA;;;;;;CAMC,GACD,MAAM,WAAW,CAAC;IAChB,IAAI,QAAQ,UAAU,CAAC,OAAO,QAAQ;QACpC,UAAU,QAAQ,KAAK,CAAC;IAC1B;IACA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,MAAM,WAAW,CAAC,aAAa,kBAAkB,OAAO;IACtD,YAAY,SAAS,GAAG,OAAO,MAAM,CAAC,iBAAiB,SAAS,EAAE;IAClE,YAAY,SAAS,CAAC,WAAW,GAAG;IACpC,OAAO,cAAc,CAAC,aAAa,SAAS;QAC1C,OAAO,iBAAiB,SAAS;IACnC;IACA,SAAS,OAAO,MAAM,CAAC,YAAY,SAAS,EAAE;AAChD;AAEA;;;;;;;;CAQC,GACD,MAAM,eAAe,CAAC,WAAW,SAAS,QAAQ;IAChD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM,SAAS,CAAC;IAEhB,UAAU,WAAW,CAAC;IACtB,6CAA6C;IAC7C,IAAI,aAAa,MAAM,OAAO;IAE9B,GAAG;QACD,QAAQ,OAAO,mBAAmB,CAAC;QACnC,IAAI,MAAM,MAAM;QAChB,MAAO,MAAM,EAAG;YACd,OAAO,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,CAAC,cAAc,WAAW,MAAM,WAAW,QAAQ,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC1E,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;gBAC/B,MAAM,CAAC,KAAK,GAAG;YACjB;QACF;QACA,YAAY,WAAW,SAAS,eAAe;IACjD,QAAS,aAAa,CAAC,CAAC,UAAU,OAAO,WAAW,QAAQ,KAAK,cAAc,OAAO,SAAS,CAAE;IAEjG,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,MAAM,WAAW,CAAC,KAAK,cAAc;IACnC,MAAM,OAAO;IACb,IAAI,aAAa,aAAa,WAAW,IAAI,MAAM,EAAE;QACnD,WAAW,IAAI,MAAM;IACvB;IACA,YAAY,aAAa,MAAM;IAC/B,MAAM,YAAY,IAAI,OAAO,CAAC,cAAc;IAC5C,OAAO,cAAc,CAAC,KAAK,cAAc;AAC3C;AAGA;;;;;;CAMC,GACD,MAAM,UAAU,CAAC;IACf,IAAI,CAAC,OAAO,OAAO;IACnB,IAAI,QAAQ,QAAQ,OAAO;IAC3B,IAAI,IAAI,MAAM,MAAM;IACpB,IAAI,CAAC,SAAS,IAAI,OAAO;IACzB,MAAM,MAAM,IAAI,MAAM;IACtB,MAAO,MAAM,EAAG;QACd,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;IACnB;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,sCAAsC;AACtC,MAAM,eAAe,CAAC,CAAA;IACpB,sCAAsC;IACtC,OAAO,CAAA;QACL,OAAO,cAAc,iBAAiB;IACxC;AACF,CAAC,EAAE,OAAO,eAAe,eAAe,eAAe;AAEvD;;;;;;;CAOC,GACD,MAAM,eAAe,CAAC,KAAK;IACzB,MAAM,YAAY,OAAO,GAAG,CAAC,SAAS;IAEtC,MAAM,YAAY,UAAU,IAAI,CAAC;IAEjC,IAAI;IAEJ,MAAO,CAAC,SAAS,UAAU,IAAI,EAAE,KAAK,CAAC,OAAO,IAAI,CAAE;QAClD,MAAM,OAAO,OAAO,KAAK;QACzB,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IAC/B;AACF;AAEA;;;;;;;CAOC,GACD,MAAM,WAAW,CAAC,QAAQ;IACxB,IAAI;IACJ,MAAM,MAAM,EAAE;IAEd,MAAO,CAAC,UAAU,OAAO,IAAI,CAAC,IAAI,MAAM,KAAM;QAC5C,IAAI,IAAI,CAAC;IACX;IAEA,OAAO;AACT;AAEA,oFAAoF,GACpF,MAAM,aAAa,WAAW;AAE9B,MAAM,cAAc,CAAA;IAClB,OAAO,IAAI,WAAW,GAAG,OAAO,CAAC,yBAC/B,SAAS,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE;QACzB,OAAO,GAAG,WAAW,KAAK;IAC5B;AAEJ;AAEA,oEAAoE,GACpE,MAAM,iBAAiB,CAAC,CAAC,EAAC,cAAc,EAAC,GAAK,CAAC,KAAK,OAAS,eAAe,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO,SAAS;AAE7G;;;;;;CAMC,GACD,MAAM,WAAW,WAAW;AAE5B,MAAM,oBAAoB,CAAC,KAAK;IAC9B,MAAM,cAAc,OAAO,yBAAyB,CAAC;IACrD,MAAM,qBAAqB,CAAC;IAE5B,QAAQ,aAAa,CAAC,YAAY;QAChC,IAAI;QACJ,IAAI,CAAC,MAAM,QAAQ,YAAY,MAAM,IAAI,MAAM,OAAO;YACpD,kBAAkB,CAAC,KAAK,GAAG,OAAO;QACpC;IACF;IAEA,OAAO,gBAAgB,CAAC,KAAK;AAC/B;AAEA;;;CAGC,GAED,MAAM,gBAAgB,CAAC;IACrB,kBAAkB,KAAK,CAAC,YAAY;QAClC,uCAAuC;QACvC,IAAI,WAAW,QAAQ;YAAC;YAAa;YAAU;SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG;YAC7E,OAAO;QACT;QAEA,MAAM,QAAQ,GAAG,CAAC,KAAK;QAEvB,IAAI,CAAC,WAAW,QAAQ;QAExB,WAAW,UAAU,GAAG;QAExB,IAAI,cAAc,YAAY;YAC5B,WAAW,QAAQ,GAAG;YACtB;QACF;QAEA,IAAI,CAAC,WAAW,GAAG,EAAE;YACnB,WAAW,GAAG,GAAG;gBACf,MAAM,MAAM,wCAAwC,OAAO;YAC7D;QACF;IACF;AACF;AAEA,MAAM,cAAc,CAAC,eAAe;IAClC,MAAM,MAAM,CAAC;IAEb,MAAM,SAAS,CAAC;QACd,IAAI,OAAO,CAAC,CAAA;YACV,GAAG,CAAC,MAAM,GAAG;QACf;IACF;IAEA,QAAQ,iBAAiB,OAAO,iBAAiB,OAAO,OAAO,eAAe,KAAK,CAAC;IAEpF,OAAO;AACT;AAEA,MAAM,OAAO,KAAO;AAEpB,MAAM,iBAAiB,CAAC,OAAO;IAC7B,OAAO,SAAS,QAAQ,OAAO,QAAQ,CAAC,QAAQ,CAAC,SAAS,QAAQ;AACpE;AAEA;;;;;;CAMC,GACD,SAAS,oBAAoB,KAAK;IAChC,OAAO,CAAC,CAAC,CAAC,SAAS,WAAW,MAAM,MAAM,KAAK,KAAK,CAAC,YAAY,KAAK,cAAc,KAAK,CAAC,SAAS;AACrG;AAEA,MAAM,eAAe,CAAC;IACpB,MAAM,QAAQ,IAAI,MAAM;IAExB,MAAM,QAAQ,CAAC,QAAQ;QAErB,IAAI,SAAS,SAAS;YACpB,IAAI,MAAM,OAAO,CAAC,WAAW,GAAG;gBAC9B;YACF;YAEA,cAAc;YACd,IAAI,SAAS,SAAS;gBACpB,OAAO;YACT;YAEA,IAAG,CAAC,CAAC,YAAY,MAAM,GAAG;gBACxB,KAAK,CAAC,EAAE,GAAG;gBACX,MAAM,SAAS,QAAQ,UAAU,EAAE,GAAG,CAAC;gBAEvC,QAAQ,QAAQ,CAAC,OAAO;oBACtB,MAAM,eAAe,MAAM,OAAO,IAAI;oBACtC,CAAC,YAAY,iBAAiB,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY;gBAC3D;gBAEA,KAAK,CAAC,EAAE,GAAG;gBAEX,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,OAAO,MAAM,KAAK;AACpB;AAEA,MAAM,YAAY,WAAW;AAE7B,MAAM,aAAa,CAAC,QAClB,SAAS,CAAC,SAAS,UAAU,WAAW,MAAM,KAAK,WAAW,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK;AAErG,gBAAgB;AAChB,oHAAoH;AAEpH,MAAM,gBAAgB,CAAC,CAAC,uBAAuB;IAC7C,IAAI,uBAAuB;QACzB,OAAO;IACT;IAEA,OAAO,uBAAuB,CAAC,CAAC,OAAO;QACrC,QAAQ,gBAAgB,CAAC,WAAW,CAAC,EAAC,MAAM,EAAE,IAAI,EAAC;YACjD,IAAI,WAAW,WAAW,SAAS,OAAO;gBACxC,UAAU,MAAM,IAAI,UAAU,KAAK;YACrC;QACF,GAAG;QAEH,OAAO,CAAC;YACN,UAAU,IAAI,CAAC;YACf,QAAQ,WAAW,CAAC,OAAO;QAC7B;IACF,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,MAAM,IAAI,EAAE,EAAE,IAAI,CAAC,KAAO,WAAW;AACxD,CAAC,EACC,OAAO,iBAAiB,YACxB,WAAW,QAAQ,WAAW;AAGhC,MAAM,OAAO,OAAO,mBAAmB,cACrC,eAAe,IAAI,CAAC,WAAa,OAAO,YAAY,eAAe,QAAQ,QAAQ,IAAI;AAEzF,wBAAwB;AAGxB,MAAM,aAAa,CAAC,QAAU,SAAS,QAAQ,WAAW,KAAK,CAAC,SAAS;uCAG1D;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,YAAY;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA,cAAc;IACd;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/AxiosError.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    this.stack = (new Error()).stack;\n  }\n\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  if (response) {\n    this.response = response;\n    this.status = response.status ? response.status : null;\n  }\n}\n\nutils.inherits(AxiosError, Error, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: utils.toJSONObject(this.config),\n      code: this.code,\n      status: this.status\n    };\n  }\n});\n\nconst prototype = AxiosError.prototype;\nconst descriptors = {};\n\n[\n  'ERR_BAD_OPTION_VALUE',\n  'ERR_BAD_OPTION',\n  'ECONNABORTED',\n  'ETIMEDOUT',\n  'ERR_NETWORK',\n  'ERR_FR_TOO_MANY_REDIRECTS',\n  'ERR_DEPRECATED',\n  'ERR_BAD_RESPONSE',\n  'ERR_BAD_REQUEST',\n  'ERR_CANCELED',\n  'ERR_NOT_SUPPORT',\n  'ERR_INVALID_URL'\n// eslint-disable-next-line func-names\n].forEach(code => {\n  descriptors[code] = {value: code};\n});\n\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {value: true});\n\n// eslint-disable-next-line func-names\nAxiosError.from = (error, code, config, request, response, customProps) => {\n  const axiosError = Object.create(prototype);\n\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  }, prop => {\n    return prop !== 'isAxiosError';\n  });\n\n  AxiosError.call(axiosError, error.message, code, config, request, response);\n\n  axiosError.cause = error;\n\n  axiosError.name = error.name;\n\n  customProps && Object.assign(axiosError, customProps);\n\n  return axiosError;\n};\n\nexport default AxiosError;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA;;;;;;;;;;CAUC,GACD,SAAS,WAAW,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ;IAC1D,MAAM,IAAI,CAAC,IAAI;IAEf,IAAI,MAAM,iBAAiB,EAAE;QAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD,OAAO;QACL,IAAI,CAAC,KAAK,GAAG,AAAC,IAAI,QAAS,KAAK;IAClC;IAEA,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,IAAI,GAAG;IACZ,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI;IACzB,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM;IAC/B,WAAW,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO;IAClC,IAAI,UAAU;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG,SAAS,MAAM,GAAG,SAAS,MAAM,GAAG;IACpD;AACF;AAEA,gJAAK,CAAC,QAAQ,CAAC,YAAY,OAAO;IAChC,QAAQ,SAAS;QACf,OAAO;YACL,WAAW;YACX,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM,IAAI,CAAC,IAAI;YACf,YAAY;YACZ,aAAa,IAAI,CAAC,WAAW;YAC7B,QAAQ,IAAI,CAAC,MAAM;YACnB,UAAU;YACV,UAAU,IAAI,CAAC,QAAQ;YACvB,YAAY,IAAI,CAAC,UAAU;YAC3B,cAAc,IAAI,CAAC,YAAY;YAC/B,OAAO,IAAI,CAAC,KAAK;YACjB,QAAQ;YACR,QAAQ,gJAAK,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM;YACtC,MAAM,IAAI,CAAC,IAAI;YACf,QAAQ,IAAI,CAAC,MAAM;QACrB;IACF;AACF;AAEA,MAAM,YAAY,WAAW,SAAS;AACtC,MAAM,cAAc,CAAC;AAErB;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CAED,CAAC,OAAO,CAAC,CAAA;IACR,WAAW,CAAC,KAAK,GAAG;QAAC,OAAO;IAAI;AAClC;AAEA,OAAO,gBAAgB,CAAC,YAAY;AACpC,OAAO,cAAc,CAAC,WAAW,gBAAgB;IAAC,OAAO;AAAI;AAE7D,sCAAsC;AACtC,WAAW,IAAI,GAAG,CAAC,OAAO,MAAM,QAAQ,SAAS,UAAU;IACzD,MAAM,aAAa,OAAO,MAAM,CAAC;IAEjC,gJAAK,CAAC,YAAY,CAAC,OAAO,YAAY,SAAS,OAAO,GAAG;QACvD,OAAO,QAAQ,MAAM,SAAS;IAChC,GAAG,CAAA;QACD,OAAO,SAAS;IAClB;IAEA,WAAW,IAAI,CAAC,YAAY,MAAM,OAAO,EAAE,MAAM,QAAQ,SAAS;IAElE,WAAW,KAAK,GAAG;IAEnB,WAAW,IAAI,GAAG,MAAM,IAAI;IAE5B,eAAe,OAAO,MAAM,CAAC,YAAY;IAEzC,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/delayed-stream/lib/delayed_stream.js"], "sourcesContent": ["var Stream = require('stream').Stream;\nvar util = require('util');\n\nmodule.exports = DelayedStream;\nfunction DelayedStream() {\n  this.source = null;\n  this.dataSize = 0;\n  this.maxDataSize = 1024 * 1024;\n  this.pauseStream = true;\n\n  this._maxDataSizeExceeded = false;\n  this._released = false;\n  this._bufferedEvents = [];\n}\nutil.inherits(DelayedStream, Stream);\n\nDelayedStream.create = function(source, options) {\n  var delayedStream = new this();\n\n  options = options || {};\n  for (var option in options) {\n    delayedStream[option] = options[option];\n  }\n\n  delayedStream.source = source;\n\n  var realEmit = source.emit;\n  source.emit = function() {\n    delayedStream._handleEmit(arguments);\n    return realEmit.apply(source, arguments);\n  };\n\n  source.on('error', function() {});\n  if (delayedStream.pauseStream) {\n    source.pause();\n  }\n\n  return delayedStream;\n};\n\nObject.defineProperty(DelayedStream.prototype, 'readable', {\n  configurable: true,\n  enumerable: true,\n  get: function() {\n    return this.source.readable;\n  }\n});\n\nDelayedStream.prototype.setEncoding = function() {\n  return this.source.setEncoding.apply(this.source, arguments);\n};\n\nDelayedStream.prototype.resume = function() {\n  if (!this._released) {\n    this.release();\n  }\n\n  this.source.resume();\n};\n\nDelayedStream.prototype.pause = function() {\n  this.source.pause();\n};\n\nDelayedStream.prototype.release = function() {\n  this._released = true;\n\n  this._bufferedEvents.forEach(function(args) {\n    this.emit.apply(this, args);\n  }.bind(this));\n  this._bufferedEvents = [];\n};\n\nDelayedStream.prototype.pipe = function() {\n  var r = Stream.prototype.pipe.apply(this, arguments);\n  this.resume();\n  return r;\n};\n\nDelayedStream.prototype._handleEmit = function(args) {\n  if (this._released) {\n    this.emit.apply(this, args);\n    return;\n  }\n\n  if (args[0] === 'data') {\n    this.dataSize += args[1].length;\n    this._checkIfMaxDataSizeExceeded();\n  }\n\n  this._bufferedEvents.push(args);\n};\n\nDelayedStream.prototype._checkIfMaxDataSizeExceeded = function() {\n  if (this._maxDataSizeExceeded) {\n    return;\n  }\n\n  if (this.dataSize <= this.maxDataSize) {\n    return;\n  }\n\n  this._maxDataSizeExceeded = true;\n  var message =\n    'DelayedStream#maxDataSize of ' + this.maxDataSize + ' bytes exceeded.'\n  this.emit('error', new Error(message));\n};\n"], "names": [], "mappings": "AAAA,IAAI,SAAS,uEAAkB,MAAM;AACrC,IAAI;AAEJ,OAAO,OAAO,GAAG;AACjB,SAAS;IACP,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG,OAAO;IAC1B,IAAI,CAAC,WAAW,GAAG;IAEnB,IAAI,CAAC,oBAAoB,GAAG;IAC5B,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,eAAe,GAAG,EAAE;AAC3B;AACA,KAAK,QAAQ,CAAC,eAAe;AAE7B,cAAc,MAAM,GAAG,SAAS,MAAM,EAAE,OAAO;IAC7C,IAAI,gBAAgB,IAAI,IAAI;IAE5B,UAAU,WAAW,CAAC;IACtB,IAAK,IAAI,UAAU,QAAS;QAC1B,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO;IACzC;IAEA,cAAc,MAAM,GAAG;IAEvB,IAAI,WAAW,OAAO,IAAI;IAC1B,OAAO,IAAI,GAAG;QACZ,cAAc,WAAW,CAAC;QAC1B,OAAO,SAAS,KAAK,CAAC,QAAQ;IAChC;IAEA,OAAO,EAAE,CAAC,SAAS,YAAY;IAC/B,IAAI,cAAc,WAAW,EAAE;QAC7B,OAAO,KAAK;IACd;IAEA,OAAO;AACT;AAEA,OAAO,cAAc,CAAC,cAAc,SAAS,EAAE,YAAY;IACzD,cAAc;IACd,YAAY;IACZ,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;IAC7B;AACF;AAEA,cAAc,SAAS,CAAC,WAAW,GAAG;IACpC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;AACpD;AAEA,cAAc,SAAS,CAAC,MAAM,GAAG;IAC/B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACnB,IAAI,CAAC,OAAO;IACd;IAEA,IAAI,CAAC,MAAM,CAAC,MAAM;AACpB;AAEA,cAAc,SAAS,CAAC,KAAK,GAAG;IAC9B,IAAI,CAAC,MAAM,CAAC,KAAK;AACnB;AAEA,cAAc,SAAS,CAAC,OAAO,GAAG;IAChC,IAAI,CAAC,SAAS,GAAG;IAEjB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA,SAAS,IAAI;QACxC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;IACxB,CAAA,EAAE,IAAI,CAAC,IAAI;IACX,IAAI,CAAC,eAAe,GAAG,EAAE;AAC3B;AAEA,cAAc,SAAS,CAAC,IAAI,GAAG;IAC7B,IAAI,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;IAC1C,IAAI,CAAC,MAAM;IACX,OAAO;AACT;AAEA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI;IACjD,IAAI,IAAI,CAAC,SAAS,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACtB;IACF;IAEA,IAAI,IAAI,CAAC,EAAE,KAAK,QAAQ;QACtB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM;QAC/B,IAAI,CAAC,2BAA2B;IAClC;IAEA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AAC5B;AAEA,cAAc,SAAS,CAAC,2BAA2B,GAAG;IACpD,IAAI,IAAI,CAAC,oBAAoB,EAAE;QAC7B;IACF;IAEA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE;QACrC;IACF;IAEA,IAAI,CAAC,oBAAoB,GAAG;IAC5B,IAAI,UACF,kCAAkC,IAAI,CAAC,WAAW,GAAG;IACvD,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/combined-stream/lib/combined_stream.js"], "sourcesContent": ["var util = require('util');\nvar Stream = require('stream').Stream;\nvar DelayedStream = require('delayed-stream');\n\nmodule.exports = CombinedStream;\nfunction CombinedStream() {\n  this.writable = false;\n  this.readable = true;\n  this.dataSize = 0;\n  this.maxDataSize = 2 * 1024 * 1024;\n  this.pauseStreams = true;\n\n  this._released = false;\n  this._streams = [];\n  this._currentStream = null;\n  this._insideLoop = false;\n  this._pendingNext = false;\n}\nutil.inherits(CombinedStream, Stream);\n\nCombinedStream.create = function(options) {\n  var combinedStream = new this();\n\n  options = options || {};\n  for (var option in options) {\n    combinedStream[option] = options[option];\n  }\n\n  return combinedStream;\n};\n\nCombinedStream.isStreamLike = function(stream) {\n  return (typeof stream !== 'function')\n    && (typeof stream !== 'string')\n    && (typeof stream !== 'boolean')\n    && (typeof stream !== 'number')\n    && (!Buffer.isBuffer(stream));\n};\n\nCombinedStream.prototype.append = function(stream) {\n  var isStreamLike = CombinedStream.isStreamLike(stream);\n\n  if (isStreamLike) {\n    if (!(stream instanceof DelayedStream)) {\n      var newStream = DelayedStream.create(stream, {\n        maxDataSize: Infinity,\n        pauseStream: this.pauseStreams,\n      });\n      stream.on('data', this._checkDataSize.bind(this));\n      stream = newStream;\n    }\n\n    this._handleErrors(stream);\n\n    if (this.pauseStreams) {\n      stream.pause();\n    }\n  }\n\n  this._streams.push(stream);\n  return this;\n};\n\nCombinedStream.prototype.pipe = function(dest, options) {\n  Stream.prototype.pipe.call(this, dest, options);\n  this.resume();\n  return dest;\n};\n\nCombinedStream.prototype._getNext = function() {\n  this._currentStream = null;\n\n  if (this._insideLoop) {\n    this._pendingNext = true;\n    return; // defer call\n  }\n\n  this._insideLoop = true;\n  try {\n    do {\n      this._pendingNext = false;\n      this._realGetNext();\n    } while (this._pendingNext);\n  } finally {\n    this._insideLoop = false;\n  }\n};\n\nCombinedStream.prototype._realGetNext = function() {\n  var stream = this._streams.shift();\n\n\n  if (typeof stream == 'undefined') {\n    this.end();\n    return;\n  }\n\n  if (typeof stream !== 'function') {\n    this._pipeNext(stream);\n    return;\n  }\n\n  var getStream = stream;\n  getStream(function(stream) {\n    var isStreamLike = CombinedStream.isStreamLike(stream);\n    if (isStreamLike) {\n      stream.on('data', this._checkDataSize.bind(this));\n      this._handleErrors(stream);\n    }\n\n    this._pipeNext(stream);\n  }.bind(this));\n};\n\nCombinedStream.prototype._pipeNext = function(stream) {\n  this._currentStream = stream;\n\n  var isStreamLike = CombinedStream.isStreamLike(stream);\n  if (isStreamLike) {\n    stream.on('end', this._getNext.bind(this));\n    stream.pipe(this, {end: false});\n    return;\n  }\n\n  var value = stream;\n  this.write(value);\n  this._getNext();\n};\n\nCombinedStream.prototype._handleErrors = function(stream) {\n  var self = this;\n  stream.on('error', function(err) {\n    self._emitError(err);\n  });\n};\n\nCombinedStream.prototype.write = function(data) {\n  this.emit('data', data);\n};\n\nCombinedStream.prototype.pause = function() {\n  if (!this.pauseStreams) {\n    return;\n  }\n\n  if(this.pauseStreams && this._currentStream && typeof(this._currentStream.pause) == 'function') this._currentStream.pause();\n  this.emit('pause');\n};\n\nCombinedStream.prototype.resume = function() {\n  if (!this._released) {\n    this._released = true;\n    this.writable = true;\n    this._getNext();\n  }\n\n  if(this.pauseStreams && this._currentStream && typeof(this._currentStream.resume) == 'function') this._currentStream.resume();\n  this.emit('resume');\n};\n\nCombinedStream.prototype.end = function() {\n  this._reset();\n  this.emit('end');\n};\n\nCombinedStream.prototype.destroy = function() {\n  this._reset();\n  this.emit('close');\n};\n\nCombinedStream.prototype._reset = function() {\n  this.writable = false;\n  this._streams = [];\n  this._currentStream = null;\n};\n\nCombinedStream.prototype._checkDataSize = function() {\n  this._updateDataSize();\n  if (this.dataSize <= this.maxDataSize) {\n    return;\n  }\n\n  var message =\n    'DelayedStream#maxDataSize of ' + this.maxDataSize + ' bytes exceeded.';\n  this._emitError(new Error(message));\n};\n\nCombinedStream.prototype._updateDataSize = function() {\n  this.dataSize = 0;\n\n  var self = this;\n  this._streams.forEach(function(stream) {\n    if (!stream.dataSize) {\n      return;\n    }\n\n    self.dataSize += stream.dataSize;\n  });\n\n  if (this._currentStream && this._currentStream.dataSize) {\n    this.dataSize += this._currentStream.dataSize;\n  }\n};\n\nCombinedStream.prototype._emitError = function(err) {\n  this._reset();\n  this.emit('error', err);\n};\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,SAAS,uEAAkB,MAAM;AACrC,IAAI;AAEJ,OAAO,OAAO,GAAG;AACjB,SAAS;IACP,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO;IAC9B,IAAI,CAAC,YAAY,GAAG;IAEpB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,YAAY,GAAG;AACtB;AACA,KAAK,QAAQ,CAAC,gBAAgB;AAE9B,eAAe,MAAM,GAAG,SAAS,OAAO;IACtC,IAAI,iBAAiB,IAAI,IAAI;IAE7B,UAAU,WAAW,CAAC;IACtB,IAAK,IAAI,UAAU,QAAS;QAC1B,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO;IAC1C;IAEA,OAAO;AACT;AAEA,eAAe,YAAY,GAAG,SAAS,MAAM;IAC3C,OAAO,AAAC,OAAO,WAAW,cACpB,OAAO,WAAW,YAClB,OAAO,WAAW,aAClB,OAAO,WAAW,YAClB,CAAC,OAAO,QAAQ,CAAC;AACzB;AAEA,eAAe,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM;IAC/C,IAAI,eAAe,eAAe,YAAY,CAAC;IAE/C,IAAI,cAAc;QAChB,IAAI,CAAC,CAAC,kBAAkB,aAAa,GAAG;YACtC,IAAI,YAAY,cAAc,MAAM,CAAC,QAAQ;gBAC3C,aAAa;gBACb,aAAa,IAAI,CAAC,YAAY;YAChC;YACA,OAAO,EAAE,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI;YAC/C,SAAS;QACX;QAEA,IAAI,CAAC,aAAa,CAAC;QAEnB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO,KAAK;QACd;IACF;IAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACnB,OAAO,IAAI;AACb;AAEA,eAAe,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,OAAO;IACpD,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;IACvC,IAAI,CAAC,MAAM;IACX,OAAO;AACT;AAEA,eAAe,SAAS,CAAC,QAAQ,GAAG;IAClC,IAAI,CAAC,cAAc,GAAG;IAEtB,IAAI,IAAI,CAAC,WAAW,EAAE;QACpB,IAAI,CAAC,YAAY,GAAG;QACpB,QAAQ,aAAa;IACvB;IAEA,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI;QACF,GAAG;YACD,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,YAAY;QACnB,QAAS,IAAI,CAAC,YAAY,CAAE;IAC9B,SAAU;QACR,IAAI,CAAC,WAAW,GAAG;IACrB;AACF;AAEA,eAAe,SAAS,CAAC,YAAY,GAAG;IACtC,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,KAAK;IAGhC,IAAI,OAAO,UAAU,aAAa;QAChC,IAAI,CAAC,GAAG;QACR;IACF;IAEA,IAAI,OAAO,WAAW,YAAY;QAChC,IAAI,CAAC,SAAS,CAAC;QACf;IACF;IAEA,IAAI,YAAY;IAChB,UAAU,CAAA,SAAS,MAAM;QACvB,IAAI,eAAe,eAAe,YAAY,CAAC;QAC/C,IAAI,cAAc;YAChB,OAAO,EAAE,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI;YAC/C,IAAI,CAAC,aAAa,CAAC;QACrB;QAEA,IAAI,CAAC,SAAS,CAAC;IACjB,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AAEA,eAAe,SAAS,CAAC,SAAS,GAAG,SAAS,MAAM;IAClD,IAAI,CAAC,cAAc,GAAG;IAEtB,IAAI,eAAe,eAAe,YAAY,CAAC;IAC/C,IAAI,cAAc;QAChB,OAAO,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACxC,OAAO,IAAI,CAAC,IAAI,EAAE;YAAC,KAAK;QAAK;QAC7B;IACF;IAEA,IAAI,QAAQ;IACZ,IAAI,CAAC,KAAK,CAAC;IACX,IAAI,CAAC,QAAQ;AACf;AAEA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAS,MAAM;IACtD,IAAI,OAAO,IAAI;IACf,OAAO,EAAE,CAAC,SAAS,SAAS,GAAG;QAC7B,KAAK,UAAU,CAAC;IAClB;AACF;AAEA,eAAe,SAAS,CAAC,KAAK,GAAG,SAAS,IAAI;IAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ;AACpB;AAEA,eAAe,SAAS,CAAC,KAAK,GAAG;IAC/B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;QACtB;IACF;IAEA,IAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,IAAK,YAAY,IAAI,CAAC,cAAc,CAAC,KAAK;IACzH,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA,eAAe,SAAS,CAAC,MAAM,GAAG;IAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACnB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ;IACf;IAEA,IAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,IAAK,YAAY,IAAI,CAAC,cAAc,CAAC,MAAM;IAC3H,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA,eAAe,SAAS,CAAC,GAAG,GAAG;IAC7B,IAAI,CAAC,MAAM;IACX,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA,eAAe,SAAS,CAAC,OAAO,GAAG;IACjC,IAAI,CAAC,MAAM;IACX,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA,eAAe,SAAS,CAAC,MAAM,GAAG;IAChC,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC,cAAc,GAAG;AACxB;AAEA,eAAe,SAAS,CAAC,cAAc,GAAG;IACxC,IAAI,CAAC,eAAe;IACpB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE;QACrC;IACF;IAEA,IAAI,UACF,kCAAkC,IAAI,CAAC,WAAW,GAAG;IACvD,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM;AAC5B;AAEA,eAAe,SAAS,CAAC,eAAe,GAAG;IACzC,IAAI,CAAC,QAAQ,GAAG;IAEhB,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,MAAM;QACnC,IAAI,CAAC,OAAO,QAAQ,EAAE;YACpB;QACF;QAEA,KAAK,QAAQ,IAAI,OAAO,QAAQ;IAClC;IAEA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QACvD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ;IAC/C;AACF;AAEA,eAAe,SAAS,CAAC,UAAU,GAAG,SAAS,GAAG;IAChD,IAAI,CAAC,MAAM;IACX,IAAI,CAAC,IAAI,CAAC,SAAS;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/mime-db/index.js"], "sourcesContent": ["/*!\n * mime-db\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015-2022 <PERSON>\n * MIT Licensed\n */\n\n/**\n * Module exports.\n */\n\nmodule.exports = require('./db.json')\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;CAEC,GAED,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/mime-types/index.js"], "sourcesContent": ["/*!\n * mime-types\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar db = require('mime-db')\nvar extname = require('path').extname\n\n/**\n * Module variables.\n * @private\n */\n\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/\nvar TEXT_TYPE_REGEXP = /^text\\//i\n\n/**\n * Module exports.\n * @public\n */\n\nexports.charset = charset\nexports.charsets = { lookup: charset }\nexports.contentType = contentType\nexports.extension = extension\nexports.extensions = Object.create(null)\nexports.lookup = lookup\nexports.types = Object.create(null)\n\n// Populate the extensions/types maps\npopulateMaps(exports.extensions, exports.types)\n\n/**\n * Get the default charset for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction charset (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n  var mime = match && db[match[1].toLowerCase()]\n\n  if (mime && mime.charset) {\n    return mime.charset\n  }\n\n  // default text/* to utf-8\n  if (match && TEXT_TYPE_REGEXP.test(match[1])) {\n    return 'UTF-8'\n  }\n\n  return false\n}\n\n/**\n * Create a full Content-Type header given a MIME type or extension.\n *\n * @param {string} str\n * @return {boolean|string}\n */\n\nfunction contentType (str) {\n  // TODO: should this even be in this module?\n  if (!str || typeof str !== 'string') {\n    return false\n  }\n\n  var mime = str.indexOf('/') === -1\n    ? exports.lookup(str)\n    : str\n\n  if (!mime) {\n    return false\n  }\n\n  // TODO: use content-type or other module\n  if (mime.indexOf('charset') === -1) {\n    var charset = exports.charset(mime)\n    if (charset) mime += '; charset=' + charset.toLowerCase()\n  }\n\n  return mime\n}\n\n/**\n * Get the default extension for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction extension (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n\n  // get extensions\n  var exts = match && exports.extensions[match[1].toLowerCase()]\n\n  if (!exts || !exts.length) {\n    return false\n  }\n\n  return exts[0]\n}\n\n/**\n * Lookup the MIME type for a file path/extension.\n *\n * @param {string} path\n * @return {boolean|string}\n */\n\nfunction lookup (path) {\n  if (!path || typeof path !== 'string') {\n    return false\n  }\n\n  // get the extension (\"ext\" or \".ext\" or full path)\n  var extension = extname('x.' + path)\n    .toLowerCase()\n    .substr(1)\n\n  if (!extension) {\n    return false\n  }\n\n  return exports.types[extension] || false\n}\n\n/**\n * Populate the extensions and types maps.\n * @private\n */\n\nfunction populateMaps (extensions, types) {\n  // source preference (least -> most)\n  var preference = ['nginx', 'apache', undefined, 'iana']\n\n  Object.keys(db).forEach(function forEachMimeType (type) {\n    var mime = db[type]\n    var exts = mime.extensions\n\n    if (!exts || !exts.length) {\n      return\n    }\n\n    // mime -> extensions\n    extensions[type] = exts\n\n    // extension -> mime\n    for (var i = 0; i < exts.length; i++) {\n      var extension = exts[i]\n\n      if (types[extension]) {\n        var from = preference.indexOf(db[types[extension]].source)\n        var to = preference.indexOf(mime.source)\n\n        if (types[extension] !== 'application/octet-stream' &&\n          (from > to || (from === to && types[extension].substr(0, 12) === 'application/'))) {\n          // skip the remapping\n          continue\n        }\n      }\n\n      // set the extension -> mime\n      types[extension] = type\n    }\n  })\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAID;;;CAGC,GAED,IAAI;AACJ,IAAI,UAAU,mEAAgB,OAAO;AAErC;;;CAGC,GAED,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AAEvB;;;CAGC,GAED,QAAQ,OAAO,GAAG;AAClB,QAAQ,QAAQ,GAAG;IAAE,QAAQ;AAAQ;AACrC,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG;AACpB,QAAQ,UAAU,GAAG,OAAO,MAAM,CAAC;AACnC,QAAQ,MAAM,GAAG;AACjB,QAAQ,KAAK,GAAG,OAAO,MAAM,CAAC;AAE9B,qCAAqC;AACrC,aAAa,QAAQ,UAAU,EAAE,QAAQ,KAAK;AAE9C;;;;;CAKC,GAED,SAAS,QAAS,IAAI;IACpB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,QAAQ,oBAAoB,IAAI,CAAC;IACrC,IAAI,OAAO,SAAS,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG;IAE9C,IAAI,QAAQ,KAAK,OAAO,EAAE;QACxB,OAAO,KAAK,OAAO;IACrB;IAEA,0BAA0B;IAC1B,IAAI,SAAS,iBAAiB,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG;QAC5C,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GAED,SAAS,YAAa,GAAG;IACvB,4CAA4C;IAC5C,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,IAC7B,QAAQ,MAAM,CAAC,OACf;IAEJ,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,yCAAyC;IACzC,IAAI,KAAK,OAAO,CAAC,eAAe,CAAC,GAAG;QAClC,IAAI,UAAU,QAAQ,OAAO,CAAC;QAC9B,IAAI,SAAS,QAAQ,eAAe,QAAQ,WAAW;IACzD;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GAED,SAAS,UAAW,IAAI;IACtB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,QAAQ,oBAAoB,IAAI,CAAC;IAErC,iBAAiB;IACjB,IAAI,OAAO,SAAS,QAAQ,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG;IAE9D,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;QACzB,OAAO;IACT;IAEA,OAAO,IAAI,CAAC,EAAE;AAChB;AAEA;;;;;CAKC,GAED,SAAS,OAAQ,IAAI;IACnB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;IACT;IAEA,mDAAmD;IACnD,IAAI,YAAY,QAAQ,OAAO,MAC5B,WAAW,GACX,MAAM,CAAC;IAEV,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,OAAO,QAAQ,KAAK,CAAC,UAAU,IAAI;AACrC;AAEA;;;CAGC,GAED,SAAS,aAAc,UAAU,EAAE,KAAK;IACtC,oCAAoC;IACpC,IAAI,aAAa;QAAC;QAAS;QAAU;QAAW;KAAO;IAEvD,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,SAAS,gBAAiB,IAAI;QACpD,IAAI,OAAO,EAAE,CAAC,KAAK;QACnB,IAAI,OAAO,KAAK,UAAU;QAE1B,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;YACzB;QACF;QAEA,qBAAqB;QACrB,UAAU,CAAC,KAAK,GAAG;QAEnB,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,YAAY,IAAI,CAAC,EAAE;YAEvB,IAAI,KAAK,CAAC,UAAU,EAAE;gBACpB,IAAI,OAAO,WAAW,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM;gBACzD,IAAI,KAAK,WAAW,OAAO,CAAC,KAAK,MAAM;gBAEvC,IAAI,KAAK,CAAC,UAAU,KAAK,8BACvB,CAAC,OAAO,MAAO,SAAS,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,QAAQ,cAAe,GAAG;oBAEnF;gBACF;YACF;YAEA,4BAA4B;YAC5B,KAAK,CAAC,UAAU,GAAG;QACrB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/lib/defer.js"], "sourcesContent": ["module.exports = defer;\n\n/**\n * Runs provided function on next iteration of the event loop\n *\n * @param {function} fn - function to run\n */\nfunction defer(fn)\n{\n  var nextTick = typeof setImmediate == 'function'\n    ? setImmediate\n    : (\n      typeof process == 'object' && typeof process.nextTick == 'function'\n      ? process.nextTick\n      : null\n    );\n\n  if (nextTick)\n  {\n    nextTick(fn);\n  }\n  else\n  {\n    setTimeout(fn, 0);\n  }\n}\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG;AAEjB;;;;CAIC,GACD,SAAS,MAAM,EAAE;IAEf,IAAI,WAAW,OAAO,gBAAgB,aAClC,eAEA,OAAO,WAAW,YAAY,OAAO,QAAQ,QAAQ,IAAI,aACvD,QAAQ,QAAQ,GAChB;IAGN,IAAI,UACJ;QACE,SAAS;IACX,OAEA;QACE,WAAW,IAAI;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/lib/async.js"], "sourcesContent": ["var defer = require('./defer.js');\n\n// API\nmodule.exports = async;\n\n/**\n * Runs provided callback asynchronously\n * even if callback itself is not\n *\n * @param   {function} callback - callback to invoke\n * @returns {function} - augmented callback\n */\nfunction async(callback)\n{\n  var isAsync = false;\n\n  // check if async happened\n  defer(function() { isAsync = true; });\n\n  return function async_callback(err, result)\n  {\n    if (isAsync)\n    {\n      callback(err, result);\n    }\n    else\n    {\n      defer(function nextTick_callback()\n      {\n        callback(err, result);\n      });\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,MAAM;AACN,OAAO,OAAO,GAAG;AAEjB;;;;;;CAMC,GACD,SAAS,MAAM,QAAQ;IAErB,IAAI,UAAU;IAEd,0BAA0B;IAC1B,MAAM;QAAa,UAAU;IAAM;IAEnC,OAAO,SAAS,eAAe,GAAG,EAAE,MAAM;QAExC,IAAI,SACJ;YACE,SAAS,KAAK;QAChB,OAEA;YACE,MAAM,SAAS;gBAEb,SAAS,KAAK;YAChB;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/lib/abort.js"], "sourcesContent": ["// API\nmodule.exports = abort;\n\n/**\n * Aborts leftover active jobs\n *\n * @param {object} state - current state object\n */\nfunction abort(state)\n{\n  Object.keys(state.jobs).forEach(clean.bind(state));\n\n  // reset leftover jobs\n  state.jobs = {};\n}\n\n/**\n * Cleans up leftover job by invoking abort function for the provided job id\n *\n * @this  state\n * @param {string|number} key - job id to abort\n */\nfunction clean(key)\n{\n  if (typeof this.jobs[key] == 'function')\n  {\n    this.jobs[key]();\n  }\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,OAAO,OAAO,GAAG;AAEjB;;;;CAIC,GACD,SAAS,MAAM,KAAK;IAElB,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC;IAE3C,sBAAsB;IACtB,MAAM,IAAI,GAAG,CAAC;AAChB;AAEA;;;;;CAKC,GACD,SAAS,MAAM,GAAG;IAEhB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,YAC7B;QACE,IAAI,CAAC,IAAI,CAAC,IAAI;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1804, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/lib/iterate.js"], "sourcesContent": ["var async = require('./async.js')\n  , abort = require('./abort.js')\n  ;\n\n// API\nmodule.exports = iterate;\n\n/**\n * Iterates over each job object\n *\n * @param {array|object} list - array or object (named list) to iterate over\n * @param {function} iterator - iterator to run\n * @param {object} state - current job status\n * @param {function} callback - invoked when all elements processed\n */\nfunction iterate(list, iterator, state, callback)\n{\n  // store current index\n  var key = state['keyedList'] ? state['keyedList'][state.index] : state.index;\n\n  state.jobs[key] = runJob(iterator, key, list[key], function(error, output)\n  {\n    // don't repeat yourself\n    // skip secondary callbacks\n    if (!(key in state.jobs))\n    {\n      return;\n    }\n\n    // clean up jobs\n    delete state.jobs[key];\n\n    if (error)\n    {\n      // don't process rest of the results\n      // stop still active jobs\n      // and reset the list\n      abort(state);\n    }\n    else\n    {\n      state.results[key] = output;\n    }\n\n    // return salvaged results\n    callback(error, state.results);\n  });\n}\n\n/**\n * Runs iterator over provided job element\n *\n * @param   {function} iterator - iterator to invoke\n * @param   {string|number} key - key/index of the element in the list of jobs\n * @param   {mixed} item - job description\n * @param   {function} callback - invoked after iterator is done with the job\n * @returns {function|mixed} - job abort function or something else\n */\nfunction runJob(iterator, key, item, callback)\n{\n  var aborter;\n\n  // allow shortcut if iterator expects only two arguments\n  if (iterator.length == 2)\n  {\n    aborter = iterator(item, async(callback));\n  }\n  // otherwise go with full three arguments\n  else\n  {\n    aborter = iterator(item, key, async(callback));\n  }\n\n  return aborter;\n}\n"], "names": [], "mappings": "AAAA,IAAI,wGACA;AAGJ,MAAM;AACN,OAAO,OAAO,GAAG;AAEjB;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;IAE9C,sBAAsB;IACtB,IAAI,MAAM,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK;IAE5E,MAAM,IAAI,CAAC,IAAI,GAAG,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,EAAE,SAAS,KAAK,EAAE,MAAM;QAEvE,wBAAwB;QACxB,2BAA2B;QAC3B,IAAI,CAAC,CAAC,OAAO,MAAM,IAAI,GACvB;YACE;QACF;QAEA,gBAAgB;QAChB,OAAO,MAAM,IAAI,CAAC,IAAI;QAEtB,IAAI,OACJ;YACE,oCAAoC;YACpC,yBAAyB;YACzB,qBAAqB;YACrB,MAAM;QACR,OAEA;YACE,MAAM,OAAO,CAAC,IAAI,GAAG;QACvB;QAEA,0BAA0B;QAC1B,SAAS,OAAO,MAAM,OAAO;IAC/B;AACF;AAEA;;;;;;;;CAQC,GACD,SAAS,OAAO,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ;IAE3C,IAAI;IAEJ,wDAAwD;IACxD,IAAI,SAAS,MAAM,IAAI,GACvB;QACE,UAAU,SAAS,MAAM,MAAM;IACjC,OAGA;QACE,UAAU,SAAS,MAAM,KAAK,MAAM;IACtC;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1858, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/lib/state.js"], "sourcesContent": ["// API\nmodule.exports = state;\n\n/**\n * Creates initial state object\n * for iteration over list\n *\n * @param   {array|object} list - list to iterate over\n * @param   {function|null} sortMethod - function to use for keys sort,\n *                                     or `null` to keep them as is\n * @returns {object} - initial state object\n */\nfunction state(list, sortMethod)\n{\n  var isNamedList = !Array.isArray(list)\n    , initState =\n    {\n      index    : 0,\n      keyedList: isNamedList || sortMethod ? Object.keys(list) : null,\n      jobs     : {},\n      results  : isNamedList ? {} : [],\n      size     : isNamedList ? Object.keys(list).length : list.length\n    }\n    ;\n\n  if (sortMethod)\n  {\n    // sort array keys based on it's values\n    // sort object's keys just on own merit\n    initState.keyedList.sort(isNamedList ? sortMethod : function(a, b)\n    {\n      return sortMethod(list[a], list[b]);\n    });\n  }\n\n  return initState;\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,OAAO,OAAO,GAAG;AAEjB;;;;;;;;CAQC,GACD,SAAS,MAAM,IAAI,EAAE,UAAU;IAE7B,IAAI,cAAc,CAAC,MAAM,OAAO,CAAC,OAC7B,YACF;QACE,OAAW;QACX,WAAW,eAAe,aAAa,OAAO,IAAI,CAAC,QAAQ;QAC3D,MAAW,CAAC;QACZ,SAAW,cAAc,CAAC,IAAI,EAAE;QAChC,MAAW,cAAc,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,KAAK,MAAM;IACjE;IAGF,IAAI,YACJ;QACE,uCAAuC;QACvC,uCAAuC;QACvC,UAAU,SAAS,CAAC,IAAI,CAAC,cAAc,aAAa,SAAS,CAAC,EAAE,CAAC;YAE/D,OAAO,WAAW,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QACpC;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/lib/terminator.js"], "sourcesContent": ["var abort = require('./abort.js')\n  , async = require('./async.js')\n  ;\n\n// API\nmodule.exports = terminator;\n\n/**\n * Terminates jobs in the attached state context\n *\n * @this  AsyncKitState#\n * @param {function} callback - final callback to invoke after termination\n */\nfunction terminator(callback)\n{\n  if (!Object.keys(this.jobs).length)\n  {\n    return;\n  }\n\n  // fast forward iteration index\n  this.index = this.size;\n\n  // abort jobs\n  abort(this);\n\n  // send back results we have so far\n  async(callback)(null, this.results);\n}\n"], "names": [], "mappings": "AAAA,IAAI,wGACA;AAGJ,MAAM;AACN,OAAO,OAAO,GAAG;AAEjB;;;;;CAKC,GACD,SAAS,WAAW,QAAQ;IAE1B,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAClC;QACE;IACF;IAEA,+BAA+B;IAC/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;IAEtB,aAAa;IACb,MAAM,IAAI;IAEV,mCAAmC;IACnC,MAAM,UAAU,MAAM,IAAI,CAAC,OAAO;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/parallel.js"], "sourcesContent": ["var iterate    = require('./lib/iterate.js')\n  , initState  = require('./lib/state.js')\n  , terminator = require('./lib/terminator.js')\n  ;\n\n// Public API\nmodule.exports = parallel;\n\n/**\n * Runs iterator over provided array elements in parallel\n *\n * @param   {array|object} list - array or object (named list) to iterate over\n * @param   {function} iterator - iterator to run\n * @param   {function} callback - invoked when all elements processed\n * @returns {function} - jobs terminator\n */\nfunction parallel(list, iterator, callback)\n{\n  var state = initState(list);\n\n  while (state.index < (state['keyedList'] || list).length)\n  {\n    iterate(list, iterator, state, function(error, result)\n    {\n      if (error)\n      {\n        callback(error, result);\n        return;\n      }\n\n      // looks like it's the last one\n      if (Object.keys(state.jobs).length === 0)\n      {\n        callback(null, state.results);\n        return;\n      }\n    });\n\n    state.index++;\n  }\n\n  return terminator.bind(state, callback);\n}\n"], "names": [], "mappings": "AAAA,IAAI,4GACA,4GACA;AAGJ,aAAa;AACb,OAAO,OAAO,GAAG;AAEjB;;;;;;;CAOC,GACD,SAAS,SAAS,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAExC,IAAI,QAAQ,UAAU;IAEtB,MAAO,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,EAAE,MAAM,CACxD;QACE,QAAQ,MAAM,UAAU,OAAO,SAAS,KAAK,EAAE,MAAM;YAEnD,IAAI,OACJ;gBACE,SAAS,OAAO;gBAChB;YACF;YAEA,+BAA+B;YAC/B,IAAI,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK,GACvC;gBACE,SAAS,MAAM,MAAM,OAAO;gBAC5B;YACF;QACF;QAEA,MAAM,KAAK;IACb;IAEA,OAAO,WAAW,IAAI,CAAC,OAAO;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/serialOrdered.js"], "sourcesContent": ["var iterate    = require('./lib/iterate.js')\n  , initState  = require('./lib/state.js')\n  , terminator = require('./lib/terminator.js')\n  ;\n\n// Public API\nmodule.exports = serialOrdered;\n// sorting helpers\nmodule.exports.ascending  = ascending;\nmodule.exports.descending = descending;\n\n/**\n * Runs iterator over provided sorted array elements in series\n *\n * @param   {array|object} list - array or object (named list) to iterate over\n * @param   {function} iterator - iterator to run\n * @param   {function} sortMethod - custom sort function\n * @param   {function} callback - invoked when all elements processed\n * @returns {function} - jobs terminator\n */\nfunction serialOrdered(list, iterator, sortMethod, callback)\n{\n  var state = initState(list, sortMethod);\n\n  iterate(list, iterator, state, function iteratorHandler(error, result)\n  {\n    if (error)\n    {\n      callback(error, result);\n      return;\n    }\n\n    state.index++;\n\n    // are we there yet?\n    if (state.index < (state['keyedList'] || list).length)\n    {\n      iterate(list, iterator, state, iteratorHandler);\n      return;\n    }\n\n    // done here\n    callback(null, state.results);\n  });\n\n  return terminator.bind(state, callback);\n}\n\n/*\n * -- Sort methods\n */\n\n/**\n * sort helper to sort array elements in ascending order\n *\n * @param   {mixed} a - an item to compare\n * @param   {mixed} b - an item to compare\n * @returns {number} - comparison result\n */\nfunction ascending(a, b)\n{\n  return a < b ? -1 : a > b ? 1 : 0;\n}\n\n/**\n * sort helper to sort array elements in descending order\n *\n * @param   {mixed} a - an item to compare\n * @param   {mixed} b - an item to compare\n * @returns {number} - comparison result\n */\nfunction descending(a, b)\n{\n  return -1 * ascending(a, b);\n}\n"], "names": [], "mappings": "AAAA,IAAI,4GACA,4GACA;AAGJ,aAAa;AACb,OAAO,OAAO,GAAG;AACjB,kBAAkB;AAClB,OAAO,OAAO,CAAC,SAAS,GAAI;AAC5B,OAAO,OAAO,CAAC,UAAU,GAAG;AAE5B;;;;;;;;CAQC,GACD,SAAS,cAAc,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ;IAEzD,IAAI,QAAQ,UAAU,MAAM;IAE5B,QAAQ,MAAM,UAAU,OAAO,SAAS,gBAAgB,KAAK,EAAE,MAAM;QAEnE,IAAI,OACJ;YACE,SAAS,OAAO;YAChB;QACF;QAEA,MAAM,KAAK;QAEX,oBAAoB;QACpB,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,EAAE,MAAM,EACrD;YACE,QAAQ,MAAM,UAAU,OAAO;YAC/B;QACF;QAEA,YAAY;QACZ,SAAS,MAAM,MAAM,OAAO;IAC9B;IAEA,OAAO,WAAW,IAAI,CAAC,OAAO;AAChC;AAEA;;CAEC,GAED;;;;;;CAMC,GACD,SAAS,UAAU,CAAC,EAAE,CAAC;IAErB,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI;AAClC;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,CAAC,EAAE,CAAC;IAEtB,OAAO,CAAC,IAAI,UAAU,GAAG;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/serial.js"], "sourcesContent": ["var serialOrdered = require('./serialOrdered.js');\n\n// Public API\nmodule.exports = serial;\n\n/**\n * Runs iterator over provided array elements in series\n *\n * @param   {array|object} list - array or object (named list) to iterate over\n * @param   {function} iterator - iterator to run\n * @param   {function} callback - invoked when all elements processed\n * @returns {function} - jobs terminator\n */\nfunction serial(list, iterator, callback)\n{\n  return serialOrdered(list, iterator, null, callback);\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,aAAa;AACb,OAAO,OAAO,GAAG;AAEjB;;;;;;;CAOC,GACD,SAAS,OAAO,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAEtC,OAAO,cAAc,MAAM,UAAU,MAAM;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/asynckit/index.js"], "sourcesContent": ["module.exports =\n{\n  parallel      : require('./parallel.js'),\n  serial        : require('./serial.js'),\n  serialOrdered : require('./serialOrdered.js')\n};\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GACd;IACE,QAAQ;IACR,MAAM;IACN,aAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-object-atoms/index.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('.')} */\nmodule.exports = Object;\n"], "names": [], "mappings": "AAEA,wBAAwB,GACxB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-errors/index.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('.')} */\nmodule.exports = Error;\n"], "names": [], "mappings": "AAEA,wBAAwB,GACxB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-errors/eval.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./eval')} */\nmodule.exports = EvalError;\n"], "names": [], "mappings": "AAEA,6BAA6B,GAC7B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-errors/range.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./range')} */\nmodule.exports = RangeError;\n"], "names": [], "mappings": "AAEA,8BAA8B,GAC9B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-errors/ref.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./ref')} */\nmodule.exports = ReferenceError;\n"], "names": [], "mappings": "AAEA,4BAA4B,GAC5B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-errors/syntax.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./syntax')} */\nmodule.exports = SyntaxError;\n"], "names": [], "mappings": "AAEA,+BAA+B,GAC/B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-errors/type.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./type')} */\nmodule.exports = TypeError;\n"], "names": [], "mappings": "AAEA,6BAA6B,GAC7B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-errors/uri.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./uri')} */\nmodule.exports = URIError;\n"], "names": [], "mappings": "AAEA,4BAA4B,GAC5B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/abs.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./abs')} */\nmodule.exports = Math.abs;\n"], "names": [], "mappings": "AAEA,4BAA4B,GAC5B,OAAO,OAAO,GAAG,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/floor.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./floor')} */\nmodule.exports = Math.floor;\n"], "names": [], "mappings": "AAEA,8BAA8B,GAC9B,OAAO,OAAO,GAAG,KAAK,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/max.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./max')} */\nmodule.exports = Math.max;\n"], "names": [], "mappings": "AAEA,4BAA4B,GAC5B,OAAO,OAAO,GAAG,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/min.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./min')} */\nmodule.exports = Math.min;\n"], "names": [], "mappings": "AAEA,4BAA4B,GAC5B,OAAO,OAAO,GAAG,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/pow.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./pow')} */\nmodule.exports = Math.pow;\n"], "names": [], "mappings": "AAEA,4BAA4B,GAC5B,OAAO,OAAO,GAAG,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/round.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./round')} */\nmodule.exports = Math.round;\n"], "names": [], "mappings": "AAEA,8BAA8B,GAC9B,OAAO,OAAO,GAAG,KAAK,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/isNaN.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./isNaN')} */\nmodule.exports = Number.isNaN || function isNaN(a) {\n\treturn a !== a;\n};\n"], "names": [], "mappings": "AAEA,8BAA8B,GAC9B,OAAO,OAAO,GAAG,OAAO,KAAK,IAAI,SAAS,MAAM,CAAC;IAChD,OAAO,MAAM;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2098, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/math-intrinsics/sign.js"], "sourcesContent": ["'use strict';\n\nvar $isNaN = require('./isNaN');\n\n/** @type {import('./sign')} */\nmodule.exports = function sign(number) {\n\tif ($isNaN(number) || number === 0) {\n\t\treturn number;\n\t}\n\treturn number < 0 ? -1 : +1;\n};\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,6BAA6B,GAC7B,OAAO,OAAO,GAAG,SAAS,KAAK,MAAM;IACpC,IAAI,OAAO,WAAW,WAAW,GAAG;QACnC,OAAO;IACR;IACA,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/gopd/gOPD.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./gOPD')} */\nmodule.exports = Object.getOwnPropertyDescriptor;\n"], "names": [], "mappings": "AAEA,6BAA6B,GAC7B,OAAO,OAAO,GAAG,OAAO,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/gopd/index.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('.')} */\nvar $gOPD = require('./gOPD');\n\nif ($gOPD) {\n\ttry {\n\t\t$gOPD([], 'length');\n\t} catch (e) {\n\t\t// IE 8 has a broken gOPD\n\t\t$gOPD = null;\n\t}\n}\n\nmodule.exports = $gOPD;\n"], "names": [], "mappings": "AAEA,wBAAwB,GACxB,IAAI;AAEJ,IAAI,OAAO;IACV,IAAI;QACH,MAAM,EAAE,EAAE;IACX,EAAE,OAAO,GAAG;QACX,yBAAyB;QACzB,QAAQ;IACT;AACD;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-define-property/index.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('.')} */\nvar $defineProperty = Object.defineProperty || false;\nif ($defineProperty) {\n\ttry {\n\t\t$defineProperty({}, 'a', { value: 1 });\n\t} catch (e) {\n\t\t// IE 8 has a broken defineProperty\n\t\t$defineProperty = false;\n\t}\n}\n\nmodule.exports = $defineProperty;\n"], "names": [], "mappings": "AAEA,wBAAwB,GACxB,IAAI,kBAAkB,OAAO,cAAc,IAAI;AAC/C,IAAI,iBAAiB;IACpB,IAAI;QACH,gBAAgB,CAAC,GAAG,KAAK;YAAE,OAAO;QAAE;IACrC,EAAE,OAAO,GAAG;QACX,mCAAmC;QACnC,kBAAkB;IACnB;AACD;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/has-symbols/shams.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./shams')} */\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n\tif (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }\n\tif (typeof Symbol.iterator === 'symbol') { return true; }\n\n\t/** @type {{ [k in symbol]?: unknown }} */\n\tvar obj = {};\n\tvar sym = Symbol('test');\n\tvar symObj = Object(sym);\n\tif (typeof sym === 'string') { return false; }\n\n\tif (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }\n\tif (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }\n\n\t// temp disabled per https://github.com/ljharb/object.assign/issues/17\n\t// if (sym instanceof Symbol) { return false; }\n\t// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n\t// if (!(symObj instanceof Symbol)) { return false; }\n\n\t// if (typeof Symbol.prototype.toString !== 'function') { return false; }\n\t// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n\tvar symVal = 42;\n\tobj[sym] = symVal;\n\tfor (var _ in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n\tif (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }\n\n\tif (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }\n\n\tvar syms = Object.getOwnPropertySymbols(obj);\n\tif (syms.length !== 1 || syms[0] !== sym) { return false; }\n\n\tif (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }\n\n\tif (typeof Object.getOwnPropertyDescriptor === 'function') {\n\t\t// eslint-disable-next-line no-extra-parens\n\t\tvar descriptor = /** @type {PropertyDescriptor} */ (Object.getOwnPropertyDescriptor(obj, sym));\n\t\tif (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }\n\t}\n\n\treturn true;\n};\n"], "names": [], "mappings": "AAEA,8BAA8B,GAC9B,uDAAuD,GACvD,OAAO,OAAO,GAAG,SAAS;IACzB,IAAI,OAAO,WAAW,cAAc,OAAO,OAAO,qBAAqB,KAAK,YAAY;QAAE,OAAO;IAAO;IACxG,IAAI,OAAO,OAAO,QAAQ,KAAK,UAAU;QAAE,OAAO;IAAM;IAExD,wCAAwC,GACxC,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,OAAO;IACjB,IAAI,SAAS,OAAO;IACpB,IAAI,OAAO,QAAQ,UAAU;QAAE,OAAO;IAAO;IAE7C,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,mBAAmB;QAAE,OAAO;IAAO;IAC/E,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,mBAAmB;QAAE,OAAO;IAAO;IAElF,sEAAsE;IACtE,+CAA+C;IAC/C,uFAAuF;IACvF,qDAAqD;IAErD,yEAAyE;IACzE,6EAA6E;IAE7E,IAAI,SAAS;IACb,GAAG,CAAC,IAAI,GAAG;IACX,IAAK,IAAI,KAAK,IAAK;QAAE,OAAO;IAAO,EAAE,gEAAgE;IACrG,IAAI,OAAO,OAAO,IAAI,KAAK,cAAc,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG;QAAE,OAAO;IAAO;IAExF,IAAI,OAAO,OAAO,mBAAmB,KAAK,cAAc,OAAO,mBAAmB,CAAC,KAAK,MAAM,KAAK,GAAG;QAAE,OAAO;IAAO;IAEtH,IAAI,OAAO,OAAO,qBAAqB,CAAC;IACxC,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK;QAAE,OAAO;IAAO;IAE1D,IAAI,CAAC,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,MAAM;QAAE,OAAO;IAAO;IAE3E,IAAI,OAAO,OAAO,wBAAwB,KAAK,YAAY;QAC1D,2CAA2C;QAC3C,IAAI,aAAgD,OAAO,wBAAwB,CAAC,KAAK;QACzF,IAAI,WAAW,KAAK,KAAK,UAAU,WAAW,UAAU,KAAK,MAAM;YAAE,OAAO;QAAO;IACpF;IAEA,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/has-symbols/index.js"], "sourcesContent": ["'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeSymbols() {\n\tif (typeof origSymbol !== 'function') { return false; }\n\tif (typeof Symbol !== 'function') { return false; }\n\tif (typeof origSymbol('foo') !== 'symbol') { return false; }\n\tif (typeof Symbol('bar') !== 'symbol') { return false; }\n\n\treturn hasSymbolSham();\n};\n"], "names": [], "mappings": "AAEA,IAAI,aAAa,OAAO,WAAW,eAAe;AAClD,IAAI;AAEJ,wBAAwB,GACxB,OAAO,OAAO,GAAG,SAAS;IACzB,IAAI,OAAO,eAAe,YAAY;QAAE,OAAO;IAAO;IACtD,IAAI,OAAO,WAAW,YAAY;QAAE,OAAO;IAAO;IAClD,IAAI,OAAO,WAAW,WAAW,UAAU;QAAE,OAAO;IAAO;IAC3D,IAAI,OAAO,OAAO,WAAW,UAAU;QAAE,OAAO;IAAO;IAEvD,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/get-proto/Reflect.getPrototypeOf.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./Reflect.getPrototypeOf')} */\nmodule.exports = (typeof Reflect !== 'undefined' && Reflect.getPrototypeOf) || null;\n"], "names": [], "mappings": "AAEA,+CAA+C,GAC/C,OAAO,OAAO,GAAG,AAAC,OAAO,YAAY,eAAe,QAAQ,cAAc,IAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/get-proto/Object.getPrototypeOf.js"], "sourcesContent": ["'use strict';\n\nvar $Object = require('es-object-atoms');\n\n/** @type {import('./Object.getPrototypeOf')} */\nmodule.exports = $Object.getPrototypeOf || null;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,8CAA8C,GAC9C,OAAO,OAAO,GAAG,QAAQ,cAAc,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/function-bind/implementation.js"], "sourcesContent": ["'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar toStr = Object.prototype.toString;\nvar max = Math.max;\nvar funcType = '[object Function]';\n\nvar concatty = function concatty(a, b) {\n    var arr = [];\n\n    for (var i = 0; i < a.length; i += 1) {\n        arr[i] = a[i];\n    }\n    for (var j = 0; j < b.length; j += 1) {\n        arr[j + a.length] = b[j];\n    }\n\n    return arr;\n};\n\nvar slicy = function slicy(arrLike, offset) {\n    var arr = [];\n    for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {\n        arr[j] = arrLike[i];\n    }\n    return arr;\n};\n\nvar joiny = function (arr, joiner) {\n    var str = '';\n    for (var i = 0; i < arr.length; i += 1) {\n        str += arr[i];\n        if (i + 1 < arr.length) {\n            str += joiner;\n        }\n    }\n    return str;\n};\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.apply(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slicy(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                concatty(args, arguments)\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        }\n        return target.apply(\n            that,\n            concatty(args, arguments)\n        );\n\n    };\n\n    var boundLength = max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs[i] = '$' + i;\n    }\n\n    bound = Function('binder', 'return function (' + joiny(boundArgs, ',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n"], "names": [], "mappings": "AAEA,6BAA6B,GAE7B,IAAI,gBAAgB;AACpB,IAAI,QAAQ,OAAO,SAAS,CAAC,QAAQ;AACrC,IAAI,MAAM,KAAK,GAAG;AAClB,IAAI,WAAW;AAEf,IAAI,WAAW,SAAS,SAAS,CAAC,EAAE,CAAC;IACjC,IAAI,MAAM,EAAE;IAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,KAAK,EAAG;QAClC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACjB;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,KAAK,EAAG;QAClC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC5B;IAEA,OAAO;AACX;AAEA,IAAI,QAAQ,SAAS,MAAM,OAAO,EAAE,MAAM;IACtC,IAAI,MAAM,EAAE;IACZ,IAAK,IAAI,IAAI,UAAU,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,GAAG,KAAK,EAAG;QACjE,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IACvB;IACA,OAAO;AACX;AAEA,IAAI,QAAQ,SAAU,GAAG,EAAE,MAAM;IAC7B,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,EAAG;QACpC,OAAO,GAAG,CAAC,EAAE;QACb,IAAI,IAAI,IAAI,IAAI,MAAM,EAAE;YACpB,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEA,OAAO,OAAO,GAAG,SAAS,KAAK,IAAI;IAC/B,IAAI,SAAS,IAAI;IACjB,IAAI,OAAO,WAAW,cAAc,MAAM,KAAK,CAAC,YAAY,UAAU;QAClE,MAAM,IAAI,UAAU,gBAAgB;IACxC;IACA,IAAI,OAAO,MAAM,WAAW;IAE5B,IAAI;IACJ,IAAI,SAAS;QACT,IAAI,IAAI,YAAY,OAAO;YACvB,IAAI,SAAS,OAAO,KAAK,CACrB,IAAI,EACJ,SAAS,MAAM;YAEnB,IAAI,OAAO,YAAY,QAAQ;gBAC3B,OAAO;YACX;YACA,OAAO,IAAI;QACf;QACA,OAAO,OAAO,KAAK,CACf,MACA,SAAS,MAAM;IAGvB;IAEA,IAAI,cAAc,IAAI,GAAG,OAAO,MAAM,GAAG,KAAK,MAAM;IACpD,IAAI,YAAY,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QAClC,SAAS,CAAC,EAAE,GAAG,MAAM;IACzB;IAEA,QAAQ,SAAS,UAAU,sBAAsB,MAAM,WAAW,OAAO,6CAA6C;IAEtH,IAAI,OAAO,SAAS,EAAE;QAClB,IAAI,QAAQ,SAAS,SAAS;QAC9B,MAAM,SAAS,GAAG,OAAO,SAAS;QAClC,MAAM,SAAS,GAAG,IAAI;QACtB,MAAM,SAAS,GAAG;IACtB;IAEA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/function-bind/index.js"], "sourcesContent": ["'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,OAAO,OAAO,GAAG,SAAS,SAAS,CAAC,IAAI,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/call-bind-apply-helpers/functionCall.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./functionCall')} */\nmodule.exports = Function.prototype.call;\n"], "names": [], "mappings": "AAEA,qCAAqC,GACrC,OAAO,OAAO,GAAG,SAAS,SAAS,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/call-bind-apply-helpers/functionApply.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./functionApply')} */\nmodule.exports = Function.prototype.apply;\n"], "names": [], "mappings": "AAEA,sCAAsC,GACtC,OAAO,OAAO,GAAG,SAAS,SAAS,CAAC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/call-bind-apply-helpers/reflectApply.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./reflectApply')} */\nmodule.exports = typeof Reflect !== 'undefined' && Reflect && Reflect.apply;\n"], "names": [], "mappings": "AAEA,qCAAqC,GACrC,OAAO,OAAO,GAAG,OAAO,YAAY,eAAe,WAAW,QAAQ,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/call-bind-apply-helpers/actualApply.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\n\nvar $apply = require('./functionApply');\nvar $call = require('./functionCall');\nvar $reflectApply = require('./reflectApply');\n\n/** @type {import('./actualApply')} */\nmodule.exports = $reflectApply || bind.call($call, $apply);\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,oCAAoC,GACpC,OAAO,OAAO,GAAG,iBAAiB,KAAK,IAAI,CAAC,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/call-bind-apply-helpers/index.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\nvar $TypeError = require('es-errors/type');\n\nvar $call = require('./functionCall');\nvar $actualApply = require('./actualApply');\n\n/** @type {(args: [Function, thisArg?: unknown, ...args: unknown[]]) => Function} TODO FIXME, find a way to use import('.') */\nmodule.exports = function callBindBasic(args) {\n\tif (args.length < 1 || typeof args[0] !== 'function') {\n\t\tthrow new $TypeError('a function is required');\n\t}\n\treturn $actualApply(bind, $call, args);\n};\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AAEJ,4HAA4H,GAC5H,OAAO,OAAO,GAAG,SAAS,cAAc,IAAI;IAC3C,IAAI,KAAK,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY;QACrD,MAAM,IAAI,WAAW;IACtB;IACA,OAAO,aAAa,MAAM,OAAO;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/dunder-proto/get.js"], "sourcesContent": ["'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\n\nvar hasProtoAccessor;\ntry {\n\t// eslint-disable-next-line no-extra-parens, no-proto\n\thasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */ ([]).__proto__ === Array.prototype;\n} catch (e) {\n\tif (!e || typeof e !== 'object' || !('code' in e) || e.code !== 'ERR_PROTO_ACCESS') {\n\t\tthrow e;\n\t}\n}\n\n// eslint-disable-next-line no-extra-parens\nvar desc = !!hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */ ('__proto__'));\n\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function'\n\t? callBind([desc.get])\n\t: typeof $getPrototypeOf === 'function'\n\t\t? /** @type {import('./get')} */ function getDunder(value) {\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\treturn $getPrototypeOf(value == null ? value : $Object(value));\n\t\t}\n\t\t: false;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;IACH,qDAAqD;IACrD,mBAAmB,mDAAmD,GAAG,AAAC,EAAE,CAAE,SAAS,KAAK,MAAM,SAAS;AAC5G,EAAE,OAAO,GAAG;IACX,IAAI,CAAC,KAAK,OAAO,MAAM,YAAY,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,KAAK,oBAAoB;QACnF,MAAM;IACP;AACD;AAEA,2CAA2C;AAC3C,IAAI,OAAO,CAAC,CAAC,oBAAoB,QAAQ,KAAK,OAAO,SAAS,EAAgD;AAE9G,IAAI,UAAU;AACd,IAAI,kBAAkB,QAAQ,cAAc;AAE5C,4BAA4B,GAC5B,OAAO,OAAO,GAAG,QAAQ,OAAO,KAAK,GAAG,KAAK,aAC1C,SAAS;IAAC,KAAK,GAAG;CAAC,IACnB,OAAO,oBAAoB,aAC1B,4BAA4B,GAAG,SAAS,UAAU,KAAK;IACxD,kCAAkC;IAClC,OAAO,gBAAgB,SAAS,OAAO,QAAQ,QAAQ;AACxD,IACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/get-proto/index.js"], "sourcesContent": ["'use strict';\n\nvar reflectGetProto = require('./Reflect.getPrototypeOf');\nvar originalGetProto = require('./Object.getPrototypeOf');\n\nvar getDunderProto = require('dunder-proto/get');\n\n/** @type {import('.')} */\nmodule.exports = reflectGetProto\n\t? function getProto(O) {\n\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\treturn reflectGetProto(O);\n\t}\n\t: originalGetProto\n\t\t? function getProto(O) {\n\t\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\t\tthrow new TypeError('getProto: not an object');\n\t\t\t}\n\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\treturn originalGetProto(O);\n\t\t}\n\t\t: getDunderProto\n\t\t\t? function getProto(O) {\n\t\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\t\treturn getDunderProto(O);\n\t\t\t}\n\t\t\t: null;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AAEJ,IAAI;AAEJ,wBAAwB,GACxB,OAAO,OAAO,GAAG,kBACd,SAAS,SAAS,CAAC;IACpB,qEAAqE;IACrE,OAAO,gBAAgB;AACxB,IACE,mBACC,SAAS,SAAS,CAAC;IACpB,IAAI,CAAC,KAAM,OAAO,MAAM,YAAY,OAAO,MAAM,YAAa;QAC7D,MAAM,IAAI,UAAU;IACrB;IACA,qEAAqE;IACrE,OAAO,iBAAiB;AACzB,IACE,iBACC,SAAS,SAAS,CAAC;IACpB,qEAAqE;IACrE,OAAO,eAAe;AACvB,IACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/hasown/index.js"], "sourcesContent": ["'use strict';\n\nvar call = Function.prototype.call;\nvar $hasOwn = Object.prototype.hasOwnProperty;\nvar bind = require('function-bind');\n\n/** @type {import('.')} */\nmodule.exports = bind.call(call, $hasOwn);\n"], "names": [], "mappings": "AAEA,IAAI,OAAO,SAAS,SAAS,CAAC,IAAI;AAClC,IAAI,UAAU,OAAO,SAAS,CAAC,cAAc;AAC7C,IAAI;AAEJ,wBAAwB,GACxB,OAAO,OAAO,GAAG,KAAK,IAAI,CAAC,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/get-intrinsic/index.js"], "sourcesContent": ["'use strict';\n\nvar undefined;\n\nvar $Object = require('es-object-atoms');\n\nvar $Error = require('es-errors');\nvar $EvalError = require('es-errors/eval');\nvar $RangeError = require('es-errors/range');\nvar $ReferenceError = require('es-errors/ref');\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\nvar $URIError = require('es-errors/uri');\n\nvar abs = require('math-intrinsics/abs');\nvar floor = require('math-intrinsics/floor');\nvar max = require('math-intrinsics/max');\nvar min = require('math-intrinsics/min');\nvar pow = require('math-intrinsics/pow');\nvar round = require('math-intrinsics/round');\nvar sign = require('math-intrinsics/sign');\n\nvar $Function = Function;\n\n// eslint-disable-next-line consistent-return\nvar getEvalledConstructor = function (expressionSyntax) {\n\ttry {\n\t\treturn $Function('\"use strict\"; return (' + expressionSyntax + ').constructor;')();\n\t} catch (e) {}\n};\n\nvar $gOPD = require('gopd');\nvar $defineProperty = require('es-define-property');\n\nvar throwTypeError = function () {\n\tthrow new $TypeError();\n};\nvar ThrowTypeError = $gOPD\n\t? (function () {\n\t\ttry {\n\t\t\t// eslint-disable-next-line no-unused-expressions, no-caller, no-restricted-properties\n\t\t\targuments.callee; // IE 8 does not throw here\n\t\t\treturn throwTypeError;\n\t\t} catch (calleeThrows) {\n\t\t\ttry {\n\t\t\t\t// IE 8 throws on Object.getOwnPropertyDescriptor(arguments, '')\n\t\t\t\treturn $gOPD(arguments, 'callee').get;\n\t\t\t} catch (gOPDthrows) {\n\t\t\t\treturn throwTypeError;\n\t\t\t}\n\t\t}\n\t}())\n\t: throwTypeError;\n\nvar hasSymbols = require('has-symbols')();\n\nvar getProto = require('get-proto');\nvar $ObjectGPO = require('get-proto/Object.getPrototypeOf');\nvar $ReflectGPO = require('get-proto/Reflect.getPrototypeOf');\n\nvar $apply = require('call-bind-apply-helpers/functionApply');\nvar $call = require('call-bind-apply-helpers/functionCall');\n\nvar needsEval = {};\n\nvar TypedArray = typeof Uint8Array === 'undefined' || !getProto ? undefined : getProto(Uint8Array);\n\nvar INTRINSICS = {\n\t__proto__: null,\n\t'%AggregateError%': typeof AggregateError === 'undefined' ? undefined : AggregateError,\n\t'%Array%': Array,\n\t'%ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer,\n\t'%ArrayIteratorPrototype%': hasSymbols && getProto ? getProto([][Symbol.iterator]()) : undefined,\n\t'%AsyncFromSyncIteratorPrototype%': undefined,\n\t'%AsyncFunction%': needsEval,\n\t'%AsyncGenerator%': needsEval,\n\t'%AsyncGeneratorFunction%': needsEval,\n\t'%AsyncIteratorPrototype%': needsEval,\n\t'%Atomics%': typeof Atomics === 'undefined' ? undefined : Atomics,\n\t'%BigInt%': typeof BigInt === 'undefined' ? undefined : BigInt,\n\t'%BigInt64Array%': typeof BigInt64Array === 'undefined' ? undefined : BigInt64Array,\n\t'%BigUint64Array%': typeof BigUint64Array === 'undefined' ? undefined : BigUint64Array,\n\t'%Boolean%': Boolean,\n\t'%DataView%': typeof DataView === 'undefined' ? undefined : DataView,\n\t'%Date%': Date,\n\t'%decodeURI%': decodeURI,\n\t'%decodeURIComponent%': decodeURIComponent,\n\t'%encodeURI%': encodeURI,\n\t'%encodeURIComponent%': encodeURIComponent,\n\t'%Error%': $Error,\n\t'%eval%': eval, // eslint-disable-line no-eval\n\t'%EvalError%': $EvalError,\n\t'%Float16Array%': typeof Float16Array === 'undefined' ? undefined : Float16Array,\n\t'%Float32Array%': typeof Float32Array === 'undefined' ? undefined : Float32Array,\n\t'%Float64Array%': typeof Float64Array === 'undefined' ? undefined : Float64Array,\n\t'%FinalizationRegistry%': typeof FinalizationRegistry === 'undefined' ? undefined : FinalizationRegistry,\n\t'%Function%': $Function,\n\t'%GeneratorFunction%': needsEval,\n\t'%Int8Array%': typeof Int8Array === 'undefined' ? undefined : Int8Array,\n\t'%Int16Array%': typeof Int16Array === 'undefined' ? undefined : Int16Array,\n\t'%Int32Array%': typeof Int32Array === 'undefined' ? undefined : Int32Array,\n\t'%isFinite%': isFinite,\n\t'%isNaN%': isNaN,\n\t'%IteratorPrototype%': hasSymbols && getProto ? getProto(getProto([][Symbol.iterator]())) : undefined,\n\t'%JSON%': typeof JSON === 'object' ? JSON : undefined,\n\t'%Map%': typeof Map === 'undefined' ? undefined : Map,\n\t'%MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Map()[Symbol.iterator]()),\n\t'%Math%': Math,\n\t'%Number%': Number,\n\t'%Object%': $Object,\n\t'%Object.getOwnPropertyDescriptor%': $gOPD,\n\t'%parseFloat%': parseFloat,\n\t'%parseInt%': parseInt,\n\t'%Promise%': typeof Promise === 'undefined' ? undefined : Promise,\n\t'%Proxy%': typeof Proxy === 'undefined' ? undefined : Proxy,\n\t'%RangeError%': $RangeError,\n\t'%ReferenceError%': $ReferenceError,\n\t'%Reflect%': typeof Reflect === 'undefined' ? undefined : Reflect,\n\t'%RegExp%': RegExp,\n\t'%Set%': typeof Set === 'undefined' ? undefined : Set,\n\t'%SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Set()[Symbol.iterator]()),\n\t'%SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer,\n\t'%String%': String,\n\t'%StringIteratorPrototype%': hasSymbols && getProto ? getProto(''[Symbol.iterator]()) : undefined,\n\t'%Symbol%': hasSymbols ? Symbol : undefined,\n\t'%SyntaxError%': $SyntaxError,\n\t'%ThrowTypeError%': ThrowTypeError,\n\t'%TypedArray%': TypedArray,\n\t'%TypeError%': $TypeError,\n\t'%Uint8Array%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array,\n\t'%Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray,\n\t'%Uint16Array%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array,\n\t'%Uint32Array%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array,\n\t'%URIError%': $URIError,\n\t'%WeakMap%': typeof WeakMap === 'undefined' ? undefined : WeakMap,\n\t'%WeakRef%': typeof WeakRef === 'undefined' ? undefined : WeakRef,\n\t'%WeakSet%': typeof WeakSet === 'undefined' ? undefined : WeakSet,\n\n\t'%Function.prototype.call%': $call,\n\t'%Function.prototype.apply%': $apply,\n\t'%Object.defineProperty%': $defineProperty,\n\t'%Object.getPrototypeOf%': $ObjectGPO,\n\t'%Math.abs%': abs,\n\t'%Math.floor%': floor,\n\t'%Math.max%': max,\n\t'%Math.min%': min,\n\t'%Math.pow%': pow,\n\t'%Math.round%': round,\n\t'%Math.sign%': sign,\n\t'%Reflect.getPrototypeOf%': $ReflectGPO\n};\n\nif (getProto) {\n\ttry {\n\t\tnull.error; // eslint-disable-line no-unused-expressions\n\t} catch (e) {\n\t\t// https://github.com/tc39/proposal-shadowrealm/pull/384#issuecomment-1364264229\n\t\tvar errorProto = getProto(getProto(e));\n\t\tINTRINSICS['%Error.prototype%'] = errorProto;\n\t}\n}\n\nvar doEval = function doEval(name) {\n\tvar value;\n\tif (name === '%AsyncFunction%') {\n\t\tvalue = getEvalledConstructor('async function () {}');\n\t} else if (name === '%GeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('function* () {}');\n\t} else if (name === '%AsyncGeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('async function* () {}');\n\t} else if (name === '%AsyncGenerator%') {\n\t\tvar fn = doEval('%AsyncGeneratorFunction%');\n\t\tif (fn) {\n\t\t\tvalue = fn.prototype;\n\t\t}\n\t} else if (name === '%AsyncIteratorPrototype%') {\n\t\tvar gen = doEval('%AsyncGenerator%');\n\t\tif (gen && getProto) {\n\t\t\tvalue = getProto(gen.prototype);\n\t\t}\n\t}\n\n\tINTRINSICS[name] = value;\n\n\treturn value;\n};\n\nvar LEGACY_ALIASES = {\n\t__proto__: null,\n\t'%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],\n\t'%ArrayPrototype%': ['Array', 'prototype'],\n\t'%ArrayProto_entries%': ['Array', 'prototype', 'entries'],\n\t'%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],\n\t'%ArrayProto_keys%': ['Array', 'prototype', 'keys'],\n\t'%ArrayProto_values%': ['Array', 'prototype', 'values'],\n\t'%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],\n\t'%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],\n\t'%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],\n\t'%BooleanPrototype%': ['Boolean', 'prototype'],\n\t'%DataViewPrototype%': ['DataView', 'prototype'],\n\t'%DatePrototype%': ['Date', 'prototype'],\n\t'%ErrorPrototype%': ['Error', 'prototype'],\n\t'%EvalErrorPrototype%': ['EvalError', 'prototype'],\n\t'%Float32ArrayPrototype%': ['Float32Array', 'prototype'],\n\t'%Float64ArrayPrototype%': ['Float64Array', 'prototype'],\n\t'%FunctionPrototype%': ['Function', 'prototype'],\n\t'%Generator%': ['GeneratorFunction', 'prototype'],\n\t'%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],\n\t'%Int8ArrayPrototype%': ['Int8Array', 'prototype'],\n\t'%Int16ArrayPrototype%': ['Int16Array', 'prototype'],\n\t'%Int32ArrayPrototype%': ['Int32Array', 'prototype'],\n\t'%JSONParse%': ['JSON', 'parse'],\n\t'%JSONStringify%': ['JSON', 'stringify'],\n\t'%MapPrototype%': ['Map', 'prototype'],\n\t'%NumberPrototype%': ['Number', 'prototype'],\n\t'%ObjectPrototype%': ['Object', 'prototype'],\n\t'%ObjProto_toString%': ['Object', 'prototype', 'toString'],\n\t'%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],\n\t'%PromisePrototype%': ['Promise', 'prototype'],\n\t'%PromiseProto_then%': ['Promise', 'prototype', 'then'],\n\t'%Promise_all%': ['Promise', 'all'],\n\t'%Promise_reject%': ['Promise', 'reject'],\n\t'%Promise_resolve%': ['Promise', 'resolve'],\n\t'%RangeErrorPrototype%': ['RangeError', 'prototype'],\n\t'%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],\n\t'%RegExpPrototype%': ['RegExp', 'prototype'],\n\t'%SetPrototype%': ['Set', 'prototype'],\n\t'%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],\n\t'%StringPrototype%': ['String', 'prototype'],\n\t'%SymbolPrototype%': ['Symbol', 'prototype'],\n\t'%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],\n\t'%TypedArrayPrototype%': ['TypedArray', 'prototype'],\n\t'%TypeErrorPrototype%': ['TypeError', 'prototype'],\n\t'%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],\n\t'%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],\n\t'%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],\n\t'%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],\n\t'%URIErrorPrototype%': ['URIError', 'prototype'],\n\t'%WeakMapPrototype%': ['WeakMap', 'prototype'],\n\t'%WeakSetPrototype%': ['WeakSet', 'prototype']\n};\n\nvar bind = require('function-bind');\nvar hasOwn = require('hasown');\nvar $concat = bind.call($call, Array.prototype.concat);\nvar $spliceApply = bind.call($apply, Array.prototype.splice);\nvar $replace = bind.call($call, String.prototype.replace);\nvar $strSlice = bind.call($call, String.prototype.slice);\nvar $exec = bind.call($call, RegExp.prototype.exec);\n\n/* adapted from https://github.com/lodash/lodash/blob/4.17.15/dist/lodash.js#L6735-L6744 */\nvar rePropName = /[^%.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|%$))/g;\nvar reEscapeChar = /\\\\(\\\\)?/g; /** Used to match backslashes in property paths. */\nvar stringToPath = function stringToPath(string) {\n\tvar first = $strSlice(string, 0, 1);\n\tvar last = $strSlice(string, -1);\n\tif (first === '%' && last !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected closing `%`');\n\t} else if (last === '%' && first !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected opening `%`');\n\t}\n\tvar result = [];\n\t$replace(string, rePropName, function (match, number, quote, subString) {\n\t\tresult[result.length] = quote ? $replace(subString, reEscapeChar, '$1') : number || match;\n\t});\n\treturn result;\n};\n/* end adaptation */\n\nvar getBaseIntrinsic = function getBaseIntrinsic(name, allowMissing) {\n\tvar intrinsicName = name;\n\tvar alias;\n\tif (hasOwn(LEGACY_ALIASES, intrinsicName)) {\n\t\talias = LEGACY_ALIASES[intrinsicName];\n\t\tintrinsicName = '%' + alias[0] + '%';\n\t}\n\n\tif (hasOwn(INTRINSICS, intrinsicName)) {\n\t\tvar value = INTRINSICS[intrinsicName];\n\t\tif (value === needsEval) {\n\t\t\tvalue = doEval(intrinsicName);\n\t\t}\n\t\tif (typeof value === 'undefined' && !allowMissing) {\n\t\t\tthrow new $TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');\n\t\t}\n\n\t\treturn {\n\t\t\talias: alias,\n\t\t\tname: intrinsicName,\n\t\t\tvalue: value\n\t\t};\n\t}\n\n\tthrow new $SyntaxError('intrinsic ' + name + ' does not exist!');\n};\n\nmodule.exports = function GetIntrinsic(name, allowMissing) {\n\tif (typeof name !== 'string' || name.length === 0) {\n\t\tthrow new $TypeError('intrinsic name must be a non-empty string');\n\t}\n\tif (arguments.length > 1 && typeof allowMissing !== 'boolean') {\n\t\tthrow new $TypeError('\"allowMissing\" argument must be a boolean');\n\t}\n\n\tif ($exec(/^%?[^%]*%?$/, name) === null) {\n\t\tthrow new $SyntaxError('`%` may not be present anywhere but at the beginning and end of the intrinsic name');\n\t}\n\tvar parts = stringToPath(name);\n\tvar intrinsicBaseName = parts.length > 0 ? parts[0] : '';\n\n\tvar intrinsic = getBaseIntrinsic('%' + intrinsicBaseName + '%', allowMissing);\n\tvar intrinsicRealName = intrinsic.name;\n\tvar value = intrinsic.value;\n\tvar skipFurtherCaching = false;\n\n\tvar alias = intrinsic.alias;\n\tif (alias) {\n\t\tintrinsicBaseName = alias[0];\n\t\t$spliceApply(parts, $concat([0, 1], alias));\n\t}\n\n\tfor (var i = 1, isOwn = true; i < parts.length; i += 1) {\n\t\tvar part = parts[i];\n\t\tvar first = $strSlice(part, 0, 1);\n\t\tvar last = $strSlice(part, -1);\n\t\tif (\n\t\t\t(\n\t\t\t\t(first === '\"' || first === \"'\" || first === '`')\n\t\t\t\t|| (last === '\"' || last === \"'\" || last === '`')\n\t\t\t)\n\t\t\t&& first !== last\n\t\t) {\n\t\t\tthrow new $SyntaxError('property names with quotes must have matching quotes');\n\t\t}\n\t\tif (part === 'constructor' || !isOwn) {\n\t\t\tskipFurtherCaching = true;\n\t\t}\n\n\t\tintrinsicBaseName += '.' + part;\n\t\tintrinsicRealName = '%' + intrinsicBaseName + '%';\n\n\t\tif (hasOwn(INTRINSICS, intrinsicRealName)) {\n\t\t\tvalue = INTRINSICS[intrinsicRealName];\n\t\t} else if (value != null) {\n\t\t\tif (!(part in value)) {\n\t\t\t\tif (!allowMissing) {\n\t\t\t\t\tthrow new $TypeError('base intrinsic for ' + name + ' exists, but the property is not available.');\n\t\t\t\t}\n\t\t\t\treturn void undefined;\n\t\t\t}\n\t\t\tif ($gOPD && (i + 1) >= parts.length) {\n\t\t\t\tvar desc = $gOPD(value, part);\n\t\t\t\tisOwn = !!desc;\n\n\t\t\t\t// By convention, when a data property is converted to an accessor\n\t\t\t\t// property to emulate a data property that does not suffer from\n\t\t\t\t// the override mistake, that accessor's getter is marked with\n\t\t\t\t// an `originalValue` property. Here, when we detect this, we\n\t\t\t\t// uphold the illusion by pretending to see that original data\n\t\t\t\t// property, i.e., returning the value rather than the getter\n\t\t\t\t// itself.\n\t\t\t\tif (isOwn && 'get' in desc && !('originalValue' in desc.get)) {\n\t\t\t\t\tvalue = desc.get;\n\t\t\t\t} else {\n\t\t\t\t\tvalue = value[part];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tisOwn = hasOwn(value, part);\n\t\t\t\tvalue = value[part];\n\t\t\t}\n\n\t\t\tif (isOwn && !skipFurtherCaching) {\n\t\t\t\tINTRINSICS[intrinsicRealName] = value;\n\t\t\t}\n\t\t}\n\t}\n\treturn value;\n};\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,YAAY;AAEhB,6CAA6C;AAC7C,IAAI,wBAAwB,SAAU,gBAAgB;IACrD,IAAI;QACH,OAAO,UAAU,2BAA2B,mBAAmB;IAChE,EAAE,OAAO,GAAG,CAAC;AACd;AAEA,IAAI;AACJ,IAAI;AAEJ,IAAI,iBAAiB;IACpB,MAAM,IAAI;AACX;AACA,IAAI,iBAAiB,QACjB;IACF,IAAI;QACH,sFAAsF;QACtF,UAAU,MAAM,EAAE,2BAA2B;QAC7C,OAAO;IACR,EAAE,OAAO,cAAc;QACtB,IAAI;YACH,gEAAgE;YAChE,OAAO,MAAM,WAAW,UAAU,GAAG;QACtC,EAAE,OAAO,YAAY;YACpB,OAAO;QACR;IACD;AACD,MACE;AAEH,IAAI,aAAa;AAEjB,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AAEJ,IAAI,YAAY,CAAC;AAEjB,IAAI,aAAa,OAAO,eAAe,eAAe,CAAC,WAAW,YAAY,SAAS;AAEvF,IAAI,aAAa;IAChB,WAAW;IACX,oBAAoB,OAAO,mBAAmB,cAAc,YAAY;IACxE,WAAW;IACX,iBAAiB,OAAO,gBAAgB,cAAc,YAAY;IAClE,4BAA4B,cAAc,WAAW,SAAS,EAAE,CAAC,OAAO,QAAQ,CAAC,MAAM;IACvF,oCAAoC;IACpC,mBAAmB;IACnB,oBAAoB;IACpB,4BAA4B;IAC5B,4BAA4B;IAC5B,aAAa,OAAO,YAAY,cAAc,YAAY;IAC1D,YAAY,OAAO,WAAW,cAAc,YAAY;IACxD,mBAAmB,OAAO,kBAAkB,cAAc,YAAY;IACtE,oBAAoB,OAAO,mBAAmB,cAAc,YAAY;IACxE,aAAa;IACb,cAAc,OAAO,aAAa,cAAc,YAAY;IAC5D,UAAU;IACV,eAAe;IACf,wBAAwB;IACxB,eAAe;IACf,wBAAwB;IACxB,WAAW;IACX,UAAU;IACV,eAAe;IACf,kBAAkB,OAAO,iBAAiB,cAAc,YAAY;IACpE,kBAAkB,OAAO,iBAAiB,cAAc,YAAY;IACpE,kBAAkB,OAAO,iBAAiB,cAAc,YAAY;IACpE,0BAA0B,OAAO,yBAAyB,cAAc,YAAY;IACpF,cAAc;IACd,uBAAuB;IACvB,eAAe,OAAO,cAAc,cAAc,YAAY;IAC9D,gBAAgB,OAAO,eAAe,cAAc,YAAY;IAChE,gBAAgB,OAAO,eAAe,cAAc,YAAY;IAChE,cAAc;IACd,WAAW;IACX,uBAAuB,cAAc,WAAW,SAAS,SAAS,EAAE,CAAC,OAAO,QAAQ,CAAC,OAAO;IAC5F,UAAU,OAAO,SAAS,WAAW,OAAO;IAC5C,SAAS,OAAO,QAAQ,cAAc,YAAY;IAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAW,YAAY,SAAS,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC;IAClI,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,qCAAqC;IACrC,gBAAgB;IAChB,cAAc;IACd,aAAa,OAAO,YAAY,cAAc,YAAY;IAC1D,WAAW,OAAO,UAAU,cAAc,YAAY;IACtD,gBAAgB;IAChB,oBAAoB;IACpB,aAAa,OAAO,YAAY,cAAc,YAAY;IAC1D,YAAY;IACZ,SAAS,OAAO,QAAQ,cAAc,YAAY;IAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAW,YAAY,SAAS,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC;IAClI,uBAAuB,OAAO,sBAAsB,cAAc,YAAY;IAC9E,YAAY;IACZ,6BAA6B,cAAc,WAAW,SAAS,EAAE,CAAC,OAAO,QAAQ,CAAC,MAAM;IACxF,YAAY,aAAa,SAAS;IAClC,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,eAAe;IACf,gBAAgB,OAAO,eAAe,cAAc,YAAY;IAChE,uBAAuB,OAAO,sBAAsB,cAAc,YAAY;IAC9E,iBAAiB,OAAO,gBAAgB,cAAc,YAAY;IAClE,iBAAiB,OAAO,gBAAgB,cAAc,YAAY;IAClE,cAAc;IACd,aAAa,OAAO,YAAY,cAAc,YAAY;IAC1D,aAAa,OAAO,YAAY,cAAc,YAAY;IAC1D,aAAa,OAAO,YAAY,cAAc,YAAY;IAE1D,6BAA6B;IAC7B,8BAA8B;IAC9B,2BAA2B;IAC3B,2BAA2B;IAC3B,cAAc;IACd,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,eAAe;IACf,4BAA4B;AAC7B;AAEA,IAAI,UAAU;IACb,IAAI;QACH,KAAK,KAAK,EAAE,4CAA4C;IACzD,EAAE,OAAO,GAAG;QACX,gFAAgF;QAChF,IAAI,aAAa,SAAS,SAAS;QACnC,UAAU,CAAC,oBAAoB,GAAG;IACnC;AACD;AAEA,IAAI,SAAS,SAAS,OAAO,IAAI;IAChC,IAAI;IACJ,IAAI,SAAS,mBAAmB;QAC/B,QAAQ,sBAAsB;IAC/B,OAAO,IAAI,SAAS,uBAAuB;QAC1C,QAAQ,sBAAsB;IAC/B,OAAO,IAAI,SAAS,4BAA4B;QAC/C,QAAQ,sBAAsB;IAC/B,OAAO,IAAI,SAAS,oBAAoB;QACvC,IAAI,KAAK,OAAO;QAChB,IAAI,IAAI;YACP,QAAQ,GAAG,SAAS;QACrB;IACD,OAAO,IAAI,SAAS,4BAA4B;QAC/C,IAAI,MAAM,OAAO;QACjB,IAAI,OAAO,UAAU;YACpB,QAAQ,SAAS,IAAI,SAAS;QAC/B;IACD;IAEA,UAAU,CAAC,KAAK,GAAG;IAEnB,OAAO;AACR;AAEA,IAAI,iBAAiB;IACpB,WAAW;IACX,0BAA0B;QAAC;QAAe;KAAY;IACtD,oBAAoB;QAAC;QAAS;KAAY;IAC1C,wBAAwB;QAAC;QAAS;QAAa;KAAU;IACzD,wBAAwB;QAAC;QAAS;QAAa;KAAU;IACzD,qBAAqB;QAAC;QAAS;QAAa;KAAO;IACnD,uBAAuB;QAAC;QAAS;QAAa;KAAS;IACvD,4BAA4B;QAAC;QAAiB;KAAY;IAC1D,oBAAoB;QAAC;QAA0B;KAAY;IAC3D,6BAA6B;QAAC;QAA0B;QAAa;KAAY;IACjF,sBAAsB;QAAC;QAAW;KAAY;IAC9C,uBAAuB;QAAC;QAAY;KAAY;IAChD,mBAAmB;QAAC;QAAQ;KAAY;IACxC,oBAAoB;QAAC;QAAS;KAAY;IAC1C,wBAAwB;QAAC;QAAa;KAAY;IAClD,2BAA2B;QAAC;QAAgB;KAAY;IACxD,2BAA2B;QAAC;QAAgB;KAAY;IACxD,uBAAuB;QAAC;QAAY;KAAY;IAChD,eAAe;QAAC;QAAqB;KAAY;IACjD,wBAAwB;QAAC;QAAqB;QAAa;KAAY;IACvE,wBAAwB;QAAC;QAAa;KAAY;IAClD,yBAAyB;QAAC;QAAc;KAAY;IACpD,yBAAyB;QAAC;QAAc;KAAY;IACpD,eAAe;QAAC;QAAQ;KAAQ;IAChC,mBAAmB;QAAC;QAAQ;KAAY;IACxC,kBAAkB;QAAC;QAAO;KAAY;IACtC,qBAAqB;QAAC;QAAU;KAAY;IAC5C,qBAAqB;QAAC;QAAU;KAAY;IAC5C,uBAAuB;QAAC;QAAU;QAAa;KAAW;IAC1D,sBAAsB;QAAC;QAAU;QAAa;KAAU;IACxD,sBAAsB;QAAC;QAAW;KAAY;IAC9C,uBAAuB;QAAC;QAAW;QAAa;KAAO;IACvD,iBAAiB;QAAC;QAAW;KAAM;IACnC,oBAAoB;QAAC;QAAW;KAAS;IACzC,qBAAqB;QAAC;QAAW;KAAU;IAC3C,yBAAyB;QAAC;QAAc;KAAY;IACpD,6BAA6B;QAAC;QAAkB;KAAY;IAC5D,qBAAqB;QAAC;QAAU;KAAY;IAC5C,kBAAkB;QAAC;QAAO;KAAY;IACtC,gCAAgC;QAAC;QAAqB;KAAY;IAClE,qBAAqB;QAAC;QAAU;KAAY;IAC5C,qBAAqB;QAAC;QAAU;KAAY;IAC5C,0BAA0B;QAAC;QAAe;KAAY;IACtD,yBAAyB;QAAC;QAAc;KAAY;IACpD,wBAAwB;QAAC;QAAa;KAAY;IAClD,yBAAyB;QAAC;QAAc;KAAY;IACpD,gCAAgC;QAAC;QAAqB;KAAY;IAClE,0BAA0B;QAAC;QAAe;KAAY;IACtD,0BAA0B;QAAC;QAAe;KAAY;IACtD,uBAAuB;QAAC;QAAY;KAAY;IAChD,sBAAsB;QAAC;QAAW;KAAY;IAC9C,sBAAsB;QAAC;QAAW;KAAY;AAC/C;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI,UAAU,KAAK,IAAI,CAAC,OAAO,MAAM,SAAS,CAAC,MAAM;AACrD,IAAI,eAAe,KAAK,IAAI,CAAC,QAAQ,MAAM,SAAS,CAAC,MAAM;AAC3D,IAAI,WAAW,KAAK,IAAI,CAAC,OAAO,OAAO,SAAS,CAAC,OAAO;AACxD,IAAI,YAAY,KAAK,IAAI,CAAC,OAAO,OAAO,SAAS,CAAC,KAAK;AACvD,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,OAAO,SAAS,CAAC,IAAI;AAElD,yFAAyF,GACzF,IAAI,aAAa;AACjB,IAAI,eAAe,YAAY,iDAAiD;AAChF,IAAI,eAAe,SAAS,aAAa,MAAM;IAC9C,IAAI,QAAQ,UAAU,QAAQ,GAAG;IACjC,IAAI,OAAO,UAAU,QAAQ,CAAC;IAC9B,IAAI,UAAU,OAAO,SAAS,KAAK;QAClC,MAAM,IAAI,aAAa;IACxB,OAAO,IAAI,SAAS,OAAO,UAAU,KAAK;QACzC,MAAM,IAAI,aAAa;IACxB;IACA,IAAI,SAAS,EAAE;IACf,SAAS,QAAQ,YAAY,SAAU,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS;QACrE,MAAM,CAAC,OAAO,MAAM,CAAC,GAAG,QAAQ,SAAS,WAAW,cAAc,QAAQ,UAAU;IACrF;IACA,OAAO;AACR;AACA,kBAAkB,GAElB,IAAI,mBAAmB,SAAS,iBAAiB,IAAI,EAAE,YAAY;IAClE,IAAI,gBAAgB;IACpB,IAAI;IACJ,IAAI,OAAO,gBAAgB,gBAAgB;QAC1C,QAAQ,cAAc,CAAC,cAAc;QACrC,gBAAgB,MAAM,KAAK,CAAC,EAAE,GAAG;IAClC;IAEA,IAAI,OAAO,YAAY,gBAAgB;QACtC,IAAI,QAAQ,UAAU,CAAC,cAAc;QACrC,IAAI,UAAU,WAAW;YACxB,QAAQ,OAAO;QAChB;QACA,IAAI,OAAO,UAAU,eAAe,CAAC,cAAc;YAClD,MAAM,IAAI,WAAW,eAAe,OAAO;QAC5C;QAEA,OAAO;YACN,OAAO;YACP,MAAM;YACN,OAAO;QACR;IACD;IAEA,MAAM,IAAI,aAAa,eAAe,OAAO;AAC9C;AAEA,OAAO,OAAO,GAAG,SAAS,aAAa,IAAI,EAAE,YAAY;IACxD,IAAI,OAAO,SAAS,YAAY,KAAK,MAAM,KAAK,GAAG;QAClD,MAAM,IAAI,WAAW;IACtB;IACA,IAAI,UAAU,MAAM,GAAG,KAAK,OAAO,iBAAiB,WAAW;QAC9D,MAAM,IAAI,WAAW;IACtB;IAEA,IAAI,MAAM,eAAe,UAAU,MAAM;QACxC,MAAM,IAAI,aAAa;IACxB;IACA,IAAI,QAAQ,aAAa;IACzB,IAAI,oBAAoB,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG;IAEtD,IAAI,YAAY,iBAAiB,MAAM,oBAAoB,KAAK;IAChE,IAAI,oBAAoB,UAAU,IAAI;IACtC,IAAI,QAAQ,UAAU,KAAK;IAC3B,IAAI,qBAAqB;IAEzB,IAAI,QAAQ,UAAU,KAAK;IAC3B,IAAI,OAAO;QACV,oBAAoB,KAAK,CAAC,EAAE;QAC5B,aAAa,OAAO,QAAQ;YAAC;YAAG;SAAE,EAAE;IACrC;IAEA,IAAK,IAAI,IAAI,GAAG,QAAQ,MAAM,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACvD,IAAI,OAAO,KAAK,CAAC,EAAE;QACnB,IAAI,QAAQ,UAAU,MAAM,GAAG;QAC/B,IAAI,OAAO,UAAU,MAAM,CAAC;QAC5B,IACC,CACC,AAAC,UAAU,OAAO,UAAU,OAAO,UAAU,OACzC,SAAS,OAAO,SAAS,OAAO,SAAS,GAC9C,KACG,UAAU,MACZ;YACD,MAAM,IAAI,aAAa;QACxB;QACA,IAAI,SAAS,iBAAiB,CAAC,OAAO;YACrC,qBAAqB;QACtB;QAEA,qBAAqB,MAAM;QAC3B,oBAAoB,MAAM,oBAAoB;QAE9C,IAAI,OAAO,YAAY,oBAAoB;YAC1C,QAAQ,UAAU,CAAC,kBAAkB;QACtC,OAAO,IAAI,SAAS,MAAM;YACzB,IAAI,CAAC,CAAC,QAAQ,KAAK,GAAG;gBACrB,IAAI,CAAC,cAAc;oBAClB,MAAM,IAAI,WAAW,wBAAwB,OAAO;gBACrD;gBACA,OAAO,KAAK;YACb;YACA,IAAI,SAAS,AAAC,IAAI,KAAM,MAAM,MAAM,EAAE;gBACrC,IAAI,OAAO,MAAM,OAAO;gBACxB,QAAQ,CAAC,CAAC;gBAEV,kEAAkE;gBAClE,gEAAgE;gBAChE,8DAA8D;gBAC9D,6DAA6D;gBAC7D,8DAA8D;gBAC9D,6DAA6D;gBAC7D,UAAU;gBACV,IAAI,SAAS,SAAS,QAAQ,CAAC,CAAC,mBAAmB,KAAK,GAAG,GAAG;oBAC7D,QAAQ,KAAK,GAAG;gBACjB,OAAO;oBACN,QAAQ,KAAK,CAAC,KAAK;gBACpB;YACD,OAAO;gBACN,QAAQ,OAAO,OAAO;gBACtB,QAAQ,KAAK,CAAC,KAAK;YACpB;YAEA,IAAI,SAAS,CAAC,oBAAoB;gBACjC,UAAU,CAAC,kBAAkB,GAAG;YACjC;QACD;IACD;IACA,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2896, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/has-tostringtag/shams.js"], "sourcesContent": ["'use strict';\n\nvar hasSymbols = require('has-symbols/shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasToStringTagShams() {\n\treturn hasSymbols() && !!Symbol.toStringTag;\n};\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,wBAAwB,GACxB,OAAO,OAAO,GAAG,SAAS;IACzB,OAAO,gBAAgB,CAAC,CAAC,OAAO,WAAW;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/es-set-tostringtag/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar $defineProperty = GetIntrinsic('%Object.defineProperty%', true);\n\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar hasOwn = require('hasown');\nvar $TypeError = require('es-errors/type');\n\nvar toStringTag = hasToStringTag ? Symbol.toStringTag : null;\n\n/** @type {import('.')} */\nmodule.exports = function setToStringTag(object, value) {\n\tvar overrideIfSet = arguments.length > 2 && !!arguments[2] && arguments[2].force;\n\tvar nonConfigurable = arguments.length > 2 && !!arguments[2] && arguments[2].nonConfigurable;\n\tif (\n\t\t(typeof overrideIfSet !== 'undefined' && typeof overrideIfSet !== 'boolean')\n\t\t|| (typeof nonConfigurable !== 'undefined' && typeof nonConfigurable !== 'boolean')\n\t) {\n\t\tthrow new $TypeError('if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans');\n\t}\n\tif (toStringTag && (overrideIfSet || !hasOwn(object, toStringTag))) {\n\t\tif ($defineProperty) {\n\t\t\t$defineProperty(object, toStringTag, {\n\t\t\t\tconfigurable: !nonConfigurable,\n\t\t\t\tenumerable: false,\n\t\t\t\tvalue: value,\n\t\t\t\twritable: false\n\t\t\t});\n\t\t} else {\n\t\t\tobject[toStringTag] = value; // eslint-disable-line no-param-reassign\n\t\t}\n\t}\n};\n"], "names": [], "mappings": "AAEA,IAAI;AAEJ,IAAI,kBAAkB,aAAa,2BAA2B;AAE9D,IAAI,iBAAiB;AACrB,IAAI;AACJ,IAAI;AAEJ,IAAI,cAAc,iBAAiB,OAAO,WAAW,GAAG;AAExD,wBAAwB,GACxB,OAAO,OAAO,GAAG,SAAS,eAAe,MAAM,EAAE,KAAK;IACrD,IAAI,gBAAgB,UAAU,MAAM,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,CAAC,KAAK;IAChF,IAAI,kBAAkB,UAAU,MAAM,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,CAAC,eAAe;IAC5F,IACC,AAAC,OAAO,kBAAkB,eAAe,OAAO,kBAAkB,aAC9D,OAAO,oBAAoB,eAAe,OAAO,oBAAoB,WACxE;QACD,MAAM,IAAI,WAAW;IACtB;IACA,IAAI,eAAe,CAAC,iBAAiB,CAAC,OAAO,QAAQ,YAAY,GAAG;QACnE,IAAI,iBAAiB;YACpB,gBAAgB,QAAQ,aAAa;gBACpC,cAAc,CAAC;gBACf,YAAY;gBACZ,OAAO;gBACP,UAAU;YACX;QACD,OAAO;YACN,MAAM,CAAC,YAAY,GAAG,OAAO,wCAAwC;QACtE;IACD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2933, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/form-data/lib/populate.js"], "sourcesContent": ["'use strict';\n\n// populates missing values\nmodule.exports = function (dst, src) {\n  Object.keys(src).forEach(function (prop) {\n    dst[prop] = dst[prop] || src[prop]; // eslint-disable-line no-param-reassign\n  });\n\n  return dst;\n};\n"], "names": [], "mappings": "AAEA,2BAA2B;AAC3B,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,GAAG;IACjC,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,SAAU,IAAI;QACrC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,EAAE,wCAAwC;IAC9E;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/form-data/lib/form_data.js"], "sourcesContent": ["'use strict';\n\nvar CombinedStream = require('combined-stream');\nvar util = require('util');\nvar path = require('path');\nvar http = require('http');\nvar https = require('https');\nvar parseUrl = require('url').parse;\nvar fs = require('fs');\nvar Stream = require('stream').Stream;\nvar crypto = require('crypto');\nvar mime = require('mime-types');\nvar asynckit = require('asynckit');\nvar setToStringTag = require('es-set-tostringtag');\nvar hasOwn = require('hasown');\nvar populate = require('./populate.js');\n\n/**\n * Create readable \"multipart/form-data\" streams.\n * Can be used to submit forms\n * and file uploads to other web applications.\n *\n * @constructor\n * @param {object} options - Properties to be added/overriden for FormData and CombinedStream\n */\nfunction FormData(options) {\n  if (!(this instanceof FormData)) {\n    return new FormData(options);\n  }\n\n  this._overheadLength = 0;\n  this._valueLength = 0;\n  this._valuesToMeasure = [];\n\n  CombinedStream.call(this);\n\n  options = options || {}; // eslint-disable-line no-param-reassign\n  for (var option in options) { // eslint-disable-line no-restricted-syntax\n    this[option] = options[option];\n  }\n}\n\n// make it a Stream\nutil.inherits(FormData, CombinedStream);\n\nFormData.LINE_BREAK = '\\r\\n';\nFormData.DEFAULT_CONTENT_TYPE = 'application/octet-stream';\n\nFormData.prototype.append = function (field, value, options) {\n  options = options || {}; // eslint-disable-line no-param-reassign\n\n  // allow filename as single option\n  if (typeof options === 'string') {\n    options = { filename: options }; // eslint-disable-line no-param-reassign\n  }\n\n  var append = CombinedStream.prototype.append.bind(this);\n\n  // all that streamy business can't handle numbers\n  if (typeof value === 'number' || value == null) {\n    value = String(value); // eslint-disable-line no-param-reassign\n  }\n\n  // https://github.com/felixge/node-form-data/issues/38\n  if (Array.isArray(value)) {\n    /*\n     * Please convert your array into string\n     * the way web server expects it\n     */\n    this._error(new Error('Arrays are not supported.'));\n    return;\n  }\n\n  var header = this._multiPartHeader(field, value, options);\n  var footer = this._multiPartFooter();\n\n  append(header);\n  append(value);\n  append(footer);\n\n  // pass along options.knownLength\n  this._trackLength(header, value, options);\n};\n\nFormData.prototype._trackLength = function (header, value, options) {\n  var valueLength = 0;\n\n  /*\n   * used w/ getLengthSync(), when length is known.\n   * e.g. for streaming directly from a remote server,\n   * w/ a known file a size, and not wanting to wait for\n   * incoming file to finish to get its size.\n   */\n  if (options.knownLength != null) {\n    valueLength += Number(options.knownLength);\n  } else if (Buffer.isBuffer(value)) {\n    valueLength = value.length;\n  } else if (typeof value === 'string') {\n    valueLength = Buffer.byteLength(value);\n  }\n\n  this._valueLength += valueLength;\n\n  // @check why add CRLF? does this account for custom/multiple CRLFs?\n  this._overheadLength += Buffer.byteLength(header) + FormData.LINE_BREAK.length;\n\n  // empty or either doesn't have path or not an http response or not a stream\n  if (!value || (!value.path && !(value.readable && hasOwn(value, 'httpVersion')) && !(value instanceof Stream))) {\n    return;\n  }\n\n  // no need to bother with the length\n  if (!options.knownLength) {\n    this._valuesToMeasure.push(value);\n  }\n};\n\nFormData.prototype._lengthRetriever = function (value, callback) {\n  if (hasOwn(value, 'fd')) {\n    // take read range into a account\n    // `end` = Infinity –> read file till the end\n    //\n    // TODO: Looks like there is bug in Node fs.createReadStream\n    // it doesn't respect `end` options without `start` options\n    // Fix it when node fixes it.\n    // https://github.com/joyent/node/issues/7819\n    if (value.end != undefined && value.end != Infinity && value.start != undefined) {\n      // when end specified\n      // no need to calculate range\n      // inclusive, starts with 0\n      callback(null, value.end + 1 - (value.start ? value.start : 0)); // eslint-disable-line callback-return\n\n      // not that fast snoopy\n    } else {\n      // still need to fetch file size from fs\n      fs.stat(value.path, function (err, stat) {\n        if (err) {\n          callback(err);\n          return;\n        }\n\n        // update final size based on the range options\n        var fileSize = stat.size - (value.start ? value.start : 0);\n        callback(null, fileSize);\n      });\n    }\n\n    // or http response\n  } else if (hasOwn(value, 'httpVersion')) {\n    callback(null, Number(value.headers['content-length'])); // eslint-disable-line callback-return\n\n    // or request stream http://github.com/mikeal/request\n  } else if (hasOwn(value, 'httpModule')) {\n    // wait till response come back\n    value.on('response', function (response) {\n      value.pause();\n      callback(null, Number(response.headers['content-length']));\n    });\n    value.resume();\n\n    // something else\n  } else {\n    callback('Unknown stream'); // eslint-disable-line callback-return\n  }\n};\n\nFormData.prototype._multiPartHeader = function (field, value, options) {\n  /*\n   * custom header specified (as string)?\n   * it becomes responsible for boundary\n   * (e.g. to handle extra CRLFs on .NET servers)\n   */\n  if (typeof options.header === 'string') {\n    return options.header;\n  }\n\n  var contentDisposition = this._getContentDisposition(value, options);\n  var contentType = this._getContentType(value, options);\n\n  var contents = '';\n  var headers = {\n    // add custom disposition as third element or keep it two elements if not\n    'Content-Disposition': ['form-data', 'name=\"' + field + '\"'].concat(contentDisposition || []),\n    // if no content type. allow it to be empty array\n    'Content-Type': [].concat(contentType || [])\n  };\n\n  // allow custom headers.\n  if (typeof options.header === 'object') {\n    populate(headers, options.header);\n  }\n\n  var header;\n  for (var prop in headers) { // eslint-disable-line no-restricted-syntax\n    if (hasOwn(headers, prop)) {\n      header = headers[prop];\n\n      // skip nullish headers.\n      if (header == null) {\n        continue; // eslint-disable-line no-restricted-syntax, no-continue\n      }\n\n      // convert all headers to arrays.\n      if (!Array.isArray(header)) {\n        header = [header];\n      }\n\n      // add non-empty headers.\n      if (header.length) {\n        contents += prop + ': ' + header.join('; ') + FormData.LINE_BREAK;\n      }\n    }\n  }\n\n  return '--' + this.getBoundary() + FormData.LINE_BREAK + contents + FormData.LINE_BREAK;\n};\n\nFormData.prototype._getContentDisposition = function (value, options) { // eslint-disable-line consistent-return\n  var filename;\n\n  if (typeof options.filepath === 'string') {\n    // custom filepath for relative paths\n    filename = path.normalize(options.filepath).replace(/\\\\/g, '/');\n  } else if (options.filename || (value && (value.name || value.path))) {\n    /*\n     * custom filename take precedence\n     * formidable and the browser add a name property\n     * fs- and request- streams have path property\n     */\n    filename = path.basename(options.filename || (value && (value.name || value.path)));\n  } else if (value && value.readable && hasOwn(value, 'httpVersion')) {\n    // or try http response\n    filename = path.basename(value.client._httpMessage.path || '');\n  }\n\n  if (filename) {\n    return 'filename=\"' + filename + '\"';\n  }\n};\n\nFormData.prototype._getContentType = function (value, options) {\n  // use custom content-type above all\n  var contentType = options.contentType;\n\n  // or try `name` from formidable, browser\n  if (!contentType && value && value.name) {\n    contentType = mime.lookup(value.name);\n  }\n\n  // or try `path` from fs-, request- streams\n  if (!contentType && value && value.path) {\n    contentType = mime.lookup(value.path);\n  }\n\n  // or if it's http-reponse\n  if (!contentType && value && value.readable && hasOwn(value, 'httpVersion')) {\n    contentType = value.headers['content-type'];\n  }\n\n  // or guess it from the filepath or filename\n  if (!contentType && (options.filepath || options.filename)) {\n    contentType = mime.lookup(options.filepath || options.filename);\n  }\n\n  // fallback to the default content type if `value` is not simple value\n  if (!contentType && value && typeof value === 'object') {\n    contentType = FormData.DEFAULT_CONTENT_TYPE;\n  }\n\n  return contentType;\n};\n\nFormData.prototype._multiPartFooter = function () {\n  return function (next) {\n    var footer = FormData.LINE_BREAK;\n\n    var lastPart = this._streams.length === 0;\n    if (lastPart) {\n      footer += this._lastBoundary();\n    }\n\n    next(footer);\n  }.bind(this);\n};\n\nFormData.prototype._lastBoundary = function () {\n  return '--' + this.getBoundary() + '--' + FormData.LINE_BREAK;\n};\n\nFormData.prototype.getHeaders = function (userHeaders) {\n  var header;\n  var formHeaders = {\n    'content-type': 'multipart/form-data; boundary=' + this.getBoundary()\n  };\n\n  for (header in userHeaders) { // eslint-disable-line no-restricted-syntax\n    if (hasOwn(userHeaders, header)) {\n      formHeaders[header.toLowerCase()] = userHeaders[header];\n    }\n  }\n\n  return formHeaders;\n};\n\nFormData.prototype.setBoundary = function (boundary) {\n  if (typeof boundary !== 'string') {\n    throw new TypeError('FormData boundary must be a string');\n  }\n  this._boundary = boundary;\n};\n\nFormData.prototype.getBoundary = function () {\n  if (!this._boundary) {\n    this._generateBoundary();\n  }\n\n  return this._boundary;\n};\n\nFormData.prototype.getBuffer = function () {\n  var dataBuffer = new Buffer.alloc(0); // eslint-disable-line new-cap\n  var boundary = this.getBoundary();\n\n  // Create the form content. Add Line breaks to the end of data.\n  for (var i = 0, len = this._streams.length; i < len; i++) {\n    if (typeof this._streams[i] !== 'function') {\n      // Add content to the buffer.\n      if (Buffer.isBuffer(this._streams[i])) {\n        dataBuffer = Buffer.concat([dataBuffer, this._streams[i]]);\n      } else {\n        dataBuffer = Buffer.concat([dataBuffer, Buffer.from(this._streams[i])]);\n      }\n\n      // Add break after content.\n      if (typeof this._streams[i] !== 'string' || this._streams[i].substring(2, boundary.length + 2) !== boundary) {\n        dataBuffer = Buffer.concat([dataBuffer, Buffer.from(FormData.LINE_BREAK)]);\n      }\n    }\n  }\n\n  // Add the footer and return the Buffer object.\n  return Buffer.concat([dataBuffer, Buffer.from(this._lastBoundary())]);\n};\n\nFormData.prototype._generateBoundary = function () {\n  // This generates a 50 character boundary similar to those used by Firefox.\n\n  // They are optimized for boyer-moore parsing.\n  this._boundary = '--------------------------' + crypto.randomBytes(12).toString('hex');\n};\n\n// Note: getLengthSync DOESN'T calculate streams length\n// As workaround one can calculate file size manually and add it as knownLength option\nFormData.prototype.getLengthSync = function () {\n  var knownLength = this._overheadLength + this._valueLength;\n\n  // Don't get confused, there are 3 \"internal\" streams for each keyval pair so it basically checks if there is any value added to the form\n  if (this._streams.length) {\n    knownLength += this._lastBoundary().length;\n  }\n\n  // https://github.com/form-data/form-data/issues/40\n  if (!this.hasKnownLength()) {\n    /*\n     * Some async length retrievers are present\n     * therefore synchronous length calculation is false.\n     * Please use getLength(callback) to get proper length\n     */\n    this._error(new Error('Cannot calculate proper length in synchronous way.'));\n  }\n\n  return knownLength;\n};\n\n// Public API to check if length of added values is known\n// https://github.com/form-data/form-data/issues/196\n// https://github.com/form-data/form-data/issues/262\nFormData.prototype.hasKnownLength = function () {\n  var hasKnownLength = true;\n\n  if (this._valuesToMeasure.length) {\n    hasKnownLength = false;\n  }\n\n  return hasKnownLength;\n};\n\nFormData.prototype.getLength = function (cb) {\n  var knownLength = this._overheadLength + this._valueLength;\n\n  if (this._streams.length) {\n    knownLength += this._lastBoundary().length;\n  }\n\n  if (!this._valuesToMeasure.length) {\n    process.nextTick(cb.bind(this, null, knownLength));\n    return;\n  }\n\n  asynckit.parallel(this._valuesToMeasure, this._lengthRetriever, function (err, values) {\n    if (err) {\n      cb(err);\n      return;\n    }\n\n    values.forEach(function (length) {\n      knownLength += length;\n    });\n\n    cb(null, knownLength);\n  });\n};\n\nFormData.prototype.submit = function (params, cb) {\n  var request;\n  var options;\n  var defaults = { method: 'post' };\n\n  // parse provided url if it's string or treat it as options object\n  if (typeof params === 'string') {\n    params = parseUrl(params); // eslint-disable-line no-param-reassign\n    /* eslint sort-keys: 0 */\n    options = populate({\n      port: params.port,\n      path: params.pathname,\n      host: params.hostname,\n      protocol: params.protocol\n    }, defaults);\n  } else { // use custom params\n    options = populate(params, defaults);\n    // if no port provided use default one\n    if (!options.port) {\n      options.port = options.protocol === 'https:' ? 443 : 80;\n    }\n  }\n\n  // put that good code in getHeaders to some use\n  options.headers = this.getHeaders(params.headers);\n\n  // https if specified, fallback to http in any other case\n  if (options.protocol === 'https:') {\n    request = https.request(options);\n  } else {\n    request = http.request(options);\n  }\n\n  // get content length and fire away\n  this.getLength(function (err, length) {\n    if (err && err !== 'Unknown stream') {\n      this._error(err);\n      return;\n    }\n\n    // add content length\n    if (length) {\n      request.setHeader('Content-Length', length);\n    }\n\n    this.pipe(request);\n    if (cb) {\n      var onResponse;\n\n      var callback = function (error, responce) {\n        request.removeListener('error', callback);\n        request.removeListener('response', onResponse);\n\n        return cb.call(this, error, responce); // eslint-disable-line no-invalid-this\n      };\n\n      onResponse = callback.bind(this, null);\n\n      request.on('error', callback);\n      request.on('response', onResponse);\n    }\n  }.bind(this));\n\n  return request;\n};\n\nFormData.prototype._error = function (err) {\n  if (!this.error) {\n    this.error = err;\n    this.pause();\n    this.emit('error', err);\n  }\n};\n\nFormData.prototype.toString = function () {\n  return '[object FormData]';\n};\nsetToStringTag(FormData, 'FormData');\n\n// Public API\nmodule.exports = FormData;\n"], "names": [], "mappings": "AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,WAAW,iEAAe,KAAK;AACnC,IAAI;AACJ,IAAI,SAAS,uEAAkB,MAAM;AACrC,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;;;;;CAOC,GACD,SAAS,SAAS,OAAO;IACvB,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,GAAG;QAC/B,OAAO,IAAI,SAAS;IACtB;IAEA,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,gBAAgB,GAAG,EAAE;IAE1B,eAAe,IAAI,CAAC,IAAI;IAExB,UAAU,WAAW,CAAC,GAAG,wCAAwC;IACjE,IAAK,IAAI,UAAU,QAAS;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO;IAChC;AACF;AAEA,mBAAmB;AACnB,KAAK,QAAQ,CAAC,UAAU;AAExB,SAAS,UAAU,GAAG;AACtB,SAAS,oBAAoB,GAAG;AAEhC,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,OAAO;IACzD,UAAU,WAAW,CAAC,GAAG,wCAAwC;IAEjE,kCAAkC;IAClC,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YAAE,UAAU;QAAQ,GAAG,wCAAwC;IAC3E;IAEA,IAAI,SAAS,eAAe,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;IAEtD,iDAAiD;IACjD,IAAI,OAAO,UAAU,YAAY,SAAS,MAAM;QAC9C,QAAQ,OAAO,QAAQ,wCAAwC;IACjE;IAEA,sDAAsD;IACtD,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB;;;KAGC,GACD,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM;QACtB;IACF;IAEA,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC,OAAO,OAAO;IACjD,IAAI,SAAS,IAAI,CAAC,gBAAgB;IAElC,OAAO;IACP,OAAO;IACP,OAAO;IAEP,iCAAiC;IACjC,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO;AACnC;AAEA,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,KAAK,EAAE,OAAO;IAChE,IAAI,cAAc;IAElB;;;;;GAKC,GACD,IAAI,QAAQ,WAAW,IAAI,MAAM;QAC/B,eAAe,OAAO,QAAQ,WAAW;IAC3C,OAAO,IAAI,OAAO,QAAQ,CAAC,QAAQ;QACjC,cAAc,MAAM,MAAM;IAC5B,OAAO,IAAI,OAAO,UAAU,UAAU;QACpC,cAAc,OAAO,UAAU,CAAC;IAClC;IAEA,IAAI,CAAC,YAAY,IAAI;IAErB,oEAAoE;IACpE,IAAI,CAAC,eAAe,IAAI,OAAO,UAAU,CAAC,UAAU,SAAS,UAAU,CAAC,MAAM;IAE9E,4EAA4E;IAC5E,IAAI,CAAC,SAAU,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,MAAM,QAAQ,IAAI,OAAO,OAAO,cAAc,KAAK,CAAC,CAAC,iBAAiB,MAAM,GAAI;QAC9G;IACF;IAEA,oCAAoC;IACpC,IAAI,CAAC,QAAQ,WAAW,EAAE;QACxB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IAC7B;AACF;AAEA,SAAS,SAAS,CAAC,gBAAgB,GAAG,SAAU,KAAK,EAAE,QAAQ;IAC7D,IAAI,OAAO,OAAO,OAAO;QACvB,iCAAiC;QACjC,6CAA6C;QAC7C,EAAE;QACF,4DAA4D;QAC5D,2DAA2D;QAC3D,6BAA6B;QAC7B,6CAA6C;QAC7C,IAAI,MAAM,GAAG,IAAI,aAAa,MAAM,GAAG,IAAI,YAAY,MAAM,KAAK,IAAI,WAAW;YAC/E,qBAAqB;YACrB,6BAA6B;YAC7B,2BAA2B;YAC3B,SAAS,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG,CAAC,IAAI,sCAAsC;QAEvG,uBAAuB;QACzB,OAAO;YACL,wCAAwC;YACxC,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,SAAU,GAAG,EAAE,IAAI;gBACrC,IAAI,KAAK;oBACP,SAAS;oBACT;gBACF;gBAEA,+CAA+C;gBAC/C,IAAI,WAAW,KAAK,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG,CAAC;gBACzD,SAAS,MAAM;YACjB;QACF;IAEA,mBAAmB;IACrB,OAAO,IAAI,OAAO,OAAO,gBAAgB;QACvC,SAAS,MAAM,OAAO,MAAM,OAAO,CAAC,iBAAiB,IAAI,sCAAsC;IAE/F,qDAAqD;IACvD,OAAO,IAAI,OAAO,OAAO,eAAe;QACtC,+BAA+B;QAC/B,MAAM,EAAE,CAAC,YAAY,SAAU,QAAQ;YACrC,MAAM,KAAK;YACX,SAAS,MAAM,OAAO,SAAS,OAAO,CAAC,iBAAiB;QAC1D;QACA,MAAM,MAAM;IAEZ,iBAAiB;IACnB,OAAO;QACL,SAAS,mBAAmB,sCAAsC;IACpE;AACF;AAEA,SAAS,SAAS,CAAC,gBAAgB,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,OAAO;IACnE;;;;GAIC,GACD,IAAI,OAAO,QAAQ,MAAM,KAAK,UAAU;QACtC,OAAO,QAAQ,MAAM;IACvB;IAEA,IAAI,qBAAqB,IAAI,CAAC,sBAAsB,CAAC,OAAO;IAC5D,IAAI,cAAc,IAAI,CAAC,eAAe,CAAC,OAAO;IAE9C,IAAI,WAAW;IACf,IAAI,UAAU;QACZ,yEAAyE;QACzE,uBAAuB;YAAC;YAAa,WAAW,QAAQ;SAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;QAC5F,iDAAiD;QACjD,gBAAgB,EAAE,CAAC,MAAM,CAAC,eAAe,EAAE;IAC7C;IAEA,wBAAwB;IACxB,IAAI,OAAO,QAAQ,MAAM,KAAK,UAAU;QACtC,SAAS,SAAS,QAAQ,MAAM;IAClC;IAEA,IAAI;IACJ,IAAK,IAAI,QAAQ,QAAS;QACxB,IAAI,OAAO,SAAS,OAAO;YACzB,SAAS,OAAO,CAAC,KAAK;YAEtB,wBAAwB;YACxB,IAAI,UAAU,MAAM;gBAClB,UAAU,wDAAwD;YACpE;YAEA,iCAAiC;YACjC,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;gBAC1B,SAAS;oBAAC;iBAAO;YACnB;YAEA,yBAAyB;YACzB,IAAI,OAAO,MAAM,EAAE;gBACjB,YAAY,OAAO,OAAO,OAAO,IAAI,CAAC,QAAQ,SAAS,UAAU;YACnE;QACF;IACF;IAEA,OAAO,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,UAAU,GAAG,WAAW,SAAS,UAAU;AACzF;AAEA,SAAS,SAAS,CAAC,sBAAsB,GAAG,SAAU,KAAK,EAAE,OAAO;IAClE,IAAI;IAEJ,IAAI,OAAO,QAAQ,QAAQ,KAAK,UAAU;QACxC,qCAAqC;QACrC,WAAW,KAAK,SAAS,CAAC,QAAQ,QAAQ,EAAE,OAAO,CAAC,OAAO;IAC7D,OAAO,IAAI,QAAQ,QAAQ,IAAK,SAAS,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,GAAI;QACpE;;;;KAIC,GACD,WAAW,KAAK,QAAQ,CAAC,QAAQ,QAAQ,IAAK,SAAS,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI;IAClF,OAAO,IAAI,SAAS,MAAM,QAAQ,IAAI,OAAO,OAAO,gBAAgB;QAClE,uBAAuB;QACvB,WAAW,KAAK,QAAQ,CAAC,MAAM,MAAM,CAAC,YAAY,CAAC,IAAI,IAAI;IAC7D;IAEA,IAAI,UAAU;QACZ,OAAO,eAAe,WAAW;IACnC;AACF;AAEA,SAAS,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,OAAO;IAC3D,oCAAoC;IACpC,IAAI,cAAc,QAAQ,WAAW;IAErC,yCAAyC;IACzC,IAAI,CAAC,eAAe,SAAS,MAAM,IAAI,EAAE;QACvC,cAAc,KAAK,MAAM,CAAC,MAAM,IAAI;IACtC;IAEA,2CAA2C;IAC3C,IAAI,CAAC,eAAe,SAAS,MAAM,IAAI,EAAE;QACvC,cAAc,KAAK,MAAM,CAAC,MAAM,IAAI;IACtC;IAEA,0BAA0B;IAC1B,IAAI,CAAC,eAAe,SAAS,MAAM,QAAQ,IAAI,OAAO,OAAO,gBAAgB;QAC3E,cAAc,MAAM,OAAO,CAAC,eAAe;IAC7C;IAEA,4CAA4C;IAC5C,IAAI,CAAC,eAAe,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,GAAG;QAC1D,cAAc,KAAK,MAAM,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ;IAChE;IAEA,sEAAsE;IACtE,IAAI,CAAC,eAAe,SAAS,OAAO,UAAU,UAAU;QACtD,cAAc,SAAS,oBAAoB;IAC7C;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,gBAAgB,GAAG;IACpC,OAAO,CAAA,SAAU,IAAI;QACnB,IAAI,SAAS,SAAS,UAAU;QAEhC,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK;QACxC,IAAI,UAAU;YACZ,UAAU,IAAI,CAAC,aAAa;QAC9B;QAEA,KAAK;IACP,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AAEA,SAAS,SAAS,CAAC,aAAa,GAAG;IACjC,OAAO,OAAO,IAAI,CAAC,WAAW,KAAK,OAAO,SAAS,UAAU;AAC/D;AAEA,SAAS,SAAS,CAAC,UAAU,GAAG,SAAU,WAAW;IACnD,IAAI;IACJ,IAAI,cAAc;QAChB,gBAAgB,mCAAmC,IAAI,CAAC,WAAW;IACrE;IAEA,IAAK,UAAU,YAAa;QAC1B,IAAI,OAAO,aAAa,SAAS;YAC/B,WAAW,CAAC,OAAO,WAAW,GAAG,GAAG,WAAW,CAAC,OAAO;QACzD;IACF;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ;IACjD,IAAI,OAAO,aAAa,UAAU;QAChC,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA,SAAS,SAAS,CAAC,WAAW,GAAG;IAC/B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACnB,IAAI,CAAC,iBAAiB;IACxB;IAEA,OAAO,IAAI,CAAC,SAAS;AACvB;AAEA,SAAS,SAAS,CAAC,SAAS,GAAG;IAC7B,IAAI,aAAa,IAAI,OAAO,KAAK,CAAC,IAAI,8BAA8B;IACpE,IAAI,WAAW,IAAI,CAAC,WAAW;IAE/B,+DAA+D;IAC/D,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;QACxD,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,YAAY;YAC1C,6BAA6B;YAC7B,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;gBACrC,aAAa,OAAO,MAAM,CAAC;oBAAC;oBAAY,IAAI,CAAC,QAAQ,CAAC,EAAE;iBAAC;YAC3D,OAAO;gBACL,aAAa,OAAO,MAAM,CAAC;oBAAC;oBAAY,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;iBAAE;YACxE;YAEA,2BAA2B;YAC3B,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,YAAY,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,OAAO,UAAU;gBAC3G,aAAa,OAAO,MAAM,CAAC;oBAAC;oBAAY,OAAO,IAAI,CAAC,SAAS,UAAU;iBAAE;YAC3E;QACF;IACF;IAEA,+CAA+C;IAC/C,OAAO,OAAO,MAAM,CAAC;QAAC;QAAY,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;KAAI;AACtE;AAEA,SAAS,SAAS,CAAC,iBAAiB,GAAG;IACrC,2EAA2E;IAE3E,8CAA8C;IAC9C,IAAI,CAAC,SAAS,GAAG,+BAA+B,OAAO,WAAW,CAAC,IAAI,QAAQ,CAAC;AAClF;AAEA,uDAAuD;AACvD,sFAAsF;AACtF,SAAS,SAAS,CAAC,aAAa,GAAG;IACjC,IAAI,cAAc,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY;IAE1D,yIAAyI;IACzI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACxB,eAAe,IAAI,CAAC,aAAa,GAAG,MAAM;IAC5C;IAEA,mDAAmD;IACnD,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI;QAC1B;;;;KAIC,GACD,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM;IACxB;IAEA,OAAO;AACT;AAEA,yDAAyD;AACzD,oDAAoD;AACpD,oDAAoD;AACpD,SAAS,SAAS,CAAC,cAAc,GAAG;IAClC,IAAI,iBAAiB;IAErB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;QAChC,iBAAiB;IACnB;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,SAAS,GAAG,SAAU,EAAE;IACzC,IAAI,cAAc,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY;IAE1D,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACxB,eAAe,IAAI,CAAC,aAAa,GAAG,MAAM;IAC5C;IAEA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;QACjC,QAAQ,QAAQ,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM;QACrC;IACF;IAEA,SAAS,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE,SAAU,GAAG,EAAE,MAAM;QACnF,IAAI,KAAK;YACP,GAAG;YACH;QACF;QAEA,OAAO,OAAO,CAAC,SAAU,MAAM;YAC7B,eAAe;QACjB;QAEA,GAAG,MAAM;IACX;AACF;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,MAAM,EAAE,EAAE;IAC9C,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW;QAAE,QAAQ;IAAO;IAEhC,kEAAkE;IAClE,IAAI,OAAO,WAAW,UAAU;QAC9B,SAAS,SAAS,SAAS,wCAAwC;QACnE,uBAAuB,GACvB,UAAU,SAAS;YACjB,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,QAAQ;YACrB,MAAM,OAAO,QAAQ;YACrB,UAAU,OAAO,QAAQ;QAC3B,GAAG;IACL,OAAO;QACL,UAAU,SAAS,QAAQ;QAC3B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,IAAI,EAAE;YACjB,QAAQ,IAAI,GAAG,QAAQ,QAAQ,KAAK,WAAW,MAAM;QACvD;IACF;IAEA,+CAA+C;IAC/C,QAAQ,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,OAAO;IAEhD,yDAAyD;IACzD,IAAI,QAAQ,QAAQ,KAAK,UAAU;QACjC,UAAU,MAAM,OAAO,CAAC;IAC1B,OAAO;QACL,UAAU,KAAK,OAAO,CAAC;IACzB;IAEA,mCAAmC;IACnC,IAAI,CAAC,SAAS,CAAC,CAAA,SAAU,GAAG,EAAE,MAAM;QAClC,IAAI,OAAO,QAAQ,kBAAkB;YACnC,IAAI,CAAC,MAAM,CAAC;YACZ;QACF;QAEA,qBAAqB;QACrB,IAAI,QAAQ;YACV,QAAQ,SAAS,CAAC,kBAAkB;QACtC;QAEA,IAAI,CAAC,IAAI,CAAC;QACV,IAAI,IAAI;YACN,IAAI;YAEJ,IAAI,WAAW,SAAU,KAAK,EAAE,QAAQ;gBACtC,QAAQ,cAAc,CAAC,SAAS;gBAChC,QAAQ,cAAc,CAAC,YAAY;gBAEnC,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,OAAO,WAAW,sCAAsC;YAC/E;YAEA,aAAa,SAAS,IAAI,CAAC,IAAI,EAAE;YAEjC,QAAQ,EAAE,CAAC,SAAS;YACpB,QAAQ,EAAE,CAAC,YAAY;QACzB;IACF,CAAA,EAAE,IAAI,CAAC,IAAI;IAEX,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;IACvC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,IAAI,CAAC,SAAS;IACrB;AACF;AAEA,SAAS,SAAS,CAAC,QAAQ,GAAG;IAC5B,OAAO;AACT;AACA,eAAe,UAAU;AAEzB,aAAa;AACb,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/platform/node/classes/FormData.js"], "sourcesContent": ["import FormData from 'form-data';\n\nexport default FormData;\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,2JAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/toFormData.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\n// temporary hotfix to avoid circular references until AxiosURLSearchParams is refactored\nimport PlatformFormData from '../platform/node/classes/FormData.js';\n\n/**\n * Determines if the given thing is a array or js object.\n *\n * @param {string} thing - The object or array to be visited.\n *\n * @returns {boolean}\n */\nfunction isVisitable(thing) {\n  return utils.isPlainObject(thing) || utils.isArray(thing);\n}\n\n/**\n * It removes the brackets from the end of a string\n *\n * @param {string} key - The key of the parameter.\n *\n * @returns {string} the key without the brackets.\n */\nfunction removeBrackets(key) {\n  return utils.endsWith(key, '[]') ? key.slice(0, -2) : key;\n}\n\n/**\n * It takes a path, a key, and a boolean, and returns a string\n *\n * @param {string} path - The path to the current key.\n * @param {string} key - The key of the current object being iterated over.\n * @param {string} dots - If true, the key will be rendered with dots instead of brackets.\n *\n * @returns {string} The path to the current key.\n */\nfunction renderKey(path, key, dots) {\n  if (!path) return key;\n  return path.concat(key).map(function each(token, i) {\n    // eslint-disable-next-line no-param-reassign\n    token = removeBrackets(token);\n    return !dots && i ? '[' + token + ']' : token;\n  }).join(dots ? '.' : '');\n}\n\n/**\n * If the array is an array and none of its elements are visitable, then it's a flat array.\n *\n * @param {Array<any>} arr - The array to check\n *\n * @returns {boolean}\n */\nfunction isFlatArray(arr) {\n  return utils.isArray(arr) && !arr.some(isVisitable);\n}\n\nconst predicates = utils.toFlatObject(utils, {}, null, function filter(prop) {\n  return /^is[A-Z]/.test(prop);\n});\n\n/**\n * Convert a data object to FormData\n *\n * @param {Object} obj\n * @param {?Object} [formData]\n * @param {?Object} [options]\n * @param {Function} [options.visitor]\n * @param {Boolean} [options.metaTokens = true]\n * @param {Boolean} [options.dots = false]\n * @param {?Boolean} [options.indexes = false]\n *\n * @returns {Object}\n **/\n\n/**\n * It converts an object into a FormData object\n *\n * @param {Object<any, any>} obj - The object to convert to form data.\n * @param {string} formData - The FormData object to append to.\n * @param {Object<string, any>} options\n *\n * @returns\n */\nfunction toFormData(obj, formData, options) {\n  if (!utils.isObject(obj)) {\n    throw new TypeError('target must be an object');\n  }\n\n  // eslint-disable-next-line no-param-reassign\n  formData = formData || new (PlatformFormData || FormData)();\n\n  // eslint-disable-next-line no-param-reassign\n  options = utils.toFlatObject(options, {\n    metaTokens: true,\n    dots: false,\n    indexes: false\n  }, false, function defined(option, source) {\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    return !utils.isUndefined(source[option]);\n  });\n\n  const metaTokens = options.metaTokens;\n  // eslint-disable-next-line no-use-before-define\n  const visitor = options.visitor || defaultVisitor;\n  const dots = options.dots;\n  const indexes = options.indexes;\n  const _Blob = options.Blob || typeof Blob !== 'undefined' && Blob;\n  const useBlob = _Blob && utils.isSpecCompliantForm(formData);\n\n  if (!utils.isFunction(visitor)) {\n    throw new TypeError('visitor must be a function');\n  }\n\n  function convertValue(value) {\n    if (value === null) return '';\n\n    if (utils.isDate(value)) {\n      return value.toISOString();\n    }\n\n    if (utils.isBoolean(value)) {\n      return value.toString();\n    }\n\n    if (!useBlob && utils.isBlob(value)) {\n      throw new AxiosError('Blob is not supported. Use a Buffer instead.');\n    }\n\n    if (utils.isArrayBuffer(value) || utils.isTypedArray(value)) {\n      return useBlob && typeof Blob === 'function' ? new Blob([value]) : Buffer.from(value);\n    }\n\n    return value;\n  }\n\n  /**\n   * Default visitor.\n   *\n   * @param {*} value\n   * @param {String|Number} key\n   * @param {Array<String|Number>} path\n   * @this {FormData}\n   *\n   * @returns {boolean} return true to visit the each prop of the value recursively\n   */\n  function defaultVisitor(value, key, path) {\n    let arr = value;\n\n    if (value && !path && typeof value === 'object') {\n      if (utils.endsWith(key, '{}')) {\n        // eslint-disable-next-line no-param-reassign\n        key = metaTokens ? key : key.slice(0, -2);\n        // eslint-disable-next-line no-param-reassign\n        value = JSON.stringify(value);\n      } else if (\n        (utils.isArray(value) && isFlatArray(value)) ||\n        ((utils.isFileList(value) || utils.endsWith(key, '[]')) && (arr = utils.toArray(value))\n        )) {\n        // eslint-disable-next-line no-param-reassign\n        key = removeBrackets(key);\n\n        arr.forEach(function each(el, index) {\n          !(utils.isUndefined(el) || el === null) && formData.append(\n            // eslint-disable-next-line no-nested-ternary\n            indexes === true ? renderKey([key], index, dots) : (indexes === null ? key : key + '[]'),\n            convertValue(el)\n          );\n        });\n        return false;\n      }\n    }\n\n    if (isVisitable(value)) {\n      return true;\n    }\n\n    formData.append(renderKey(path, key, dots), convertValue(value));\n\n    return false;\n  }\n\n  const stack = [];\n\n  const exposedHelpers = Object.assign(predicates, {\n    defaultVisitor,\n    convertValue,\n    isVisitable\n  });\n\n  function build(value, path) {\n    if (utils.isUndefined(value)) return;\n\n    if (stack.indexOf(value) !== -1) {\n      throw Error('Circular reference detected in ' + path.join('.'));\n    }\n\n    stack.push(value);\n\n    utils.forEach(value, function each(el, key) {\n      const result = !(utils.isUndefined(el) || el === null) && visitor.call(\n        formData, el, utils.isString(key) ? key.trim() : key, path, exposedHelpers\n      );\n\n      if (result === true) {\n        build(el, path ? path.concat(key) : [key]);\n      }\n    });\n\n    stack.pop();\n  }\n\n  if (!utils.isObject(obj)) {\n    throw new TypeError('data must be an object');\n  }\n\n  build(obj);\n\n  return formData;\n}\n\nexport default toFormData;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,yFAAyF;AACzF;AALA;;;;AAOA;;;;;;CAMC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,gJAAK,CAAC,aAAa,CAAC,UAAU,gJAAK,CAAC,OAAO,CAAC;AACrD;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,GAAG;IACzB,OAAO,gJAAK,CAAC,QAAQ,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK;AACxD;AAEA;;;;;;;;CAQC,GACD,SAAS,UAAU,IAAI,EAAE,GAAG,EAAE,IAAI;IAChC,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;QAChD,6CAA6C;QAC7C,QAAQ,eAAe;QACvB,OAAO,CAAC,QAAQ,IAAI,MAAM,QAAQ,MAAM;IAC1C,GAAG,IAAI,CAAC,OAAO,MAAM;AACvB;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,GAAG;IACtB,OAAO,gJAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;AACzC;AAEA,MAAM,aAAa,gJAAK,CAAC,YAAY,CAAC,gJAAK,EAAE,CAAC,GAAG,MAAM,SAAS,OAAO,IAAI;IACzE,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA;;;;;;;;;;;;EAYE,GAEF;;;;;;;;CAQC,GACD,SAAS,WAAW,GAAG,EAAE,QAAQ,EAAE,OAAO;IACxC,IAAI,CAAC,gJAAK,CAAC,QAAQ,CAAC,MAAM;QACxB,MAAM,IAAI,UAAU;IACtB;IAEA,6CAA6C;IAC7C,WAAW,YAAY,IAAI,CAAC,kLAAgB,IAAI,QAAQ;IAExD,6CAA6C;IAC7C,UAAU,gJAAK,CAAC,YAAY,CAAC,SAAS;QACpC,YAAY;QACZ,MAAM;QACN,SAAS;IACX,GAAG,OAAO,SAAS,QAAQ,MAAM,EAAE,MAAM;QACvC,6CAA6C;QAC7C,OAAO,CAAC,gJAAK,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;IAC1C;IAEA,MAAM,aAAa,QAAQ,UAAU;IACrC,gDAAgD;IAChD,MAAM,UAAU,QAAQ,OAAO,IAAI;IACnC,MAAM,OAAO,QAAQ,IAAI;IACzB,MAAM,UAAU,QAAQ,OAAO;IAC/B,MAAM,QAAQ,QAAQ,IAAI,IAAI,OAAO,SAAS,eAAe;IAC7D,MAAM,UAAU,SAAS,gJAAK,CAAC,mBAAmB,CAAC;IAEnD,IAAI,CAAC,gJAAK,CAAC,UAAU,CAAC,UAAU;QAC9B,MAAM,IAAI,UAAU;IACtB;IAEA,SAAS,aAAa,KAAK;QACzB,IAAI,UAAU,MAAM,OAAO;QAE3B,IAAI,gJAAK,CAAC,MAAM,CAAC,QAAQ;YACvB,OAAO,MAAM,WAAW;QAC1B;QAEA,IAAI,gJAAK,CAAC,SAAS,CAAC,QAAQ;YAC1B,OAAO,MAAM,QAAQ;QACvB;QAEA,IAAI,CAAC,WAAW,gJAAK,CAAC,MAAM,CAAC,QAAQ;YACnC,MAAM,IAAI,6JAAU,CAAC;QACvB;QAEA,IAAI,gJAAK,CAAC,aAAa,CAAC,UAAU,gJAAK,CAAC,YAAY,CAAC,QAAQ;YAC3D,OAAO,WAAW,OAAO,SAAS,aAAa,IAAI,KAAK;gBAAC;aAAM,IAAI,OAAO,IAAI,CAAC;QACjF;QAEA,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,eAAe,KAAK,EAAE,GAAG,EAAE,IAAI;QACtC,IAAI,MAAM;QAEV,IAAI,SAAS,CAAC,QAAQ,OAAO,UAAU,UAAU;YAC/C,IAAI,gJAAK,CAAC,QAAQ,CAAC,KAAK,OAAO;gBAC7B,6CAA6C;gBAC7C,MAAM,aAAa,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC;gBACvC,6CAA6C;gBAC7C,QAAQ,KAAK,SAAS,CAAC;YACzB,OAAO,IACL,AAAC,gJAAK,CAAC,OAAO,CAAC,UAAU,YAAY,UACpC,CAAC,gJAAK,CAAC,UAAU,CAAC,UAAU,gJAAK,CAAC,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,gJAAK,CAAC,OAAO,CAAC,MAAM,GACnF;gBACH,6CAA6C;gBAC7C,MAAM,eAAe;gBAErB,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE,EAAE,KAAK;oBACjC,CAAC,CAAC,gJAAK,CAAC,WAAW,CAAC,OAAO,OAAO,IAAI,KAAK,SAAS,MAAM,CACxD,6CAA6C;oBAC7C,YAAY,OAAO,UAAU;wBAAC;qBAAI,EAAE,OAAO,QAAS,YAAY,OAAO,MAAM,MAAM,MACnF,aAAa;gBAEjB;gBACA,OAAO;YACT;QACF;QAEA,IAAI,YAAY,QAAQ;YACtB,OAAO;QACT;QAEA,SAAS,MAAM,CAAC,UAAU,MAAM,KAAK,OAAO,aAAa;QAEzD,OAAO;IACT;IAEA,MAAM,QAAQ,EAAE;IAEhB,MAAM,iBAAiB,OAAO,MAAM,CAAC,YAAY;QAC/C;QACA;QACA;IACF;IAEA,SAAS,MAAM,KAAK,EAAE,IAAI;QACxB,IAAI,gJAAK,CAAC,WAAW,CAAC,QAAQ;QAE9B,IAAI,MAAM,OAAO,CAAC,WAAW,CAAC,GAAG;YAC/B,MAAM,MAAM,oCAAoC,KAAK,IAAI,CAAC;QAC5D;QAEA,MAAM,IAAI,CAAC;QAEX,gJAAK,CAAC,OAAO,CAAC,OAAO,SAAS,KAAK,EAAE,EAAE,GAAG;YACxC,MAAM,SAAS,CAAC,CAAC,gJAAK,CAAC,WAAW,CAAC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,CACpE,UAAU,IAAI,gJAAK,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,KAAK,KAAK,MAAM;YAG9D,IAAI,WAAW,MAAM;gBACnB,MAAM,IAAI,OAAO,KAAK,MAAM,CAAC,OAAO;oBAAC;iBAAI;YAC3C;QACF;QAEA,MAAM,GAAG;IACX;IAEA,IAAI,CAAC,gJAAK,CAAC,QAAQ,CAAC,MAAM;QACxB,MAAM,IAAI,UAAU;IACtB;IAEA,MAAM;IAEN,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/AxiosURLSearchParams.js"], "sourcesContent": ["'use strict';\n\nimport toFormData from './toFormData.js';\n\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */\nfunction encode(str) {\n  const charMap = {\n    '!': '%21',\n    \"'\": '%27',\n    '(': '%28',\n    ')': '%29',\n    '~': '%7E',\n    '%20': '+',\n    '%00': '\\x00'\n  };\n  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n    return charMap[match];\n  });\n}\n\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */\nfunction AxiosURLSearchParams(params, options) {\n  this._pairs = [];\n\n  params && toFormData(params, this, options);\n}\n\nconst prototype = AxiosURLSearchParams.prototype;\n\nprototype.append = function append(name, value) {\n  this._pairs.push([name, value]);\n};\n\nprototype.toString = function toString(encoder) {\n  const _encode = encoder ? function(value) {\n    return encoder.call(this, value, encode);\n  } : encode;\n\n  return this._pairs.map(function each(pair) {\n    return _encode(pair[0]) + '=' + _encode(pair[1]);\n  }, '').join('&');\n};\n\nexport default AxiosURLSearchParams;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA;;;;;;;CAOC,GACD,SAAS,OAAO,GAAG;IACjB,MAAM,UAAU;QACd,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;IACT;IACA,OAAO,mBAAmB,KAAK,OAAO,CAAC,oBAAoB,SAAS,SAAS,KAAK;QAChF,OAAO,OAAO,CAAC,MAAM;IACvB;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,qBAAqB,MAAM,EAAE,OAAO;IAC3C,IAAI,CAAC,MAAM,GAAG,EAAE;IAEhB,UAAU,IAAA,gKAAU,EAAC,QAAQ,IAAI,EAAE;AACrC;AAEA,MAAM,YAAY,qBAAqB,SAAS;AAEhD,UAAU,MAAM,GAAG,SAAS,OAAO,IAAI,EAAE,KAAK;IAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QAAC;QAAM;KAAM;AAChC;AAEA,UAAU,QAAQ,GAAG,SAAS,SAAS,OAAO;IAC5C,MAAM,UAAU,UAAU,SAAS,KAAK;QACtC,OAAO,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO;IACnC,IAAI;IAEJ,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,KAAK,IAAI;QACvC,OAAO,QAAQ,IAAI,CAAC,EAAE,IAAI,MAAM,QAAQ,IAAI,CAAC,EAAE;IACjD,GAAG,IAAI,IAAI,CAAC;AACd;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3624, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/buildURL.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosURLSearchParams from '../helpers/AxiosURLSearchParams.js';\n\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?(object|Function)} options\n *\n * @returns {string} The formatted url\n */\nexport default function buildURL(url, params, options) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n  \n  const _encode = options && options.encode || encode;\n\n  if (utils.isFunction(options)) {\n    options = {\n      serialize: options\n    };\n  } \n\n  const serializeFn = options && options.serialize;\n\n  let serializedParams;\n\n  if (serializeFn) {\n    serializedParams = serializeFn(params, options);\n  } else {\n    serializedParams = utils.isURLSearchParams(params) ?\n      params.toString() :\n      new AxiosURLSearchParams(params, options).toString(_encode);\n  }\n\n  if (serializedParams) {\n    const hashmarkIndex = url.indexOf(\"#\");\n\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAKA;;;;;;;CAOC,GACD,SAAS,OAAO,GAAG;IACjB,OAAO,mBAAmB,KACxB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS;AACrB;AAWe,SAAS,SAAS,GAAG,EAAE,MAAM,EAAE,OAAO;IACnD,4BAA4B,GAC5B,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,UAAU,WAAW,QAAQ,MAAM,IAAI;IAE7C,IAAI,gJAAK,CAAC,UAAU,CAAC,UAAU;QAC7B,UAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,WAAW,QAAQ,SAAS;IAEhD,IAAI;IAEJ,IAAI,aAAa;QACf,mBAAmB,YAAY,QAAQ;IACzC,OAAO;QACL,mBAAmB,gJAAK,CAAC,iBAAiB,CAAC,UACzC,OAAO,QAAQ,KACf,IAAI,0KAAoB,CAAC,QAAQ,SAAS,QAAQ,CAAC;IACvD;IAEA,IAAI,kBAAkB;QACpB,MAAM,gBAAgB,IAAI,OAAO,CAAC;QAElC,IAAI,kBAAkB,CAAC,GAAG;YACxB,MAAM,IAAI,KAAK,CAAC,GAAG;QACrB;QACA,OAAO,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,MAAM,GAAG,IAAI;IACjD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/InterceptorManager.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\nclass InterceptorManager {\n  constructor() {\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  use(fulfilled, rejected, options) {\n    this.handlers.push({\n      fulfilled,\n      rejected,\n      synchronous: options ? options.synchronous : false,\n      runWhen: options ? options.runWhen : null\n    });\n    return this.handlers.length - 1;\n  }\n\n  /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */\n  eject(id) {\n    if (this.handlers[id]) {\n      this.handlers[id] = null;\n    }\n  }\n\n  /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */\n  clear() {\n    if (this.handlers) {\n      this.handlers = [];\n    }\n  }\n\n  /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */\n  forEach(fn) {\n    utils.forEach(this.handlers, function forEachHandler(h) {\n      if (h !== null) {\n        fn(h);\n      }\n    });\n  }\n}\n\nexport default InterceptorManager;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,EAAE;IACpB;IAEA;;;;;;;GAOC,GACD,IAAI,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE;QAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB;YACA;YACA,aAAa,UAAU,QAAQ,WAAW,GAAG;YAC7C,SAAS,UAAU,QAAQ,OAAO,GAAG;QACvC;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;IAChC;IAEA;;;;;;GAMC,GACD,MAAM,EAAE,EAAE;QACR,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACrB,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG;QACtB;IACF;IAEA;;;;GAIC,GACD,QAAQ;QACN,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,GAAG,EAAE;QACpB;IACF;IAEA;;;;;;;;;GASC,GACD,QAAQ,EAAE,EAAE;QACV,gJAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,eAAe,CAAC;YACpD,IAAI,MAAM,MAAM;gBACd,GAAG;YACL;QACF;IACF;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/defaults/transitional.js"], "sourcesContent": ["'use strict';\n\nexport default {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};\n"], "names": [], "mappings": ";;;;AAAA;uCAEe;IACb,mBAAmB;IACnB,mBAAmB;IACnB,qBAAqB;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/platform/node/classes/URLSearchParams.js"], "sourcesContent": ["'use strict';\n\nimport url from 'url';\nexport default url.URLSearchParams;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;uCAGe,0GAAG,CAAC,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/platform/node/index.js"], "sourcesContent": ["import crypto from 'crypto';\nimport URLSearchParams from './classes/URLSearchParams.js'\nimport FormData from './classes/FormData.js'\n\nconst ALPHA = 'abcdefghijklmnopqrstuvwxyz'\n\nconst DIGIT = '0123456789';\n\nconst ALPHABET = {\n  DIGIT,\n  ALPHA,\n  ALPHA_DIGIT: ALPHA + ALPHA.toUpperCase() + DIGIT\n}\n\nconst generateString = (size = 16, alphabet = ALPHABET.ALPHA_DIGIT) => {\n  let str = '';\n  const {length} = alphabet;\n  const randomValues = new Uint32Array(size);\n  crypto.randomFillSync(randomValues);\n  for (let i = 0; i < size; i++) {\n    str += alphabet[randomValues[i] % length];\n  }\n\n  return str;\n}\n\n\nexport default {\n  isNode: true,\n  classes: {\n    URLSearchParams,\n    FormData,\n    Blob: typeof Blob !== 'undefined' && Blob || null\n  },\n  ALPHABET,\n  generateString,\n  protocols: [ 'http', 'https', 'file', 'data' ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,QAAQ;AAEd,MAAM,QAAQ;AAEd,MAAM,WAAW;IACf;IACA;IACA,aAAa,QAAQ,MAAM,WAAW,KAAK;AAC7C;AAEA,MAAM,iBAAiB,CAAC,OAAO,EAAE,EAAE,WAAW,SAAS,WAAW;IAChE,IAAI,MAAM;IACV,MAAM,EAAC,MAAM,EAAC,GAAG;IACjB,MAAM,eAAe,IAAI,YAAY;IACrC,gHAAM,CAAC,cAAc,CAAC;IACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC7B,OAAO,QAAQ,CAAC,YAAY,CAAC,EAAE,GAAG,OAAO;IAC3C;IAEA,OAAO;AACT;uCAGe;IACb,QAAQ;IACR,SAAS;QACP,iBAAA,yLAAe;QACf,UAAA,kLAAQ;QACR,MAAM,OAAO,SAAS,eAAe,QAAQ;IAC/C;IACA;IACA;IACA,WAAW;QAAE;QAAQ;QAAS;QAAQ;KAAQ;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3815, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/platform/common/utils.js"], "sourcesContent": ["const hasBrowserEnv = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nconst _navigator = typeof navigator === 'object' && navigator || undefined;\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */\nconst hasStandardBrowserEnv = hasBrowserEnv &&\n  (!_navigator || ['ReactNative', 'NativeScript', 'NS'].indexOf(_navigator.product) < 0);\n\n/**\n * Determine if we're running in a standard browser webWorker environment\n *\n * Although the `isStandardBrowserEnv` method indicates that\n * `allows axios to run in a web worker`, the WebWorker will still be\n * filtered out due to its judgment standard\n * `typeof window !== 'undefined' && typeof document !== 'undefined'`.\n * This leads to a problem when axios post `FormData` in webWorker\n */\nconst hasStandardBrowserWebWorkerEnv = (() => {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    // eslint-disable-next-line no-undef\n    self instanceof WorkerGlobalScope &&\n    typeof self.importScripts === 'function'\n  );\n})();\n\nconst origin = hasBrowserEnv && window.location.href || 'http://localhost';\n\nexport {\n  hasBrowserEnv,\n  hasStandardBrowserWebWorkerEnv,\n  hasStandardBrowserEnv,\n  _navigator as navigator,\n  origin\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,MAAM,gBAAgB,gBAAkB,eAAe,OAAO,aAAa;AAE3E,MAAM,aAAa,OAAO,cAAc,YAAY,aAAa;AAEjE;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,wBAAwB,iBAC5B,CAAC,CAAC,cAAc;IAAC;IAAe;IAAgB;CAAK,CAAC,OAAO,CAAC,WAAW,OAAO,IAAI,CAAC;AAEvF;;;;;;;;CAQC,GACD,MAAM,iCAAiC,CAAC;IACtC,OACE,OAAO,sBAAsB,eAC7B,oCAAoC;IACpC,gBAAgB,qBAChB,OAAO,KAAK,aAAa,KAAK;AAElC,CAAC;AAED,MAAM,SAAS,iBAAiB,OAAO,QAAQ,CAAC,IAAI,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/platform/index.js"], "sourcesContent": ["import platform from './node/index.js';\nimport * as utils from './common/utils.js';\n\nexport default {\n  ...utils,\n  ...platform\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe;IACb,GAAG,2JAAK;IACR,GAAG,oKAAQ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3884, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/toURLEncodedForm.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport toFormData from './toFormData.js';\nimport platform from '../platform/index.js';\n\nexport default function toURLEncodedForm(data, options) {\n  return toFormData(data, new platform.classes.URLSearchParams(), {\n    visitor: function(value, key, path, helpers) {\n      if (platform.isNode && utils.isBuffer(value)) {\n        this.append(key, value.toString('base64'));\n        return false;\n      }\n\n      return helpers.defaultVisitor.apply(this, arguments);\n    },\n    ...options\n  });\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;AAMe,SAAS,iBAAiB,IAAI,EAAE,OAAO;IACpD,OAAO,IAAA,gKAAU,EAAC,MAAM,IAAI,4JAAQ,CAAC,OAAO,CAAC,eAAe,IAAI;QAC9D,SAAS,SAAS,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO;YACzC,IAAI,4JAAQ,CAAC,MAAM,IAAI,gJAAK,CAAC,QAAQ,CAAC,QAAQ;gBAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM,QAAQ,CAAC;gBAChC,OAAO;YACT;YAEA,OAAO,QAAQ,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE;QAC5C;QACA,GAAG,OAAO;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/formDataToJSON.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */\nfunction parsePropPath(name) {\n  // foo[x][y][z]\n  // foo.x.y.z\n  // foo-x-y-z\n  // foo x y z\n  return utils.matchAll(/\\w+|\\[(\\w*)]/g, name).map(match => {\n    return match[0] === '[]' ? '' : match[1] || match[0];\n  });\n}\n\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */\nfunction arrayToObject(arr) {\n  const obj = {};\n  const keys = Object.keys(arr);\n  let i;\n  const len = keys.length;\n  let key;\n  for (i = 0; i < len; i++) {\n    key = keys[i];\n    obj[key] = arr[key];\n  }\n  return obj;\n}\n\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */\nfunction formDataToJSON(formData) {\n  function buildPath(path, value, target, index) {\n    let name = path[index++];\n\n    if (name === '__proto__') return true;\n\n    const isNumericKey = Number.isFinite(+name);\n    const isLast = index >= path.length;\n    name = !name && utils.isArray(target) ? target.length : name;\n\n    if (isLast) {\n      if (utils.hasOwnProp(target, name)) {\n        target[name] = [target[name], value];\n      } else {\n        target[name] = value;\n      }\n\n      return !isNumericKey;\n    }\n\n    if (!target[name] || !utils.isObject(target[name])) {\n      target[name] = [];\n    }\n\n    const result = buildPath(path, value, target[name], index);\n\n    if (result && utils.isArray(target[name])) {\n      target[name] = arrayToObject(target[name]);\n    }\n\n    return !isNumericKey;\n  }\n\n  if (utils.isFormData(formData) && utils.isFunction(formData.entries)) {\n    const obj = {};\n\n    utils.forEachEntry(formData, (name, value) => {\n      buildPath(parsePropPath(name), value, obj, 0);\n    });\n\n    return obj;\n  }\n\n  return null;\n}\n\nexport default formDataToJSON;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA;;;;;;CAMC,GACD,SAAS,cAAc,IAAI;IACzB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,OAAO,gJAAK,CAAC,QAAQ,CAAC,iBAAiB,MAAM,GAAG,CAAC,CAAA;QAC/C,OAAO,KAAK,CAAC,EAAE,KAAK,OAAO,KAAK,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;IACtD;AACF;AAEA;;;;;;CAMC,GACD,SAAS,cAAc,GAAG;IACxB,MAAM,MAAM,CAAC;IACb,MAAM,OAAO,OAAO,IAAI,CAAC;IACzB,IAAI;IACJ,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,KAAK,IAAK;QACxB,MAAM,IAAI,CAAC,EAAE;QACb,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;IACrB;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,QAAQ;IAC9B,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;QAC3C,IAAI,OAAO,IAAI,CAAC,QAAQ;QAExB,IAAI,SAAS,aAAa,OAAO;QAEjC,MAAM,eAAe,OAAO,QAAQ,CAAC,CAAC;QACtC,MAAM,SAAS,SAAS,KAAK,MAAM;QACnC,OAAO,CAAC,QAAQ,gJAAK,CAAC,OAAO,CAAC,UAAU,OAAO,MAAM,GAAG;QAExD,IAAI,QAAQ;YACV,IAAI,gJAAK,CAAC,UAAU,CAAC,QAAQ,OAAO;gBAClC,MAAM,CAAC,KAAK,GAAG;oBAAC,MAAM,CAAC,KAAK;oBAAE;iBAAM;YACtC,OAAO;gBACL,MAAM,CAAC,KAAK,GAAG;YACjB;YAEA,OAAO,CAAC;QACV;QAEA,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,gJAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG;YAClD,MAAM,CAAC,KAAK,GAAG,EAAE;QACnB;QAEA,MAAM,SAAS,UAAU,MAAM,OAAO,MAAM,CAAC,KAAK,EAAE;QAEpD,IAAI,UAAU,gJAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG;YACzC,MAAM,CAAC,KAAK,GAAG,cAAc,MAAM,CAAC,KAAK;QAC3C;QAEA,OAAO,CAAC;IACV;IAEA,IAAI,gJAAK,CAAC,UAAU,CAAC,aAAa,gJAAK,CAAC,UAAU,CAAC,SAAS,OAAO,GAAG;QACpE,MAAM,MAAM,CAAC;QAEb,gJAAK,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YAClC,UAAU,cAAc,OAAO,OAAO,KAAK;QAC7C;QAEA,OAAO;IACT;IAEA,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/defaults/index.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport transitionalDefaults from './transitional.js';\nimport toFormData from '../helpers/toFormData.js';\nimport toURLEncodedForm from '../helpers/toURLEncodedForm.js';\nimport platform from '../platform/index.js';\nimport formDataToJSON from '../helpers/formDataToJSON.js';\n\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nconst defaults = {\n\n  transitional: transitionalDefaults,\n\n  adapter: ['xhr', 'http', 'fetch'],\n\n  transformRequest: [function transformRequest(data, headers) {\n    const contentType = headers.getContentType() || '';\n    const hasJSONContentType = contentType.indexOf('application/json') > -1;\n    const isObjectPayload = utils.isObject(data);\n\n    if (isObjectPayload && utils.isHTMLForm(data)) {\n      data = new FormData(data);\n    }\n\n    const isFormData = utils.isFormData(data);\n\n    if (isFormData) {\n      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n    }\n\n    if (utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data) ||\n      utils.isReadableStream(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      headers.setContentType('application/x-www-form-urlencoded;charset=utf-8', false);\n      return data.toString();\n    }\n\n    let isFileList;\n\n    if (isObjectPayload) {\n      if (contentType.indexOf('application/x-www-form-urlencoded') > -1) {\n        return toURLEncodedForm(data, this.formSerializer).toString();\n      }\n\n      if ((isFileList = utils.isFileList(data)) || contentType.indexOf('multipart/form-data') > -1) {\n        const _FormData = this.env && this.env.FormData;\n\n        return toFormData(\n          isFileList ? {'files[]': data} : data,\n          _FormData && new _FormData(),\n          this.formSerializer\n        );\n      }\n    }\n\n    if (isObjectPayload || hasJSONContentType ) {\n      headers.setContentType('application/json', false);\n      return stringifySafely(data);\n    }\n\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    const transitional = this.transitional || defaults.transitional;\n    const forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    const JSONRequested = this.responseType === 'json';\n\n    if (utils.isResponse(data) || utils.isReadableStream(data)) {\n      return data;\n    }\n\n    if (data && utils.isString(data) && ((forcedJSONParsing && !this.responseType) || JSONRequested)) {\n      const silentJSONParsing = transitional && transitional.silentJSONParsing;\n      const strictJSONParsing = !silentJSONParsing && JSONRequested;\n\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  env: {\n    FormData: platform.classes.FormData,\n    Blob: platform.classes.Blob\n  },\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*',\n      'Content-Type': undefined\n    }\n  }\n};\n\nutils.forEach(['delete', 'get', 'head', 'post', 'put', 'patch'], (method) => {\n  defaults.headers[method] = {};\n});\n\nexport default defaults;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA;;;;;;;;;CASC,GACD,SAAS,gBAAgB,QAAQ,EAAE,MAAM,EAAE,OAAO;IAChD,IAAI,gJAAK,CAAC,QAAQ,CAAC,WAAW;QAC5B,IAAI;YACF,CAAC,UAAU,KAAK,KAAK,EAAE;YACvB,OAAO,gJAAK,CAAC,IAAI,CAAC;QACpB,EAAE,OAAO,GAAG;YACV,IAAI,EAAE,IAAI,KAAK,eAAe;gBAC5B,MAAM;YACR;QACF;IACF;IAEA,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;AACrC;AAEA,MAAM,WAAW;IAEf,cAAc,mKAAoB;IAElC,SAAS;QAAC;QAAO;QAAQ;KAAQ;IAEjC,kBAAkB;QAAC,SAAS,iBAAiB,IAAI,EAAE,OAAO;YACxD,MAAM,cAAc,QAAQ,cAAc,MAAM;YAChD,MAAM,qBAAqB,YAAY,OAAO,CAAC,sBAAsB,CAAC;YACtE,MAAM,kBAAkB,gJAAK,CAAC,QAAQ,CAAC;YAEvC,IAAI,mBAAmB,gJAAK,CAAC,UAAU,CAAC,OAAO;gBAC7C,OAAO,IAAI,SAAS;YACtB;YAEA,MAAM,aAAa,gJAAK,CAAC,UAAU,CAAC;YAEpC,IAAI,YAAY;gBACd,OAAO,qBAAqB,KAAK,SAAS,CAAC,IAAA,oKAAc,EAAC,SAAS;YACrE;YAEA,IAAI,gJAAK,CAAC,aAAa,CAAC,SACtB,gJAAK,CAAC,QAAQ,CAAC,SACf,gJAAK,CAAC,QAAQ,CAAC,SACf,gJAAK,CAAC,MAAM,CAAC,SACb,gJAAK,CAAC,MAAM,CAAC,SACb,gJAAK,CAAC,gBAAgB,CAAC,OACvB;gBACA,OAAO;YACT;YACA,IAAI,gJAAK,CAAC,iBAAiB,CAAC,OAAO;gBACjC,OAAO,KAAK,MAAM;YACpB;YACA,IAAI,gJAAK,CAAC,iBAAiB,CAAC,OAAO;gBACjC,QAAQ,cAAc,CAAC,mDAAmD;gBAC1E,OAAO,KAAK,QAAQ;YACtB;YAEA,IAAI;YAEJ,IAAI,iBAAiB;gBACnB,IAAI,YAAY,OAAO,CAAC,uCAAuC,CAAC,GAAG;oBACjE,OAAO,IAAA,sKAAgB,EAAC,MAAM,IAAI,CAAC,cAAc,EAAE,QAAQ;gBAC7D;gBAEA,IAAI,CAAC,aAAa,gJAAK,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,OAAO,CAAC,yBAAyB,CAAC,GAAG;oBAC5F,MAAM,YAAY,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ;oBAE/C,OAAO,IAAA,gKAAU,EACf,aAAa;wBAAC,WAAW;oBAAI,IAAI,MACjC,aAAa,IAAI,aACjB,IAAI,CAAC,cAAc;gBAEvB;YACF;YAEA,IAAI,mBAAmB,oBAAqB;gBAC1C,QAAQ,cAAc,CAAC,oBAAoB;gBAC3C,OAAO,gBAAgB;YACzB;YAEA,OAAO;QACT;KAAE;IAEF,mBAAmB;QAAC,SAAS,kBAAkB,IAAI;YACjD,MAAM,eAAe,IAAI,CAAC,YAAY,IAAI,SAAS,YAAY;YAC/D,MAAM,oBAAoB,gBAAgB,aAAa,iBAAiB;YACxE,MAAM,gBAAgB,IAAI,CAAC,YAAY,KAAK;YAE5C,IAAI,gJAAK,CAAC,UAAU,CAAC,SAAS,gJAAK,CAAC,gBAAgB,CAAC,OAAO;gBAC1D,OAAO;YACT;YAEA,IAAI,QAAQ,gJAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,AAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,IAAK,aAAa,GAAG;gBAChG,MAAM,oBAAoB,gBAAgB,aAAa,iBAAiB;gBACxE,MAAM,oBAAoB,CAAC,qBAAqB;gBAEhD,IAAI;oBACF,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAO,GAAG;oBACV,IAAI,mBAAmB;wBACrB,IAAI,EAAE,IAAI,KAAK,eAAe;4BAC5B,MAAM,6JAAU,CAAC,IAAI,CAAC,GAAG,6JAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ;wBACjF;wBACA,MAAM;oBACR;gBACF;YACF;YAEA,OAAO;QACT;KAAE;IAEF;;;GAGC,GACD,SAAS;IAET,gBAAgB;IAChB,gBAAgB;IAEhB,kBAAkB,CAAC;IACnB,eAAe,CAAC;IAEhB,KAAK;QACH,UAAU,4JAAQ,CAAC,OAAO,CAAC,QAAQ;QACnC,MAAM,4JAAQ,CAAC,OAAO,CAAC,IAAI;IAC7B;IAEA,gBAAgB,SAAS,eAAe,MAAM;QAC5C,OAAO,UAAU,OAAO,SAAS;IACnC;IAEA,SAAS;QACP,QAAQ;YACN,UAAU;YACV,gBAAgB;QAClB;IACF;AACF;AAEA,gJAAK,CAAC,OAAO,CAAC;IAAC;IAAU;IAAO;IAAQ;IAAQ;IAAO;CAAQ,EAAE,CAAC;IAChE,SAAS,OAAO,CAAC,OAAO,GAAG,CAAC;AAC9B;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/parseHeaders.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\n// RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nconst ignoreDuplicateOf = utils.toObjectSet([\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n]);\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */\nexport default rawHeaders => {\n  const parsed = {};\n  let key;\n  let val;\n  let i;\n\n  rawHeaders && rawHeaders.split('\\n').forEach(function parser(line) {\n    i = line.indexOf(':');\n    key = line.substring(0, i).trim().toLowerCase();\n    val = line.substring(i + 1).trim();\n\n    if (!key || (parsed[key] && ignoreDuplicateOf[key])) {\n      return;\n    }\n\n    if (key === 'set-cookie') {\n      if (parsed[key]) {\n        parsed[key].push(val);\n      } else {\n        parsed[key] = [val];\n      }\n    } else {\n      parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n    }\n  });\n\n  return parsed;\n};\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA,uDAAuD;AACvD,6DAA6D;AAC7D,MAAM,oBAAoB,gJAAK,CAAC,WAAW,CAAC;IAC1C;IAAO;IAAiB;IAAkB;IAAgB;IAC1D;IAAW;IAAQ;IAAQ;IAAqB;IAChD;IAAiB;IAAY;IAAgB;IAC7C;IAAW;IAAe;CAC3B;uCAgBc,CAAA;IACb,MAAM,SAAS,CAAC;IAChB,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,cAAc,WAAW,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,OAAO,IAAI;QAC/D,IAAI,KAAK,OAAO,CAAC;QACjB,MAAM,KAAK,SAAS,CAAC,GAAG,GAAG,IAAI,GAAG,WAAW;QAC7C,MAAM,KAAK,SAAS,CAAC,IAAI,GAAG,IAAI;QAEhC,IAAI,CAAC,OAAQ,MAAM,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,EAAG;YACnD;QACF;QAEA,IAAI,QAAQ,cAAc;YACxB,IAAI,MAAM,CAAC,IAAI,EAAE;gBACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YACnB,OAAO;gBACL,MAAM,CAAC,IAAI,GAAG;oBAAC;iBAAI;YACrB;QACF,OAAO;YACL,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,OAAO,MAAM;QACzD;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/AxiosHeaders.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport parseHeaders from '../helpers/parseHeaders.js';\n\nconst $internals = Symbol('internals');\n\nfunction normalizeHeader(header) {\n  return header && String(header).trim().toLowerCase();\n}\n\nfunction normalizeValue(value) {\n  if (value === false || value == null) {\n    return value;\n  }\n\n  return utils.isArray(value) ? value.map(normalizeValue) : String(value);\n}\n\nfunction parseTokens(str) {\n  const tokens = Object.create(null);\n  const tokensRE = /([^\\s,;=]+)\\s*(?:=\\s*([^,;]+))?/g;\n  let match;\n\n  while ((match = tokensRE.exec(str))) {\n    tokens[match[1]] = match[2];\n  }\n\n  return tokens;\n}\n\nconst isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());\n\nfunction matchHeaderValue(context, value, header, filter, isHeaderNameFilter) {\n  if (utils.isFunction(filter)) {\n    return filter.call(this, value, header);\n  }\n\n  if (isHeaderNameFilter) {\n    value = header;\n  }\n\n  if (!utils.isString(value)) return;\n\n  if (utils.isString(filter)) {\n    return value.indexOf(filter) !== -1;\n  }\n\n  if (utils.isRegExp(filter)) {\n    return filter.test(value);\n  }\n}\n\nfunction formatHeader(header) {\n  return header.trim()\n    .toLowerCase().replace(/([a-z\\d])(\\w*)/g, (w, char, str) => {\n      return char.toUpperCase() + str;\n    });\n}\n\nfunction buildAccessors(obj, header) {\n  const accessorName = utils.toCamelCase(' ' + header);\n\n  ['get', 'set', 'has'].forEach(methodName => {\n    Object.defineProperty(obj, methodName + accessorName, {\n      value: function(arg1, arg2, arg3) {\n        return this[methodName].call(this, header, arg1, arg2, arg3);\n      },\n      configurable: true\n    });\n  });\n}\n\nclass AxiosHeaders {\n  constructor(headers) {\n    headers && this.set(headers);\n  }\n\n  set(header, valueOrRewrite, rewrite) {\n    const self = this;\n\n    function setHeader(_value, _header, _rewrite) {\n      const lHeader = normalizeHeader(_header);\n\n      if (!lHeader) {\n        throw new Error('header name must be a non-empty string');\n      }\n\n      const key = utils.findKey(self, lHeader);\n\n      if(!key || self[key] === undefined || _rewrite === true || (_rewrite === undefined && self[key] !== false)) {\n        self[key || _header] = normalizeValue(_value);\n      }\n    }\n\n    const setHeaders = (headers, _rewrite) =>\n      utils.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));\n\n    if (utils.isPlainObject(header) || header instanceof this.constructor) {\n      setHeaders(header, valueOrRewrite)\n    } else if(utils.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {\n      setHeaders(parseHeaders(header), valueOrRewrite);\n    } else if (utils.isObject(header) && utils.isIterable(header)) {\n      let obj = {}, dest, key;\n      for (const entry of header) {\n        if (!utils.isArray(entry)) {\n          throw TypeError('Object iterator must return a key-value pair');\n        }\n\n        obj[key = entry[0]] = (dest = obj[key]) ?\n          (utils.isArray(dest) ? [...dest, entry[1]] : [dest, entry[1]]) : entry[1];\n      }\n\n      setHeaders(obj, valueOrRewrite)\n    } else {\n      header != null && setHeader(valueOrRewrite, header, rewrite);\n    }\n\n    return this;\n  }\n\n  get(header, parser) {\n    header = normalizeHeader(header);\n\n    if (header) {\n      const key = utils.findKey(this, header);\n\n      if (key) {\n        const value = this[key];\n\n        if (!parser) {\n          return value;\n        }\n\n        if (parser === true) {\n          return parseTokens(value);\n        }\n\n        if (utils.isFunction(parser)) {\n          return parser.call(this, value, key);\n        }\n\n        if (utils.isRegExp(parser)) {\n          return parser.exec(value);\n        }\n\n        throw new TypeError('parser must be boolean|regexp|function');\n      }\n    }\n  }\n\n  has(header, matcher) {\n    header = normalizeHeader(header);\n\n    if (header) {\n      const key = utils.findKey(this, header);\n\n      return !!(key && this[key] !== undefined && (!matcher || matchHeaderValue(this, this[key], key, matcher)));\n    }\n\n    return false;\n  }\n\n  delete(header, matcher) {\n    const self = this;\n    let deleted = false;\n\n    function deleteHeader(_header) {\n      _header = normalizeHeader(_header);\n\n      if (_header) {\n        const key = utils.findKey(self, _header);\n\n        if (key && (!matcher || matchHeaderValue(self, self[key], key, matcher))) {\n          delete self[key];\n\n          deleted = true;\n        }\n      }\n    }\n\n    if (utils.isArray(header)) {\n      header.forEach(deleteHeader);\n    } else {\n      deleteHeader(header);\n    }\n\n    return deleted;\n  }\n\n  clear(matcher) {\n    const keys = Object.keys(this);\n    let i = keys.length;\n    let deleted = false;\n\n    while (i--) {\n      const key = keys[i];\n      if(!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {\n        delete this[key];\n        deleted = true;\n      }\n    }\n\n    return deleted;\n  }\n\n  normalize(format) {\n    const self = this;\n    const headers = {};\n\n    utils.forEach(this, (value, header) => {\n      const key = utils.findKey(headers, header);\n\n      if (key) {\n        self[key] = normalizeValue(value);\n        delete self[header];\n        return;\n      }\n\n      const normalized = format ? formatHeader(header) : String(header).trim();\n\n      if (normalized !== header) {\n        delete self[header];\n      }\n\n      self[normalized] = normalizeValue(value);\n\n      headers[normalized] = true;\n    });\n\n    return this;\n  }\n\n  concat(...targets) {\n    return this.constructor.concat(this, ...targets);\n  }\n\n  toJSON(asStrings) {\n    const obj = Object.create(null);\n\n    utils.forEach(this, (value, header) => {\n      value != null && value !== false && (obj[header] = asStrings && utils.isArray(value) ? value.join(', ') : value);\n    });\n\n    return obj;\n  }\n\n  [Symbol.iterator]() {\n    return Object.entries(this.toJSON())[Symbol.iterator]();\n  }\n\n  toString() {\n    return Object.entries(this.toJSON()).map(([header, value]) => header + ': ' + value).join('\\n');\n  }\n\n  getSetCookie() {\n    return this.get(\"set-cookie\") || [];\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'AxiosHeaders';\n  }\n\n  static from(thing) {\n    return thing instanceof this ? thing : new this(thing);\n  }\n\n  static concat(first, ...targets) {\n    const computed = new this(first);\n\n    targets.forEach((target) => computed.set(target));\n\n    return computed;\n  }\n\n  static accessor(header) {\n    const internals = this[$internals] = (this[$internals] = {\n      accessors: {}\n    });\n\n    const accessors = internals.accessors;\n    const prototype = this.prototype;\n\n    function defineAccessor(_header) {\n      const lHeader = normalizeHeader(_header);\n\n      if (!accessors[lHeader]) {\n        buildAccessors(prototype, _header);\n        accessors[lHeader] = true;\n      }\n    }\n\n    utils.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);\n\n    return this;\n  }\n}\n\nAxiosHeaders.accessor(['Content-Type', 'Content-Length', 'Accept', 'Accept-Encoding', 'User-Agent', 'Authorization']);\n\n// reserved names hotfix\nutils.reduceDescriptors(AxiosHeaders.prototype, ({value}, key) => {\n  let mapped = key[0].toUpperCase() + key.slice(1); // map `set` => `Set`\n  return {\n    get: () => value,\n    set(headerValue) {\n      this[mapped] = headerValue;\n    }\n  }\n});\n\nutils.freezeMethods(AxiosHeaders);\n\nexport default AxiosHeaders;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAKA,MAAM,aAAa,OAAO;AAE1B,SAAS,gBAAgB,MAAM;IAC7B,OAAO,UAAU,OAAO,QAAQ,IAAI,GAAG,WAAW;AACpD;AAEA,SAAS,eAAe,KAAK;IAC3B,IAAI,UAAU,SAAS,SAAS,MAAM;QACpC,OAAO;IACT;IAEA,OAAO,gJAAK,CAAC,OAAO,CAAC,SAAS,MAAM,GAAG,CAAC,kBAAkB,OAAO;AACnE;AAEA,SAAS,YAAY,GAAG;IACtB,MAAM,SAAS,OAAO,MAAM,CAAC;IAC7B,MAAM,WAAW;IACjB,IAAI;IAEJ,MAAQ,QAAQ,SAAS,IAAI,CAAC,KAAO;QACnC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE;IAC7B;IAEA,OAAO;AACT;AAEA,MAAM,oBAAoB,CAAC,MAAQ,iCAAiC,IAAI,CAAC,IAAI,IAAI;AAEjF,SAAS,iBAAiB,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB;IAC1E,IAAI,gJAAK,CAAC,UAAU,CAAC,SAAS;QAC5B,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO;IAClC;IAEA,IAAI,oBAAoB;QACtB,QAAQ;IACV;IAEA,IAAI,CAAC,gJAAK,CAAC,QAAQ,CAAC,QAAQ;IAE5B,IAAI,gJAAK,CAAC,QAAQ,CAAC,SAAS;QAC1B,OAAO,MAAM,OAAO,CAAC,YAAY,CAAC;IACpC;IAEA,IAAI,gJAAK,CAAC,QAAQ,CAAC,SAAS;QAC1B,OAAO,OAAO,IAAI,CAAC;IACrB;AACF;AAEA,SAAS,aAAa,MAAM;IAC1B,OAAO,OAAO,IAAI,GACf,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,GAAG,MAAM;QAClD,OAAO,KAAK,WAAW,KAAK;IAC9B;AACJ;AAEA,SAAS,eAAe,GAAG,EAAE,MAAM;IACjC,MAAM,eAAe,gJAAK,CAAC,WAAW,CAAC,MAAM;IAE7C;QAAC;QAAO;QAAO;KAAM,CAAC,OAAO,CAAC,CAAA;QAC5B,OAAO,cAAc,CAAC,KAAK,aAAa,cAAc;YACpD,OAAO,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI;gBAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,MAAM,MAAM;YACzD;YACA,cAAc;QAChB;IACF;AACF;AAEA,MAAM;IACJ,YAAY,OAAO,CAAE;QACnB,WAAW,IAAI,CAAC,GAAG,CAAC;IACtB;IAEA,IAAI,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE;QACnC,MAAM,OAAO,IAAI;QAEjB,SAAS,UAAU,MAAM,EAAE,OAAO,EAAE,QAAQ;YAC1C,MAAM,UAAU,gBAAgB;YAEhC,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,MAAM,gJAAK,CAAC,OAAO,CAAC,MAAM;YAEhC,IAAG,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa,aAAa,QAAS,aAAa,aAAa,IAAI,CAAC,IAAI,KAAK,OAAQ;gBAC1G,IAAI,CAAC,OAAO,QAAQ,GAAG,eAAe;YACxC;QACF;QAEA,MAAM,aAAa,CAAC,SAAS,WAC3B,gJAAK,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,UAAY,UAAU,QAAQ,SAAS;QAEzE,IAAI,gJAAK,CAAC,aAAa,CAAC,WAAW,kBAAkB,IAAI,CAAC,WAAW,EAAE;YACrE,WAAW,QAAQ;QACrB,OAAO,IAAG,gJAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,OAAO,IAAI,EAAE,KAAK,CAAC,kBAAkB,SAAS;YAC1F,WAAW,IAAA,kKAAY,EAAC,SAAS;QACnC,OAAO,IAAI,gJAAK,CAAC,QAAQ,CAAC,WAAW,gJAAK,CAAC,UAAU,CAAC,SAAS;YAC7D,IAAI,MAAM,CAAC,GAAG,MAAM;YACpB,KAAK,MAAM,SAAS,OAAQ;gBAC1B,IAAI,CAAC,gJAAK,CAAC,OAAO,CAAC,QAAQ;oBACzB,MAAM,UAAU;gBAClB;gBAEA,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,IAAI,IACnC,gJAAK,CAAC,OAAO,CAAC,QAAQ;uBAAI;oBAAM,KAAK,CAAC,EAAE;iBAAC,GAAG;oBAAC;oBAAM,KAAK,CAAC,EAAE;iBAAC,GAAI,KAAK,CAAC,EAAE;YAC7E;YAEA,WAAW,KAAK;QAClB,OAAO;YACL,UAAU,QAAQ,UAAU,gBAAgB,QAAQ;QACtD;QAEA,OAAO,IAAI;IACb;IAEA,IAAI,MAAM,EAAE,MAAM,EAAE;QAClB,SAAS,gBAAgB;QAEzB,IAAI,QAAQ;YACV,MAAM,MAAM,gJAAK,CAAC,OAAO,CAAC,IAAI,EAAE;YAEhC,IAAI,KAAK;gBACP,MAAM,QAAQ,IAAI,CAAC,IAAI;gBAEvB,IAAI,CAAC,QAAQ;oBACX,OAAO;gBACT;gBAEA,IAAI,WAAW,MAAM;oBACnB,OAAO,YAAY;gBACrB;gBAEA,IAAI,gJAAK,CAAC,UAAU,CAAC,SAAS;oBAC5B,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO;gBAClC;gBAEA,IAAI,gJAAK,CAAC,QAAQ,CAAC,SAAS;oBAC1B,OAAO,OAAO,IAAI,CAAC;gBACrB;gBAEA,MAAM,IAAI,UAAU;YACtB;QACF;IACF;IAEA,IAAI,MAAM,EAAE,OAAO,EAAE;QACnB,SAAS,gBAAgB;QAEzB,IAAI,QAAQ;YACV,MAAM,MAAM,gJAAK,CAAC,OAAO,CAAC,IAAI,EAAE;YAEhC,OAAO,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,WAAW,iBAAiB,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,CAAC;QAC3G;QAEA,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,OAAO,EAAE;QACtB,MAAM,OAAO,IAAI;QACjB,IAAI,UAAU;QAEd,SAAS,aAAa,OAAO;YAC3B,UAAU,gBAAgB;YAE1B,IAAI,SAAS;gBACX,MAAM,MAAM,gJAAK,CAAC,OAAO,CAAC,MAAM;gBAEhC,IAAI,OAAO,CAAC,CAAC,WAAW,iBAAiB,MAAM,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,GAAG;oBACxE,OAAO,IAAI,CAAC,IAAI;oBAEhB,UAAU;gBACZ;YACF;QACF;QAEA,IAAI,gJAAK,CAAC,OAAO,CAAC,SAAS;YACzB,OAAO,OAAO,CAAC;QACjB,OAAO;YACL,aAAa;QACf;QAEA,OAAO;IACT;IAEA,MAAM,OAAO,EAAE;QACb,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI;QAC7B,IAAI,IAAI,KAAK,MAAM;QACnB,IAAI,UAAU;QAEd,MAAO,IAAK;YACV,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAG,CAAC,WAAW,iBAAiB,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,SAAS,OAAO;gBACpE,OAAO,IAAI,CAAC,IAAI;gBAChB,UAAU;YACZ;QACF;QAEA,OAAO;IACT;IAEA,UAAU,MAAM,EAAE;QAChB,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,CAAC;QAEjB,gJAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO;YAC1B,MAAM,MAAM,gJAAK,CAAC,OAAO,CAAC,SAAS;YAEnC,IAAI,KAAK;gBACP,IAAI,CAAC,IAAI,GAAG,eAAe;gBAC3B,OAAO,IAAI,CAAC,OAAO;gBACnB;YACF;YAEA,MAAM,aAAa,SAAS,aAAa,UAAU,OAAO,QAAQ,IAAI;YAEtE,IAAI,eAAe,QAAQ;gBACzB,OAAO,IAAI,CAAC,OAAO;YACrB;YAEA,IAAI,CAAC,WAAW,GAAG,eAAe;YAElC,OAAO,CAAC,WAAW,GAAG;QACxB;QAEA,OAAO,IAAI;IACb;IAEA,OAAO,GAAG,OAAO,EAAE;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,KAAK;IAC1C;IAEA,OAAO,SAAS,EAAE;QAChB,MAAM,MAAM,OAAO,MAAM,CAAC;QAE1B,gJAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO;YAC1B,SAAS,QAAQ,UAAU,SAAS,CAAC,GAAG,CAAC,OAAO,GAAG,aAAa,gJAAK,CAAC,OAAO,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,KAAK;QACjH;QAEA,OAAO;IACT;IAEA,CAAC,OAAO,QAAQ,CAAC,GAAG;QAClB,OAAO,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,QAAQ,CAAC;IACvD;IAEA,WAAW;QACT,OAAO,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,GAAK,SAAS,OAAO,OAAO,IAAI,CAAC;IAC5F;IAEA,eAAe;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE;IACrC;IAEA,IAAI,CAAC,OAAO,WAAW,CAAC,GAAG;QACzB,OAAO;IACT;IAEA,OAAO,KAAK,KAAK,EAAE;QACjB,OAAO,iBAAiB,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC;IAClD;IAEA,OAAO,OAAO,KAAK,EAAE,GAAG,OAAO,EAAE;QAC/B,MAAM,WAAW,IAAI,IAAI,CAAC;QAE1B,QAAQ,OAAO,CAAC,CAAC,SAAW,SAAS,GAAG,CAAC;QAEzC,OAAO;IACT;IAEA,OAAO,SAAS,MAAM,EAAE;QACtB,MAAM,YAAY,IAAI,CAAC,WAAW,GAAI,IAAI,CAAC,WAAW,GAAG;YACvD,WAAW,CAAC;QACd;QAEA,MAAM,YAAY,UAAU,SAAS;QACrC,MAAM,YAAY,IAAI,CAAC,SAAS;QAEhC,SAAS,eAAe,OAAO;YAC7B,MAAM,UAAU,gBAAgB;YAEhC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACvB,eAAe,WAAW;gBAC1B,SAAS,CAAC,QAAQ,GAAG;YACvB;QACF;QAEA,gJAAK,CAAC,OAAO,CAAC,UAAU,OAAO,OAAO,CAAC,kBAAkB,eAAe;QAExE,OAAO,IAAI;IACb;AACF;AAEA,aAAa,QAAQ,CAAC;IAAC;IAAgB;IAAkB;IAAU;IAAmB;IAAc;CAAgB;AAEpH,wBAAwB;AACxB,gJAAK,CAAC,iBAAiB,CAAC,aAAa,SAAS,EAAE,CAAC,EAAC,KAAK,EAAC,EAAE;IACxD,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,KAAK,CAAC,IAAI,qBAAqB;IACvE,OAAO;QACL,KAAK,IAAM;QACX,KAAI,WAAW;YACb,IAAI,CAAC,OAAO,GAAG;QACjB;IACF;AACF;AAEA,gJAAK,CAAC,aAAa,CAAC;uCAEL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/transformData.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport defaults from '../defaults/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */\nexport default function transformData(fns, response) {\n  const config = this || defaults;\n  const context = response || config;\n  const headers = AxiosHeaders.from(context.headers);\n  let data = context.data;\n\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n  });\n\n  headers.normalize();\n\n  return data;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;AAce,SAAS,cAAc,GAAG,EAAE,QAAQ;IACjD,MAAM,SAAS,IAAI,IAAI,4JAAQ;IAC/B,MAAM,UAAU,YAAY;IAC5B,MAAM,UAAU,+JAAY,CAAC,IAAI,CAAC,QAAQ,OAAO;IACjD,IAAI,OAAO,QAAQ,IAAI;IAEvB,gJAAK,CAAC,OAAO,CAAC,KAAK,SAAS,UAAU,EAAE;QACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,MAAM,QAAQ,SAAS,IAAI,WAAW,SAAS,MAAM,GAAG;IACjF;IAEA,QAAQ,SAAS;IAEjB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/cancel/isCancel.js"], "sourcesContent": ["'use strict';\n\nexport default function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEe,SAAS,SAAS,KAAK;IACpC,OAAO,CAAC,CAAC,CAAC,SAAS,MAAM,UAAU;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/cancel/CanceledError.js"], "sourcesContent": ["'use strict';\n\nimport AxiosError from '../core/AxiosError.js';\nimport utils from '../utils.js';\n\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */\nfunction CanceledError(message, config, request) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED, config, request);\n  this.name = 'CanceledError';\n}\n\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\n\nexport default CanceledError;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAKA;;;;;;;;CAQC,GACD,SAAS,cAAc,OAAO,EAAE,MAAM,EAAE,OAAO;IAC7C,6CAA6C;IAC7C,6JAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,OAAO,aAAa,SAAS,6JAAU,CAAC,YAAY,EAAE,QAAQ;IAC/F,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,gJAAK,CAAC,QAAQ,CAAC,eAAe,6JAAU,EAAE;IACxC,YAAY;AACd;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/settle.js"], "sourcesContent": ["'use strict';\n\nimport AxiosError from './AxiosError.js';\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */\nexport default function settle(resolve, reject, response) {\n  const validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError(\n      'Request failed with status code ' + response.status,\n      [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],\n      response.config,\n      response.request,\n      response\n    ));\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAae,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,QAAQ;IACtD,MAAM,iBAAiB,SAAS,MAAM,CAAC,cAAc;IACrD,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,kBAAkB,eAAe,SAAS,MAAM,GAAG;QAC1E,QAAQ;IACV,OAAO;QACL,OAAO,IAAI,6JAAU,CACnB,qCAAqC,SAAS,MAAM,EACpD;YAAC,6JAAU,CAAC,eAAe;YAAE,6JAAU,CAAC,gBAAgB;SAAC,CAAC,KAAK,KAAK,CAAC,SAAS,MAAM,GAAG,OAAO,EAAE,EAChG,SAAS,MAAM,EACf,SAAS,OAAO,EAChB;IAEJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/isAbsoluteURL.js"], "sourcesContent": ["'use strict';\n\n/**\n * Determines whether the specified URL is absolute\n *\n * @param {string} url The URL to test\n *\n * @returns {boolean} True if the specified URL is absolute, otherwise false\n */\nexport default function isAbsoluteURL(url) {\n  // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\n  // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\n  // by any combination of letters, digits, plus, period, or hyphen.\n  return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url);\n}\n"], "names": [], "mappings": ";;;;AAAA;AASe,SAAS,cAAc,GAAG;IACvC,gGAAgG;IAChG,gGAAgG;IAChG,kEAAkE;IAClE,OAAO,8BAA8B,IAAI,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/combineURLs.js"], "sourcesContent": ["'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */\nexport default function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/?\\/$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAUe,SAAS,YAAY,OAAO,EAAE,WAAW;IACtD,OAAO,cACH,QAAQ,OAAO,CAAC,UAAU,MAAM,MAAM,YAAY,OAAO,CAAC,QAAQ,MAClE;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/buildFullPath.js"], "sourcesContent": ["'use strict';\n\nimport isAbsoluteURL from '../helpers/isAbsoluteURL.js';\nimport combineURLs from '../helpers/combineURLs.js';\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */\nexport default function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {\n  let isRelativeUrl = !isAbsoluteURL(requestedURL);\n  if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAee,SAAS,cAAc,OAAO,EAAE,YAAY,EAAE,iBAAiB;IAC5E,IAAI,gBAAgB,CAAC,IAAA,mKAAa,EAAC;IACnC,IAAI,WAAW,CAAC,iBAAiB,qBAAqB,KAAK,GAAG;QAC5D,OAAO,IAAA,iKAAW,EAAC,SAAS;IAC9B;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/proxy-from-env/index.js"], "sourcesContent": ["'use strict';\n\nvar parseUrl = require('url').parse;\n\nvar DEFAULT_PORTS = {\n  ftp: 21,\n  gopher: 70,\n  http: 80,\n  https: 443,\n  ws: 80,\n  wss: 443,\n};\n\nvar stringEndsWith = String.prototype.endsWith || function(s) {\n  return s.length <= this.length &&\n    this.indexOf(s, this.length - s.length) !== -1;\n};\n\n/**\n * @param {string|object} url - The URL, or the result from url.parse.\n * @return {string} The URL of the proxy that should handle the request to the\n *  given URL. If no proxy is set, this will be an empty string.\n */\nfunction getProxyForUrl(url) {\n  var parsedUrl = typeof url === 'string' ? parseUrl(url) : url || {};\n  var proto = parsedUrl.protocol;\n  var hostname = parsedUrl.host;\n  var port = parsedUrl.port;\n  if (typeof hostname !== 'string' || !hostname || typeof proto !== 'string') {\n    return '';  // Don't proxy URLs without a valid scheme or host.\n  }\n\n  proto = proto.split(':', 1)[0];\n  // Stripping ports in this way instead of using parsedUrl.hostname to make\n  // sure that the brackets around IPv6 addresses are kept.\n  hostname = hostname.replace(/:\\d*$/, '');\n  port = parseInt(port) || DEFAULT_PORTS[proto] || 0;\n  if (!shouldProxy(hostname, port)) {\n    return '';  // Don't proxy URLs that match NO_PROXY.\n  }\n\n  var proxy =\n    getEnv('npm_config_' + proto + '_proxy') ||\n    getEnv(proto + '_proxy') ||\n    getEnv('npm_config_proxy') ||\n    getEnv('all_proxy');\n  if (proxy && proxy.indexOf('://') === -1) {\n    // Missing scheme in proxy, default to the requested URL's scheme.\n    proxy = proto + '://' + proxy;\n  }\n  return proxy;\n}\n\n/**\n * Determines whether a given URL should be proxied.\n *\n * @param {string} hostname - The host name of the URL.\n * @param {number} port - The effective port of the URL.\n * @returns {boolean} Whether the given URL should be proxied.\n * @private\n */\nfunction shouldProxy(hostname, port) {\n  var NO_PROXY =\n    (getEnv('npm_config_no_proxy') || getEnv('no_proxy')).toLowerCase();\n  if (!NO_PROXY) {\n    return true;  // Always proxy if NO_PROXY is not set.\n  }\n  if (NO_PROXY === '*') {\n    return false;  // Never proxy if wildcard is set.\n  }\n\n  return NO_PROXY.split(/[,\\s]/).every(function(proxy) {\n    if (!proxy) {\n      return true;  // Skip zero-length hosts.\n    }\n    var parsedProxy = proxy.match(/^(.+):(\\d+)$/);\n    var parsedProxyHostname = parsedProxy ? parsedProxy[1] : proxy;\n    var parsedProxyPort = parsedProxy ? parseInt(parsedProxy[2]) : 0;\n    if (parsedProxyPort && parsedProxyPort !== port) {\n      return true;  // Skip if ports don't match.\n    }\n\n    if (!/^[.*]/.test(parsedProxyHostname)) {\n      // No wildcards, so stop proxying if there is an exact match.\n      return hostname !== parsedProxyHostname;\n    }\n\n    if (parsedProxyHostname.charAt(0) === '*') {\n      // Remove leading wildcard.\n      parsedProxyHostname = parsedProxyHostname.slice(1);\n    }\n    // Stop proxying if the hostname ends with the no_proxy host.\n    return !stringEndsWith.call(hostname, parsedProxyHostname);\n  });\n}\n\n/**\n * Get the value for an environment variable.\n *\n * @param {string} key - The name of the environment variable.\n * @return {string} The value of the environment variable.\n * @private\n */\nfunction getEnv(key) {\n  return process.env[key.toLowerCase()] || process.env[key.toUpperCase()] || '';\n}\n\nexports.getProxyForUrl = getProxyForUrl;\n"], "names": [], "mappings": "AAEA,IAAI,WAAW,iEAAe,KAAK;AAEnC,IAAI,gBAAgB;IAClB,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,IAAI;IACJ,KAAK;AACP;AAEA,IAAI,iBAAiB,OAAO,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC;IAC1D,OAAO,EAAE,MAAM,IAAI,IAAI,CAAC,MAAM,IAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,MAAM,MAAM,CAAC;AACjD;AAEA;;;;CAIC,GACD,SAAS,eAAe,GAAG;IACzB,IAAI,YAAY,OAAO,QAAQ,WAAW,SAAS,OAAO,OAAO,CAAC;IAClE,IAAI,QAAQ,UAAU,QAAQ;IAC9B,IAAI,WAAW,UAAU,IAAI;IAC7B,IAAI,OAAO,UAAU,IAAI;IACzB,IAAI,OAAO,aAAa,YAAY,CAAC,YAAY,OAAO,UAAU,UAAU;QAC1E,OAAO,IAAK,mDAAmD;IACjE;IAEA,QAAQ,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;IAC9B,0EAA0E;IAC1E,yDAAyD;IACzD,WAAW,SAAS,OAAO,CAAC,SAAS;IACrC,OAAO,SAAS,SAAS,aAAa,CAAC,MAAM,IAAI;IACjD,IAAI,CAAC,YAAY,UAAU,OAAO;QAChC,OAAO,IAAK,wCAAwC;IACtD;IAEA,IAAI,QACF,OAAO,gBAAgB,QAAQ,aAC/B,OAAO,QAAQ,aACf,OAAO,uBACP,OAAO;IACT,IAAI,SAAS,MAAM,OAAO,CAAC,WAAW,CAAC,GAAG;QACxC,kEAAkE;QAClE,QAAQ,QAAQ,QAAQ;IAC1B;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,YAAY,QAAQ,EAAE,IAAI;IACjC,IAAI,WACF,CAAC,OAAO,0BAA0B,OAAO,WAAW,EAAE,WAAW;IACnE,IAAI,CAAC,UAAU;QACb,OAAO,MAAO,uCAAuC;IACvD;IACA,IAAI,aAAa,KAAK;QACpB,OAAO,OAAQ,kCAAkC;IACnD;IAEA,OAAO,SAAS,KAAK,CAAC,SAAS,KAAK,CAAC,SAAS,KAAK;QACjD,IAAI,CAAC,OAAO;YACV,OAAO,MAAO,0BAA0B;QAC1C;QACA,IAAI,cAAc,MAAM,KAAK,CAAC;QAC9B,IAAI,sBAAsB,cAAc,WAAW,CAAC,EAAE,GAAG;QACzD,IAAI,kBAAkB,cAAc,SAAS,WAAW,CAAC,EAAE,IAAI;QAC/D,IAAI,mBAAmB,oBAAoB,MAAM;YAC/C,OAAO,MAAO,6BAA6B;QAC7C;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,sBAAsB;YACtC,6DAA6D;YAC7D,OAAO,aAAa;QACtB;QAEA,IAAI,oBAAoB,MAAM,CAAC,OAAO,KAAK;YACzC,2BAA2B;YAC3B,sBAAsB,oBAAoB,KAAK,CAAC;QAClD;QACA,6DAA6D;QAC7D,OAAO,CAAC,eAAe,IAAI,CAAC,UAAU;IACxC;AACF;AAEA;;;;;;CAMC,GACD,SAAS,OAAO,GAAG;IACjB,OAAO,QAAQ,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,QAAQ,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI;AAC7E;AAEA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/ms/index.js"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,IAAI,IAAI;AACR,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AAEZ;;;;;;;;;;;;CAYC,GAED,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,UAAU,WAAW,CAAC;IACtB,IAAI,OAAO,OAAO;IAClB,IAAI,SAAS,YAAY,IAAI,MAAM,GAAG,GAAG;QACvC,OAAO,MAAM;IACf,OAAO,IAAI,SAAS,YAAY,SAAS,MAAM;QAC7C,OAAO,QAAQ,IAAI,GAAG,QAAQ,OAAO,SAAS;IAChD;IACA,MAAM,IAAI,MACR,0DACE,KAAK,SAAS,CAAC;AAErB;AAEA;;;;;;CAMC,GAED,SAAS,MAAM,GAAG;IAChB,MAAM,OAAO;IACb,IAAI,IAAI,MAAM,GAAG,KAAK;QACpB;IACF;IACA,IAAI,QAAQ,mIAAmI,IAAI,CACjJ;IAEF,IAAI,CAAC,OAAO;QACV;IACF;IACA,IAAI,IAAI,WAAW,KAAK,CAAC,EAAE;IAC3B,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE,WAAW;IACzC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA;;;;;;CAMC,GAED,SAAS,SAAS,EAAE;IAClB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;;;;;CAMC,GAED,SAAS,QAAQ,EAAE;IACjB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;CAEC,GAED,SAAS,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI;IAChC,IAAI,WAAW,SAAS,IAAI;IAC5B,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,MAAM,OAAO,CAAC,WAAW,MAAM,EAAE;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/debug/src/common.js"], "sourcesContent": ["\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(/\\s+/g, ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n"], "names": [], "mappings": "AACA;;;CAGC,GAED,SAAS,MAAM,GAAG;IACjB,YAAY,KAAK,GAAG;IACpB,YAAY,OAAO,GAAG;IACtB,YAAY,MAAM,GAAG;IACrB,YAAY,OAAO,GAAG;IACtB,YAAY,MAAM,GAAG;IACrB,YAAY,OAAO,GAAG;IACtB,YAAY,QAAQ;IACpB,YAAY,OAAO,GAAG;IAEtB,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,CAAA;QACxB,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;IAC5B;IAEA;;CAEA,GAEA,YAAY,KAAK,GAAG,EAAE;IACtB,YAAY,KAAK,GAAG,EAAE;IAEtB;;;;CAIA,GACA,YAAY,UAAU,GAAG,CAAC;IAE1B;;;;;CAKA,GACA,SAAS,YAAY,SAAS;QAC7B,IAAI,OAAO;QAEX,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YAC1C,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ,UAAU,UAAU,CAAC;YACnD,QAAQ,GAAG,2BAA2B;QACvC;QAEA,OAAO,YAAY,MAAM,CAAC,KAAK,GAAG,CAAC,QAAQ,YAAY,MAAM,CAAC,MAAM,CAAC;IACtE;IACA,YAAY,WAAW,GAAG;IAE1B;;;;;;CAMA,GACA,SAAS,YAAY,SAAS;QAC7B,IAAI;QACJ,IAAI,iBAAiB;QACrB,IAAI;QACJ,IAAI;QAEJ,SAAS,MAAM,GAAG,IAAI;YACrB,YAAY;YACZ,IAAI,CAAC,MAAM,OAAO,EAAE;gBACnB;YACD;YAEA,MAAM,OAAO;YAEb,uBAAuB;YACvB,MAAM,OAAO,OAAO,IAAI;YACxB,MAAM,KAAK,OAAO,CAAC,YAAY,IAAI;YACnC,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,WAAW;YAEX,IAAI,CAAC,EAAE,GAAG,YAAY,MAAM,CAAC,IAAI,CAAC,EAAE;YAEpC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAChC,sCAAsC;gBACtC,KAAK,OAAO,CAAC;YACd;YAEA,yCAAyC;YACzC,IAAI,QAAQ;YACZ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO;gBAClD,mEAAmE;gBACnE,IAAI,UAAU,MAAM;oBACnB,OAAO;gBACR;gBACA;gBACA,MAAM,YAAY,YAAY,UAAU,CAAC,OAAO;gBAChD,IAAI,OAAO,cAAc,YAAY;oBACpC,MAAM,MAAM,IAAI,CAAC,MAAM;oBACvB,QAAQ,UAAU,IAAI,CAAC,MAAM;oBAE7B,yEAAyE;oBACzE,KAAK,MAAM,CAAC,OAAO;oBACnB;gBACD;gBACA,OAAO;YACR;YAEA,+CAA+C;YAC/C,YAAY,UAAU,CAAC,IAAI,CAAC,MAAM;YAElC,MAAM,QAAQ,KAAK,GAAG,IAAI,YAAY,GAAG;YACzC,MAAM,KAAK,CAAC,MAAM;QACnB;QAEA,MAAM,SAAS,GAAG;QAClB,MAAM,SAAS,GAAG,YAAY,SAAS;QACvC,MAAM,KAAK,GAAG,YAAY,WAAW,CAAC;QACtC,MAAM,MAAM,GAAG;QACf,MAAM,OAAO,GAAG,YAAY,OAAO,EAAE,4DAA4D;QAEjG,OAAO,cAAc,CAAC,OAAO,WAAW;YACvC,YAAY;YACZ,cAAc;YACd,KAAK;gBACJ,IAAI,mBAAmB,MAAM;oBAC5B,OAAO;gBACR;gBACA,IAAI,oBAAoB,YAAY,UAAU,EAAE;oBAC/C,kBAAkB,YAAY,UAAU;oBACxC,eAAe,YAAY,OAAO,CAAC;gBACpC;gBAEA,OAAO;YACR;YACA,KAAK,CAAA;gBACJ,iBAAiB;YAClB;QACD;QAEA,wDAAwD;QACxD,IAAI,OAAO,YAAY,IAAI,KAAK,YAAY;YAC3C,YAAY,IAAI,CAAC;QAClB;QAEA,OAAO;IACR;IAEA,SAAS,OAAO,SAAS,EAAE,SAAS;QACnC,MAAM,WAAW,YAAY,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,cAAc,cAAc,MAAM,SAAS,IAAI;QACrG,SAAS,GAAG,GAAG,IAAI,CAAC,GAAG;QACvB,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,OAAO,UAAU;QACzB,YAAY,IAAI,CAAC;QACjB,YAAY,UAAU,GAAG;QAEzB,YAAY,KAAK,GAAG,EAAE;QACtB,YAAY,KAAK,GAAG,EAAE;QAEtB,MAAM,QAAQ,CAAC,OAAO,eAAe,WAAW,aAAa,EAAE,EAC7D,IAAI,GACJ,OAAO,CAAC,QAAQ,KAChB,KAAK,CAAC,KACN,MAAM,CAAC;QAET,KAAK,MAAM,MAAM,MAAO;YACvB,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK;gBAClB,YAAY,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACjC,OAAO;gBACN,YAAY,KAAK,CAAC,IAAI,CAAC;YACxB;QACD;IACD;IAEA;;;;;;;EAOC,GACD,SAAS,gBAAgB,MAAM,EAAE,QAAQ;QACxC,IAAI,cAAc;QAClB,IAAI,gBAAgB;QACpB,IAAI,YAAY,CAAC;QACjB,IAAI,aAAa;QAEjB,MAAO,cAAc,OAAO,MAAM,CAAE;YACnC,IAAI,gBAAgB,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,KAAK,MAAM,CAAC,YAAY,IAAI,QAAQ,CAAC,cAAc,KAAK,GAAG,GAAG;gBAC5H,2CAA2C;gBAC3C,IAAI,QAAQ,CAAC,cAAc,KAAK,KAAK;oBACpC,YAAY;oBACZ,aAAa;oBACb,iBAAiB,eAAe;gBACjC,OAAO;oBACN;oBACA;gBACD;YACD,OAAO,IAAI,cAAc,CAAC,GAAG;gBAC5B,6DAA6D;gBAC7D,gBAAgB,YAAY;gBAC5B;gBACA,cAAc;YACf,OAAO;gBACN,OAAO,OAAO,WAAW;YAC1B;QACD;QAEA,kCAAkC;QAClC,MAAO,gBAAgB,SAAS,MAAM,IAAI,QAAQ,CAAC,cAAc,KAAK,IAAK;YAC1E;QACD;QAEA,OAAO,kBAAkB,SAAS,MAAM;IACzC;IAEA;;;;;CAKA,GACA,SAAS;QACR,MAAM,aAAa;eACf,YAAY,KAAK;eACjB,YAAY,KAAK,CAAC,GAAG,CAAC,CAAA,YAAa,MAAM;SAC5C,CAAC,IAAI,CAAC;QACP,YAAY,MAAM,CAAC;QACnB,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,QAAQ,IAAI;QACpB,KAAK,MAAM,QAAQ,YAAY,KAAK,CAAE;YACrC,IAAI,gBAAgB,MAAM,OAAO;gBAChC,OAAO;YACR;QACD;QAEA,KAAK,MAAM,MAAM,YAAY,KAAK,CAAE;YACnC,IAAI,gBAAgB,MAAM,KAAK;gBAC9B,OAAO;YACR;QACD;QAEA,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,OAAO,GAAG;QAClB,IAAI,eAAe,OAAO;YACzB,OAAO,IAAI,KAAK,IAAI,IAAI,OAAO;QAChC;QACA,OAAO;IACR;IAEA;;;CAGA,GACA,SAAS;QACR,QAAQ,IAAI,CAAC;IACd;IAEA,YAAY,MAAM,CAAC,YAAY,IAAI;IAEnC,OAAO;AACR;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/has-flag/index.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (flag, argv = process.argv) => {\n\tconst prefix = flag.startsWith('-') ? '' : (flag.length === 1 ? '-' : '--');\n\tconst position = argv.indexOf(prefix + flag);\n\tconst terminatorPosition = argv.indexOf('--');\n\treturn position !== -1 && (terminatorPosition === -1 || position < terminatorPosition);\n};\n"], "names": [], "mappings": "AAEA,OAAO,OAAO,GAAG,CAAC,MAAM,OAAO,QAAQ,IAAI;IAC1C,MAAM,SAAS,KAAK,UAAU,CAAC,OAAO,KAAM,KAAK,MAAM,KAAK,IAAI,MAAM;IACtE,MAAM,WAAW,KAAK,OAAO,CAAC,SAAS;IACvC,MAAM,qBAAqB,KAAK,OAAO,CAAC;IACxC,OAAO,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,WAAW,kBAAkB;AACtF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5083, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/supports-color/index.js"], "sourcesContent": ["'use strict';\nconst os = require('os');\nconst tty = require('tty');\nconst hasFlag = require('has-flag');\n\nconst {env} = process;\n\nlet forceColor;\nif (hasFlag('no-color') ||\n\thasFlag('no-colors') ||\n\thasFlag('color=false') ||\n\thasFlag('color=never')) {\n\tforceColor = 0;\n} else if (hasFlag('color') ||\n\thasFlag('colors') ||\n\thasFlag('color=true') ||\n\thasFlag('color=always')) {\n\tforceColor = 1;\n}\n\nif ('FORCE_COLOR' in env) {\n\tif (env.FORCE_COLOR === 'true') {\n\t\tforceColor = 1;\n\t} else if (env.FORCE_COLOR === 'false') {\n\t\tforceColor = 0;\n\t} else {\n\t\tforceColor = env.FORCE_COLOR.length === 0 ? 1 : Math.min(parseInt(env.FORCE_COLOR, 10), 3);\n\t}\n}\n\nfunction translateLevel(level) {\n\tif (level === 0) {\n\t\treturn false;\n\t}\n\n\treturn {\n\t\tlevel,\n\t\thasBasic: true,\n\t\thas256: level >= 2,\n\t\thas16m: level >= 3\n\t};\n}\n\nfunction supportsColor(haveStream, streamIsTTY) {\n\tif (forceColor === 0) {\n\t\treturn 0;\n\t}\n\n\tif (hasFlag('color=16m') ||\n\t\thasFlag('color=full') ||\n\t\thasFlag('color=truecolor')) {\n\t\treturn 3;\n\t}\n\n\tif (hasFlag('color=256')) {\n\t\treturn 2;\n\t}\n\n\tif (haveStream && !streamIsTTY && forceColor === undefined) {\n\t\treturn 0;\n\t}\n\n\tconst min = forceColor || 0;\n\n\tif (env.TERM === 'dumb') {\n\t\treturn min;\n\t}\n\n\tif (process.platform === 'win32') {\n\t\t// Windows 10 build 10586 is the first Windows release that supports 256 colors.\n\t\t// Windows 10 build 14931 is the first release that supports 16m/TrueColor.\n\t\tconst osRelease = os.release().split('.');\n\t\tif (\n\t\t\tNumber(osRelease[0]) >= 10 &&\n\t\t\tNumber(osRelease[2]) >= 10586\n\t\t) {\n\t\t\treturn Number(osRelease[2]) >= 14931 ? 3 : 2;\n\t\t}\n\n\t\treturn 1;\n\t}\n\n\tif ('CI' in env) {\n\t\tif (['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI', 'GITHUB_ACTIONS', 'BUILDKITE'].some(sign => sign in env) || env.CI_NAME === 'codeship') {\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn min;\n\t}\n\n\tif ('TEAMCITY_VERSION' in env) {\n\t\treturn /^(9\\.(0*[1-9]\\d*)\\.|\\d{2,}\\.)/.test(env.TEAMCITY_VERSION) ? 1 : 0;\n\t}\n\n\tif (env.COLORTERM === 'truecolor') {\n\t\treturn 3;\n\t}\n\n\tif ('TERM_PROGRAM' in env) {\n\t\tconst version = parseInt((env.TERM_PROGRAM_VERSION || '').split('.')[0], 10);\n\n\t\tswitch (env.TERM_PROGRAM) {\n\t\t\tcase 'iTerm.app':\n\t\t\t\treturn version >= 3 ? 3 : 2;\n\t\t\tcase 'Apple_Terminal':\n\t\t\t\treturn 2;\n\t\t\t// No default\n\t\t}\n\t}\n\n\tif (/-256(color)?$/i.test(env.TERM)) {\n\t\treturn 2;\n\t}\n\n\tif (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)) {\n\t\treturn 1;\n\t}\n\n\tif ('COLORTERM' in env) {\n\t\treturn 1;\n\t}\n\n\treturn min;\n}\n\nfunction getSupportLevel(stream) {\n\tconst level = supportsColor(stream, stream && stream.isTTY);\n\treturn translateLevel(level);\n}\n\nmodule.exports = {\n\tsupportsColor: getSupportLevel,\n\tstdout: translateLevel(supportsColor(true, tty.isatty(1))),\n\tstderr: translateLevel(supportsColor(true, tty.isatty(2)))\n};\n"], "names": [], "mappings": "AACA,MAAM;AACN,MAAM;AACN,MAAM;AAEN,MAAM,EAAC,GAAG,EAAC,GAAG;AAEd,IAAI;AACJ,IAAI,QAAQ,eACX,QAAQ,gBACR,QAAQ,kBACR,QAAQ,gBAAgB;IACxB,aAAa;AACd,OAAO,IAAI,QAAQ,YAClB,QAAQ,aACR,QAAQ,iBACR,QAAQ,iBAAiB;IACzB,aAAa;AACd;AAEA,IAAI,iBAAiB,KAAK;IACzB,IAAI,IAAI,WAAW,KAAK,QAAQ;QAC/B,aAAa;IACd,OAAO,IAAI,IAAI,WAAW,KAAK,SAAS;QACvC,aAAa;IACd,OAAO;QACN,aAAa,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC,SAAS,IAAI,WAAW,EAAE,KAAK;IACzF;AACD;AAEA,SAAS,eAAe,KAAK;IAC5B,IAAI,UAAU,GAAG;QAChB,OAAO;IACR;IAEA,OAAO;QACN;QACA,UAAU;QACV,QAAQ,SAAS;QACjB,QAAQ,SAAS;IAClB;AACD;AAEA,SAAS,cAAc,UAAU,EAAE,WAAW;IAC7C,IAAI,eAAe,GAAG;QACrB,OAAO;IACR;IAEA,IAAI,QAAQ,gBACX,QAAQ,iBACR,QAAQ,oBAAoB;QAC5B,OAAO;IACR;IAEA,IAAI,QAAQ,cAAc;QACzB,OAAO;IACR;IAEA,IAAI,cAAc,CAAC,eAAe,eAAe,WAAW;QAC3D,OAAO;IACR;IAEA,MAAM,MAAM,cAAc;IAE1B,IAAI,IAAI,IAAI,KAAK,QAAQ;QACxB,OAAO;IACR;IAEA,wCAAkC;QACjC,gFAAgF;QAChF,2EAA2E;QAC3E,MAAM,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC;QACrC,IACC,OAAO,SAAS,CAAC,EAAE,KAAK,MACxB,OAAO,SAAS,CAAC,EAAE,KAAK,OACvB;YACD,OAAO,OAAO,SAAS,CAAC,EAAE,KAAK,QAAQ,IAAI;QAC5C;QAEA,OAAO;IACR;;;AA2CD;AAEA,SAAS,gBAAgB,MAAM;IAC9B,MAAM,QAAQ,cAAc,QAAQ,UAAU,OAAO,KAAK;IAC1D,OAAO,eAAe;AACvB;AAEA,OAAO,OAAO,GAAG;IAChB,eAAe;IACf,QAAQ,eAAe,cAAc,MAAM,IAAI,MAAM,CAAC;IACtD,QAAQ,eAAe,cAAc,MAAM,IAAI,MAAM,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/debug/src/node.js"], "sourcesContent": ["/**\n * Module dependencies.\n */\n\nconst tty = require('tty');\nconst util = require('util');\n\n/**\n * This is the Node.js implementation of `debug()`.\n */\n\nexports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.destroy = util.deprecate(\n\t() => {},\n\t'Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.'\n);\n\n/**\n * Colors.\n */\n\nexports.colors = [6, 2, 3, 4, 5, 1];\n\ntry {\n\t// Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)\n\t// eslint-disable-next-line import/no-extraneous-dependencies\n\tconst supportsColor = require('supports-color');\n\n\tif (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {\n\t\texports.colors = [\n\t\t\t20,\n\t\t\t21,\n\t\t\t26,\n\t\t\t27,\n\t\t\t32,\n\t\t\t33,\n\t\t\t38,\n\t\t\t39,\n\t\t\t40,\n\t\t\t41,\n\t\t\t42,\n\t\t\t43,\n\t\t\t44,\n\t\t\t45,\n\t\t\t56,\n\t\t\t57,\n\t\t\t62,\n\t\t\t63,\n\t\t\t68,\n\t\t\t69,\n\t\t\t74,\n\t\t\t75,\n\t\t\t76,\n\t\t\t77,\n\t\t\t78,\n\t\t\t79,\n\t\t\t80,\n\t\t\t81,\n\t\t\t92,\n\t\t\t93,\n\t\t\t98,\n\t\t\t99,\n\t\t\t112,\n\t\t\t113,\n\t\t\t128,\n\t\t\t129,\n\t\t\t134,\n\t\t\t135,\n\t\t\t148,\n\t\t\t149,\n\t\t\t160,\n\t\t\t161,\n\t\t\t162,\n\t\t\t163,\n\t\t\t164,\n\t\t\t165,\n\t\t\t166,\n\t\t\t167,\n\t\t\t168,\n\t\t\t169,\n\t\t\t170,\n\t\t\t171,\n\t\t\t172,\n\t\t\t173,\n\t\t\t178,\n\t\t\t179,\n\t\t\t184,\n\t\t\t185,\n\t\t\t196,\n\t\t\t197,\n\t\t\t198,\n\t\t\t199,\n\t\t\t200,\n\t\t\t201,\n\t\t\t202,\n\t\t\t203,\n\t\t\t204,\n\t\t\t205,\n\t\t\t206,\n\t\t\t207,\n\t\t\t208,\n\t\t\t209,\n\t\t\t214,\n\t\t\t215,\n\t\t\t220,\n\t\t\t221\n\t\t];\n\t}\n} catch (error) {\n\t// Swallow - we only care if `supports-color` is available; it doesn't have to be.\n}\n\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */\n\nexports.inspectOpts = Object.keys(process.env).filter(key => {\n\treturn /^debug_/i.test(key);\n}).reduce((obj, key) => {\n\t// Camel-case\n\tconst prop = key\n\t\t.substring(6)\n\t\t.toLowerCase()\n\t\t.replace(/_([a-z])/g, (_, k) => {\n\t\t\treturn k.toUpperCase();\n\t\t});\n\n\t// Coerce string value into JS value\n\tlet val = process.env[key];\n\tif (/^(yes|on|true|enabled)$/i.test(val)) {\n\t\tval = true;\n\t} else if (/^(no|off|false|disabled)$/i.test(val)) {\n\t\tval = false;\n\t} else if (val === 'null') {\n\t\tval = null;\n\t} else {\n\t\tval = Number(val);\n\t}\n\n\tobj[prop] = val;\n\treturn obj;\n}, {});\n\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */\n\nfunction useColors() {\n\treturn 'colors' in exports.inspectOpts ?\n\t\tBoolean(exports.inspectOpts.colors) :\n\t\ttty.isatty(process.stderr.fd);\n}\n\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\tconst {namespace: name, useColors} = this;\n\n\tif (useColors) {\n\t\tconst c = this.color;\n\t\tconst colorCode = '\\u001B[3' + (c < 8 ? c : '8;5;' + c);\n\t\tconst prefix = `  ${colorCode};1m${name} \\u001B[0m`;\n\n\t\targs[0] = prefix + args[0].split('\\n').join('\\n' + prefix);\n\t\targs.push(colorCode + 'm+' + module.exports.humanize(this.diff) + '\\u001B[0m');\n\t} else {\n\t\targs[0] = getDate() + name + ' ' + args[0];\n\t}\n}\n\nfunction getDate() {\n\tif (exports.inspectOpts.hideDate) {\n\t\treturn '';\n\t}\n\treturn new Date().toISOString() + ' ';\n}\n\n/**\n * Invokes `util.formatWithOptions()` with the specified arguments and writes to stderr.\n */\n\nfunction log(...args) {\n\treturn process.stderr.write(util.formatWithOptions(exports.inspectOpts, ...args) + '\\n');\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\tif (namespaces) {\n\t\tprocess.env.DEBUG = namespaces;\n\t} else {\n\t\t// If you set a process.env field to null or undefined, it gets cast to the\n\t\t// string 'null' or 'undefined'. Just delete instead.\n\t\tdelete process.env.DEBUG;\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n\treturn process.env.DEBUG;\n}\n\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */\n\nfunction init(debug) {\n\tdebug.inspectOpts = {};\n\n\tconst keys = Object.keys(exports.inspectOpts);\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tdebug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */\n\nformatters.o = function (v) {\n\tthis.inspectOpts.colors = this.useColors;\n\treturn util.inspect(v, this.inspectOpts)\n\t\t.split('\\n')\n\t\t.map(str => str.trim())\n\t\t.join(' ');\n};\n\n/**\n * Map %O to `util.inspect()`, allowing multiple lines if needed.\n */\n\nformatters.O = function (v) {\n\tthis.inspectOpts.colors = this.useColors;\n\treturn util.inspect(v, this.inspectOpts);\n};\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,MAAM;AACN,MAAM;AAEN;;CAEC,GAED,QAAQ,IAAI,GAAG;AACf,QAAQ,GAAG,GAAG;AACd,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG;AACf,QAAQ,IAAI,GAAG;AACf,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG,KAAK,SAAS,CAC/B,KAAO,GACP;AAGD;;CAEC,GAED,QAAQ,MAAM,GAAG;IAAC;IAAG;IAAG;IAAG;IAAG;IAAG;CAAE;AAEnC,IAAI;IACH,2GAA2G;IAC3G,6DAA6D;IAC7D,MAAM;IAEN,IAAI,iBAAiB,CAAC,cAAc,MAAM,IAAI,aAAa,EAAE,KAAK,IAAI,GAAG;QACxE,QAAQ,MAAM,GAAG;YAChB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACA;IACF;AACD,EAAE,OAAO,OAAO;AACf,kFAAkF;AACnF;AAEA;;;;CAIC,GAED,QAAQ,WAAW,GAAG,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE,MAAM,CAAC,CAAA;IACrD,OAAO,WAAW,IAAI,CAAC;AACxB,GAAG,MAAM,CAAC,CAAC,KAAK;IACf,aAAa;IACb,MAAM,OAAO,IACX,SAAS,CAAC,GACV,WAAW,GACX,OAAO,CAAC,aAAa,CAAC,GAAG;QACzB,OAAO,EAAE,WAAW;IACrB;IAED,oCAAoC;IACpC,IAAI,MAAM,QAAQ,GAAG,CAAC,IAAI;IAC1B,IAAI,2BAA2B,IAAI,CAAC,MAAM;QACzC,MAAM;IACP,OAAO,IAAI,6BAA6B,IAAI,CAAC,MAAM;QAClD,MAAM;IACP,OAAO,IAAI,QAAQ,QAAQ;QAC1B,MAAM;IACP,OAAO;QACN,MAAM,OAAO;IACd;IAEA,GAAG,CAAC,KAAK,GAAG;IACZ,OAAO;AACR,GAAG,CAAC;AAEJ;;CAEC,GAED,SAAS;IACR,OAAO,YAAY,QAAQ,WAAW,GACrC,QAAQ,QAAQ,WAAW,CAAC,MAAM,IAClC,IAAI,MAAM,CAAC,QAAQ,MAAM,CAAC,EAAE;AAC9B;AAEA;;;;CAIC,GAED,SAAS,WAAW,IAAI;IACvB,MAAM,EAAC,WAAW,IAAI,EAAE,SAAS,EAAC,GAAG,IAAI;IAEzC,IAAI,WAAW;QACd,MAAM,IAAI,IAAI,CAAC,KAAK;QACpB,MAAM,YAAY,aAAa,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;QACtD,MAAM,SAAS,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,KAAK,UAAU,CAAC;QAEnD,IAAI,CAAC,EAAE,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,OAAO;QACnD,KAAK,IAAI,CAAC,YAAY,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI;IACnE,OAAO;QACN,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,MAAM,IAAI,CAAC,EAAE;IAC3C;AACD;AAEA,SAAS;IACR,IAAI,QAAQ,WAAW,CAAC,QAAQ,EAAE;QACjC,OAAO;IACR;IACA,OAAO,IAAI,OAAO,WAAW,KAAK;AACnC;AAEA;;CAEC,GAED,SAAS,IAAI,GAAG,IAAI;IACnB,OAAO,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC,QAAQ,WAAW,KAAK,QAAQ;AACpF;AAEA;;;;;CAKC,GACD,SAAS,KAAK,UAAU;IACvB,IAAI,YAAY;QACf,QAAQ,GAAG,CAAC,KAAK,GAAG;IACrB,OAAO;QACN,2EAA2E;QAC3E,qDAAqD;QACrD,OAAO,QAAQ,GAAG,CAAC,KAAK;IACzB;AACD;AAEA;;;;;CAKC,GAED,SAAS;IACR,OAAO,QAAQ,GAAG,CAAC,KAAK;AACzB;AAEA;;;;;CAKC,GAED,SAAS,KAAK,KAAK;IAClB,MAAM,WAAW,GAAG,CAAC;IAErB,MAAM,OAAO,OAAO,IAAI,CAAC,QAAQ,WAAW;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACrC,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1D;AACD;AAEA,OAAO,OAAO,GAAG,6FAAoB;AAErC,MAAM,EAAC,UAAU,EAAC,GAAG,OAAO,OAAO;AAEnC;;CAEC,GAED,WAAW,CAAC,GAAG,SAAU,CAAC;IACzB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;IACxC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,EACrC,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,IAAI,CAAC;AACR;AAEA;;CAEC,GAED,WAAW,CAAC,GAAG,SAAU,CAAC;IACzB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;IACxC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/debug/src/browser.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug') || exports.storage.getItem('DEBUG') ;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n"], "names": [], "mappings": "AAAA,sBAAsB,GAEtB;;CAEC,GAED,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG;AACf,QAAQ,IAAI,GAAG;AACf,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG,CAAC;IAClB,IAAI,SAAS;IAEb,OAAO;QACN,IAAI,CAAC,QAAQ;YACZ,SAAS;YACT,QAAQ,IAAI,CAAC;QACd;IACD;AACD,CAAC;AAED;;CAEC,GAED,QAAQ,MAAM,GAAG;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACA;AAED;;;;;;CAMC,GAED,sCAAsC;AACtC,SAAS;IACR,4EAA4E;IAC5E,0EAA0E;IAC1E,aAAa;IACb;;IAIA,oDAAoD;IACpD,IAAI,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,0BAA0B;QAChI,OAAO;IACR;IAEA,IAAI;IAEJ,wDAAwD;IACxD,4FAA4F;IAC5F,4CAA4C;IAC5C,OAAO,AAAC,OAAO,aAAa,eAAe,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,KAAK,IAAI,SAAS,eAAe,CAAC,KAAK,CAAC,gBAAgB,IAEtJ,gBAAkB,eAAe,OAAO,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,IAAK,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,KAAK,AAAC,KAGhI,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,CAAC,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,EAAE,EAAE,OAAO,MAEpJ,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;AACtG;AAEA;;;;CAIC,GAED,SAAS,WAAW,IAAI;IACvB,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,IACpC,IAAI,CAAC,SAAS,GACd,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAC7B,IAAI,CAAC,EAAE,GACP,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAC7B,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;IAExC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACpB;IACD;IAEA,MAAM,IAAI,YAAY,IAAI,CAAC,KAAK;IAChC,KAAK,MAAM,CAAC,GAAG,GAAG,GAAG;IAErB,kEAAkE;IAClE,gEAAgE;IAChE,sDAAsD;IACtD,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,eAAe,CAAA;QAC9B,IAAI,UAAU,MAAM;YACnB;QACD;QACA;QACA,IAAI,UAAU,MAAM;YACnB,0CAA0C;YAC1C,yCAAyC;YACzC,QAAQ;QACT;IACD;IAEA,KAAK,MAAM,CAAC,OAAO,GAAG;AACvB;AAEA;;;;;;;CAOC,GACD,QAAQ,GAAG,GAAG,QAAQ,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAO,CAAC;AAEvD;;;;;CAKC,GACD,SAAS,KAAK,UAAU;IACvB,IAAI;QACH,IAAI,YAAY;YACf,QAAQ,OAAO,CAAC,OAAO,CAAC,SAAS;QAClC,OAAO;YACN,QAAQ,OAAO,CAAC,UAAU,CAAC;QAC5B;IACD,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;AACD;AAEA;;;;;CAKC,GACD,SAAS;IACR,IAAI;IACJ,IAAI;QACH,IAAI,QAAQ,OAAO,CAAC,OAAO,CAAC,YAAY,QAAQ,OAAO,CAAC,OAAO,CAAC;IACjE,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;IAEA,sEAAsE;IACtE,IAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;QAC7D,IAAI,QAAQ,GAAG,CAAC,KAAK;IACtB;IAEA,OAAO;AACR;AAEA;;;;;;;;;CASC,GAED,SAAS;IACR,IAAI;QACH,uGAAuG;QACvG,2DAA2D;QAC3D,OAAO;IACR,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;AACD;AAEA,OAAO,OAAO,GAAG,6FAAoB;AAErC,MAAM,EAAC,UAAU,EAAC,GAAG,OAAO,OAAO;AAEnC;;CAEC,GAED,WAAW,CAAC,GAAG,SAAU,CAAC;IACzB,IAAI;QACH,OAAO,KAAK,SAAS,CAAC;IACvB,EAAE,OAAO,OAAO;QACf,OAAO,iCAAiC,MAAM,OAAO;IACtD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/debug/src/index.js"], "sourcesContent": ["/**\n * Detect Electron renderer / nwjs process, which is node, but we should\n * treat as a browser.\n */\n\nif (typeof process === 'undefined' || process.type === 'renderer' || process.browser === true || process.__nwjs) {\n\tmodule.exports = require('./browser.js');\n} else {\n\tmodule.exports = require('./node.js');\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,IAAI,OAAO,YAAY,eAAe,QAAQ,IAAI,KAAK,cAAc,4CAAoB,QAAQ,QAAQ,MAAM,EAAE;IAChH,OAAO,OAAO;AACf,OAAO;IACN,OAAO,OAAO;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/follow-redirects/debug.js"], "sourcesContent": ["var debug;\n\nmodule.exports = function () {\n  if (!debug) {\n    try {\n      /* eslint global-require: off */\n      debug = require(\"debug\")(\"follow-redirects\");\n    }\n    catch (error) { /* */ }\n    if (typeof debug !== \"function\") {\n      debug = function () { /* */ };\n    }\n  }\n  debug.apply(null, arguments);\n};\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,OAAO,OAAO,GAAG;IACf,IAAI,CAAC,OAAO;QACV,IAAI;YACF,8BAA8B,GAC9B,QAAQ,4FAAiB;QAC3B,EACA,OAAO,OAAO,CAAQ;QACtB,IAAI,OAAO,UAAU,YAAY;YAC/B,QAAQ,YAAoB;QAC9B;IACF;IACA,MAAM,KAAK,CAAC,MAAM;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/follow-redirects/index.js"], "sourcesContent": ["var url = require(\"url\");\nvar URL = url.URL;\nvar http = require(\"http\");\nvar https = require(\"https\");\nvar Writable = require(\"stream\").Writable;\nvar assert = require(\"assert\");\nvar debug = require(\"./debug\");\n\n// Preventive platform detection\n// istanbul ignore next\n(function detectUnsupportedEnvironment() {\n  var looksLikeNode = typeof process !== \"undefined\";\n  var looksLikeBrowser = typeof window !== \"undefined\" && typeof document !== \"undefined\";\n  var looksLikeV8 = isFunction(Error.captureStackTrace);\n  if (!looksLikeNode && (looksLikeBrowser || !looksLikeV8)) {\n    console.warn(\"The follow-redirects package should be excluded from browser builds.\");\n  }\n}());\n\n// Whether to use the native URL object or the legacy url module\nvar useNativeURL = false;\ntry {\n  assert(new URL(\"\"));\n}\ncatch (error) {\n  useNativeURL = error.code === \"ERR_INVALID_URL\";\n}\n\n// URL fields to preserve in copy operations\nvar preservedUrlFields = [\n  \"auth\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"path\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"query\",\n  \"search\",\n  \"hash\",\n];\n\n// Create handlers that pass events from native requests\nvar events = [\"abort\", \"aborted\", \"connect\", \"error\", \"socket\", \"timeout\"];\nvar eventHandlers = Object.create(null);\nevents.forEach(function (event) {\n  eventHandlers[event] = function (arg1, arg2, arg3) {\n    this._redirectable.emit(event, arg1, arg2, arg3);\n  };\n});\n\n// Error types with codes\nvar InvalidUrlError = createErrorType(\n  \"ERR_INVALID_URL\",\n  \"Invalid URL\",\n  TypeError\n);\nvar RedirectionError = createErrorType(\n  \"ERR_FR_REDIRECTION_FAILURE\",\n  \"Redirected request failed\"\n);\nvar TooManyRedirectsError = createErrorType(\n  \"ERR_FR_TOO_MANY_REDIRECTS\",\n  \"Maximum number of redirects exceeded\",\n  RedirectionError\n);\nvar MaxBodyLengthExceededError = createErrorType(\n  \"ERR_FR_MAX_BODY_LENGTH_EXCEEDED\",\n  \"Request body larger than maxBodyLength limit\"\n);\nvar WriteAfterEndError = createErrorType(\n  \"ERR_STREAM_WRITE_AFTER_END\",\n  \"write after end\"\n);\n\n// istanbul ignore next\nvar destroy = Writable.prototype.destroy || noop;\n\n// An HTTP(S) request that can be redirected\nfunction RedirectableRequest(options, responseCallback) {\n  // Initialize the request\n  Writable.call(this);\n  this._sanitizeOptions(options);\n  this._options = options;\n  this._ended = false;\n  this._ending = false;\n  this._redirectCount = 0;\n  this._redirects = [];\n  this._requestBodyLength = 0;\n  this._requestBodyBuffers = [];\n\n  // Attach a callback if passed\n  if (responseCallback) {\n    this.on(\"response\", responseCallback);\n  }\n\n  // React to responses of native requests\n  var self = this;\n  this._onNativeResponse = function (response) {\n    try {\n      self._processResponse(response);\n    }\n    catch (cause) {\n      self.emit(\"error\", cause instanceof RedirectionError ?\n        cause : new RedirectionError({ cause: cause }));\n    }\n  };\n\n  // Perform the first request\n  this._performRequest();\n}\nRedirectableRequest.prototype = Object.create(Writable.prototype);\n\nRedirectableRequest.prototype.abort = function () {\n  destroyRequest(this._currentRequest);\n  this._currentRequest.abort();\n  this.emit(\"abort\");\n};\n\nRedirectableRequest.prototype.destroy = function (error) {\n  destroyRequest(this._currentRequest, error);\n  destroy.call(this, error);\n  return this;\n};\n\n// Writes buffered data to the current native request\nRedirectableRequest.prototype.write = function (data, encoding, callback) {\n  // Writing is not allowed if end has been called\n  if (this._ending) {\n    throw new WriteAfterEndError();\n  }\n\n  // Validate input and shift parameters if necessary\n  if (!isString(data) && !isBuffer(data)) {\n    throw new TypeError(\"data should be a string, Buffer or Uint8Array\");\n  }\n  if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Ignore empty buffers, since writing them doesn't invoke the callback\n  // https://github.com/nodejs/node/issues/22066\n  if (data.length === 0) {\n    if (callback) {\n      callback();\n    }\n    return;\n  }\n  // Only write when we don't exceed the maximum body length\n  if (this._requestBodyLength + data.length <= this._options.maxBodyLength) {\n    this._requestBodyLength += data.length;\n    this._requestBodyBuffers.push({ data: data, encoding: encoding });\n    this._currentRequest.write(data, encoding, callback);\n  }\n  // Error when we exceed the maximum body length\n  else {\n    this.emit(\"error\", new MaxBodyLengthExceededError());\n    this.abort();\n  }\n};\n\n// Ends the current native request\nRedirectableRequest.prototype.end = function (data, encoding, callback) {\n  // Shift parameters if necessary\n  if (isFunction(data)) {\n    callback = data;\n    data = encoding = null;\n  }\n  else if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Write data if needed and end\n  if (!data) {\n    this._ended = this._ending = true;\n    this._currentRequest.end(null, null, callback);\n  }\n  else {\n    var self = this;\n    var currentRequest = this._currentRequest;\n    this.write(data, encoding, function () {\n      self._ended = true;\n      currentRequest.end(null, null, callback);\n    });\n    this._ending = true;\n  }\n};\n\n// Sets a header value on the current native request\nRedirectableRequest.prototype.setHeader = function (name, value) {\n  this._options.headers[name] = value;\n  this._currentRequest.setHeader(name, value);\n};\n\n// Clears a header value on the current native request\nRedirectableRequest.prototype.removeHeader = function (name) {\n  delete this._options.headers[name];\n  this._currentRequest.removeHeader(name);\n};\n\n// Global timeout for all underlying requests\nRedirectableRequest.prototype.setTimeout = function (msecs, callback) {\n  var self = this;\n\n  // Destroys the socket on timeout\n  function destroyOnTimeout(socket) {\n    socket.setTimeout(msecs);\n    socket.removeListener(\"timeout\", socket.destroy);\n    socket.addListener(\"timeout\", socket.destroy);\n  }\n\n  // Sets up a timer to trigger a timeout event\n  function startTimer(socket) {\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n    }\n    self._timeout = setTimeout(function () {\n      self.emit(\"timeout\");\n      clearTimer();\n    }, msecs);\n    destroyOnTimeout(socket);\n  }\n\n  // Stops a timeout from triggering\n  function clearTimer() {\n    // Clear the timeout\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n      self._timeout = null;\n    }\n\n    // Clean up all attached listeners\n    self.removeListener(\"abort\", clearTimer);\n    self.removeListener(\"error\", clearTimer);\n    self.removeListener(\"response\", clearTimer);\n    self.removeListener(\"close\", clearTimer);\n    if (callback) {\n      self.removeListener(\"timeout\", callback);\n    }\n    if (!self.socket) {\n      self._currentRequest.removeListener(\"socket\", startTimer);\n    }\n  }\n\n  // Attach callback if passed\n  if (callback) {\n    this.on(\"timeout\", callback);\n  }\n\n  // Start the timer if or when the socket is opened\n  if (this.socket) {\n    startTimer(this.socket);\n  }\n  else {\n    this._currentRequest.once(\"socket\", startTimer);\n  }\n\n  // Clean up on events\n  this.on(\"socket\", destroyOnTimeout);\n  this.on(\"abort\", clearTimer);\n  this.on(\"error\", clearTimer);\n  this.on(\"response\", clearTimer);\n  this.on(\"close\", clearTimer);\n\n  return this;\n};\n\n// Proxy all other public ClientRequest methods\n[\n  \"flushHeaders\", \"getHeader\",\n  \"setNoDelay\", \"setSocketKeepAlive\",\n].forEach(function (method) {\n  RedirectableRequest.prototype[method] = function (a, b) {\n    return this._currentRequest[method](a, b);\n  };\n});\n\n// Proxy all public ClientRequest properties\n[\"aborted\", \"connection\", \"socket\"].forEach(function (property) {\n  Object.defineProperty(RedirectableRequest.prototype, property, {\n    get: function () { return this._currentRequest[property]; },\n  });\n});\n\nRedirectableRequest.prototype._sanitizeOptions = function (options) {\n  // Ensure headers are always present\n  if (!options.headers) {\n    options.headers = {};\n  }\n\n  // Since http.request treats host as an alias of hostname,\n  // but the url module interprets host as hostname plus port,\n  // eliminate the host property to avoid confusion.\n  if (options.host) {\n    // Use hostname if set, because it has precedence\n    if (!options.hostname) {\n      options.hostname = options.host;\n    }\n    delete options.host;\n  }\n\n  // Complete the URL object when necessary\n  if (!options.pathname && options.path) {\n    var searchPos = options.path.indexOf(\"?\");\n    if (searchPos < 0) {\n      options.pathname = options.path;\n    }\n    else {\n      options.pathname = options.path.substring(0, searchPos);\n      options.search = options.path.substring(searchPos);\n    }\n  }\n};\n\n\n// Executes the next native request (initial or redirect)\nRedirectableRequest.prototype._performRequest = function () {\n  // Load the native protocol\n  var protocol = this._options.protocol;\n  var nativeProtocol = this._options.nativeProtocols[protocol];\n  if (!nativeProtocol) {\n    throw new TypeError(\"Unsupported protocol \" + protocol);\n  }\n\n  // If specified, use the agent corresponding to the protocol\n  // (HTTP and HTTPS use different types of agents)\n  if (this._options.agents) {\n    var scheme = protocol.slice(0, -1);\n    this._options.agent = this._options.agents[scheme];\n  }\n\n  // Create the native request and set up its event handlers\n  var request = this._currentRequest =\n        nativeProtocol.request(this._options, this._onNativeResponse);\n  request._redirectable = this;\n  for (var event of events) {\n    request.on(event, eventHandlers[event]);\n  }\n\n  // RFC7230§5.3.1: When making a request directly to an origin server, […]\n  // a client MUST send only the absolute path […] as the request-target.\n  this._currentUrl = /^\\//.test(this._options.path) ?\n    url.format(this._options) :\n    // When making a request to a proxy, […]\n    // a client MUST send the target URI in absolute-form […].\n    this._options.path;\n\n  // End a redirected request\n  // (The first request must be ended explicitly with RedirectableRequest#end)\n  if (this._isRedirect) {\n    // Write the request entity and end\n    var i = 0;\n    var self = this;\n    var buffers = this._requestBodyBuffers;\n    (function writeNext(error) {\n      // Only write if this request has not been redirected yet\n      // istanbul ignore else\n      if (request === self._currentRequest) {\n        // Report any write errors\n        // istanbul ignore if\n        if (error) {\n          self.emit(\"error\", error);\n        }\n        // Write the next buffer if there are still left\n        else if (i < buffers.length) {\n          var buffer = buffers[i++];\n          // istanbul ignore else\n          if (!request.finished) {\n            request.write(buffer.data, buffer.encoding, writeNext);\n          }\n        }\n        // End the request if `end` has been called on us\n        else if (self._ended) {\n          request.end();\n        }\n      }\n    }());\n  }\n};\n\n// Processes a response from the current native request\nRedirectableRequest.prototype._processResponse = function (response) {\n  // Store the redirected response\n  var statusCode = response.statusCode;\n  if (this._options.trackRedirects) {\n    this._redirects.push({\n      url: this._currentUrl,\n      headers: response.headers,\n      statusCode: statusCode,\n    });\n  }\n\n  // RFC7231§6.4: The 3xx (Redirection) class of status code indicates\n  // that further action needs to be taken by the user agent in order to\n  // fulfill the request. If a Location header field is provided,\n  // the user agent MAY automatically redirect its request to the URI\n  // referenced by the Location field value,\n  // even if the specific status code is not understood.\n\n  // If the response is not a redirect; return it as-is\n  var location = response.headers.location;\n  if (!location || this._options.followRedirects === false ||\n      statusCode < 300 || statusCode >= 400) {\n    response.responseUrl = this._currentUrl;\n    response.redirects = this._redirects;\n    this.emit(\"response\", response);\n\n    // Clean up\n    this._requestBodyBuffers = [];\n    return;\n  }\n\n  // The response is a redirect, so abort the current request\n  destroyRequest(this._currentRequest);\n  // Discard the remainder of the response to avoid waiting for data\n  response.destroy();\n\n  // RFC7231§6.4: A client SHOULD detect and intervene\n  // in cyclical redirections (i.e., \"infinite\" redirection loops).\n  if (++this._redirectCount > this._options.maxRedirects) {\n    throw new TooManyRedirectsError();\n  }\n\n  // Store the request headers if applicable\n  var requestHeaders;\n  var beforeRedirect = this._options.beforeRedirect;\n  if (beforeRedirect) {\n    requestHeaders = Object.assign({\n      // The Host header was set by nativeProtocol.request\n      Host: response.req.getHeader(\"host\"),\n    }, this._options.headers);\n  }\n\n  // RFC7231§6.4: Automatic redirection needs to done with\n  // care for methods not known to be safe, […]\n  // RFC7231§6.4.2–3: For historical reasons, a user agent MAY change\n  // the request method from POST to GET for the subsequent request.\n  var method = this._options.method;\n  if ((statusCode === 301 || statusCode === 302) && this._options.method === \"POST\" ||\n      // RFC7231§6.4.4: The 303 (See Other) status code indicates that\n      // the server is redirecting the user agent to a different resource […]\n      // A user agent can perform a retrieval request targeting that URI\n      // (a GET or HEAD request if using HTTP) […]\n      (statusCode === 303) && !/^(?:GET|HEAD)$/.test(this._options.method)) {\n    this._options.method = \"GET\";\n    // Drop a possible entity and headers related to it\n    this._requestBodyBuffers = [];\n    removeMatchingHeaders(/^content-/i, this._options.headers);\n  }\n\n  // Drop the Host header, as the redirect might lead to a different host\n  var currentHostHeader = removeMatchingHeaders(/^host$/i, this._options.headers);\n\n  // If the redirect is relative, carry over the host of the last request\n  var currentUrlParts = parseUrl(this._currentUrl);\n  var currentHost = currentHostHeader || currentUrlParts.host;\n  var currentUrl = /^\\w+:/.test(location) ? this._currentUrl :\n    url.format(Object.assign(currentUrlParts, { host: currentHost }));\n\n  // Create the redirected request\n  var redirectUrl = resolveUrl(location, currentUrl);\n  debug(\"redirecting to\", redirectUrl.href);\n  this._isRedirect = true;\n  spreadUrlObject(redirectUrl, this._options);\n\n  // Drop confidential headers when redirecting to a less secure protocol\n  // or to a different domain that is not a superdomain\n  if (redirectUrl.protocol !== currentUrlParts.protocol &&\n     redirectUrl.protocol !== \"https:\" ||\n     redirectUrl.host !== currentHost &&\n     !isSubdomain(redirectUrl.host, currentHost)) {\n    removeMatchingHeaders(/^(?:(?:proxy-)?authorization|cookie)$/i, this._options.headers);\n  }\n\n  // Evaluate the beforeRedirect callback\n  if (isFunction(beforeRedirect)) {\n    var responseDetails = {\n      headers: response.headers,\n      statusCode: statusCode,\n    };\n    var requestDetails = {\n      url: currentUrl,\n      method: method,\n      headers: requestHeaders,\n    };\n    beforeRedirect(this._options, responseDetails, requestDetails);\n    this._sanitizeOptions(this._options);\n  }\n\n  // Perform the redirected request\n  this._performRequest();\n};\n\n// Wraps the key/value object of protocols with redirect functionality\nfunction wrap(protocols) {\n  // Default settings\n  var exports = {\n    maxRedirects: 21,\n    maxBodyLength: 10 * 1024 * 1024,\n  };\n\n  // Wrap each protocol\n  var nativeProtocols = {};\n  Object.keys(protocols).forEach(function (scheme) {\n    var protocol = scheme + \":\";\n    var nativeProtocol = nativeProtocols[protocol] = protocols[scheme];\n    var wrappedProtocol = exports[scheme] = Object.create(nativeProtocol);\n\n    // Executes a request, following redirects\n    function request(input, options, callback) {\n      // Parse parameters, ensuring that input is an object\n      if (isURL(input)) {\n        input = spreadUrlObject(input);\n      }\n      else if (isString(input)) {\n        input = spreadUrlObject(parseUrl(input));\n      }\n      else {\n        callback = options;\n        options = validateUrl(input);\n        input = { protocol: protocol };\n      }\n      if (isFunction(options)) {\n        callback = options;\n        options = null;\n      }\n\n      // Set defaults\n      options = Object.assign({\n        maxRedirects: exports.maxRedirects,\n        maxBodyLength: exports.maxBodyLength,\n      }, input, options);\n      options.nativeProtocols = nativeProtocols;\n      if (!isString(options.host) && !isString(options.hostname)) {\n        options.hostname = \"::1\";\n      }\n\n      assert.equal(options.protocol, protocol, \"protocol mismatch\");\n      debug(\"options\", options);\n      return new RedirectableRequest(options, callback);\n    }\n\n    // Executes a GET request, following redirects\n    function get(input, options, callback) {\n      var wrappedRequest = wrappedProtocol.request(input, options, callback);\n      wrappedRequest.end();\n      return wrappedRequest;\n    }\n\n    // Expose the properties on the wrapped protocol\n    Object.defineProperties(wrappedProtocol, {\n      request: { value: request, configurable: true, enumerable: true, writable: true },\n      get: { value: get, configurable: true, enumerable: true, writable: true },\n    });\n  });\n  return exports;\n}\n\nfunction noop() { /* empty */ }\n\nfunction parseUrl(input) {\n  var parsed;\n  // istanbul ignore else\n  if (useNativeURL) {\n    parsed = new URL(input);\n  }\n  else {\n    // Ensure the URL is valid and absolute\n    parsed = validateUrl(url.parse(input));\n    if (!isString(parsed.protocol)) {\n      throw new InvalidUrlError({ input });\n    }\n  }\n  return parsed;\n}\n\nfunction resolveUrl(relative, base) {\n  // istanbul ignore next\n  return useNativeURL ? new URL(relative, base) : parseUrl(url.resolve(base, relative));\n}\n\nfunction validateUrl(input) {\n  if (/^\\[/.test(input.hostname) && !/^\\[[:0-9a-f]+\\]$/i.test(input.hostname)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  if (/^\\[/.test(input.host) && !/^\\[[:0-9a-f]+\\](:\\d+)?$/i.test(input.host)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  return input;\n}\n\nfunction spreadUrlObject(urlObject, target) {\n  var spread = target || {};\n  for (var key of preservedUrlFields) {\n    spread[key] = urlObject[key];\n  }\n\n  // Fix IPv6 hostname\n  if (spread.hostname.startsWith(\"[\")) {\n    spread.hostname = spread.hostname.slice(1, -1);\n  }\n  // Ensure port is a number\n  if (spread.port !== \"\") {\n    spread.port = Number(spread.port);\n  }\n  // Concatenate path\n  spread.path = spread.search ? spread.pathname + spread.search : spread.pathname;\n\n  return spread;\n}\n\nfunction removeMatchingHeaders(regex, headers) {\n  var lastValue;\n  for (var header in headers) {\n    if (regex.test(header)) {\n      lastValue = headers[header];\n      delete headers[header];\n    }\n  }\n  return (lastValue === null || typeof lastValue === \"undefined\") ?\n    undefined : String(lastValue).trim();\n}\n\nfunction createErrorType(code, message, baseClass) {\n  // Create constructor\n  function CustomError(properties) {\n    // istanbul ignore else\n    if (isFunction(Error.captureStackTrace)) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n    Object.assign(this, properties || {});\n    this.code = code;\n    this.message = this.cause ? message + \": \" + this.cause.message : message;\n  }\n\n  // Attach constructor and set default properties\n  CustomError.prototype = new (baseClass || Error)();\n  Object.defineProperties(CustomError.prototype, {\n    constructor: {\n      value: CustomError,\n      enumerable: false,\n    },\n    name: {\n      value: \"Error [\" + code + \"]\",\n      enumerable: false,\n    },\n  });\n  return CustomError;\n}\n\nfunction destroyRequest(request, error) {\n  for (var event of events) {\n    request.removeListener(event, eventHandlers[event]);\n  }\n  request.on(\"error\", noop);\n  request.destroy(error);\n}\n\nfunction isSubdomain(subdomain, domain) {\n  assert(isString(subdomain) && isString(domain));\n  var dot = subdomain.length - domain.length - 1;\n  return dot > 0 && subdomain[dot] === \".\" && subdomain.endsWith(domain);\n}\n\nfunction isString(value) {\n  return typeof value === \"string\" || value instanceof String;\n}\n\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\nfunction isBuffer(value) {\n  return typeof value === \"object\" && (\"length\" in value);\n}\n\nfunction isURL(value) {\n  return URL && value instanceof URL;\n}\n\n// Exports\nmodule.exports = wrap({ http: http, https: https });\nmodule.exports.wrap = wrap;\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,MAAM,IAAI,GAAG;AACjB,IAAI;AACJ,IAAI;AACJ,IAAI,WAAW,uEAAkB,QAAQ;AACzC,IAAI;AACJ,IAAI;AAEJ,gCAAgC;AAChC,uBAAuB;AACtB,CAAA,SAAS;IACR,IAAI,gBAAgB,OAAO,YAAY;IACvC,IAAI,mBAAmB,gBAAkB,eAAe,OAAO,aAAa;IAC5E,IAAI,cAAc,WAAW,MAAM,iBAAiB;IACpD,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,WAAW,GAAG;QACxD,QAAQ,IAAI,CAAC;IACf;AACF,CAAA;AAEA,gEAAgE;AAChE,IAAI,eAAe;AACnB,IAAI;IACF,OAAO,IAAI,IAAI;AACjB,EACA,OAAO,OAAO;IACZ,eAAe,MAAM,IAAI,KAAK;AAChC;AAEA,4CAA4C;AAC5C,IAAI,qBAAqB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,wDAAwD;AACxD,IAAI,SAAS;IAAC;IAAS;IAAW;IAAW;IAAS;IAAU;CAAU;AAC1E,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAClC,OAAO,OAAO,CAAC,SAAU,KAAK;IAC5B,aAAa,CAAC,MAAM,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,IAAI;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,MAAM,MAAM;IAC7C;AACF;AAEA,yBAAyB;AACzB,IAAI,kBAAkB,gBACpB,mBACA,eACA;AAEF,IAAI,mBAAmB,gBACrB,8BACA;AAEF,IAAI,wBAAwB,gBAC1B,6BACA,wCACA;AAEF,IAAI,6BAA6B,gBAC/B,mCACA;AAEF,IAAI,qBAAqB,gBACvB,8BACA;AAGF,uBAAuB;AACvB,IAAI,UAAU,SAAS,SAAS,CAAC,OAAO,IAAI;AAE5C,4CAA4C;AAC5C,SAAS,oBAAoB,OAAO,EAAE,gBAAgB;IACpD,yBAAyB;IACzB,SAAS,IAAI,CAAC,IAAI;IAClB,IAAI,CAAC,gBAAgB,CAAC;IACtB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,mBAAmB,GAAG,EAAE;IAE7B,8BAA8B;IAC9B,IAAI,kBAAkB;QACpB,IAAI,CAAC,EAAE,CAAC,YAAY;IACtB;IAEA,wCAAwC;IACxC,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,iBAAiB,GAAG,SAAU,QAAQ;QACzC,IAAI;YACF,KAAK,gBAAgB,CAAC;QACxB,EACA,OAAO,OAAO;YACZ,KAAK,IAAI,CAAC,SAAS,iBAAiB,mBAClC,QAAQ,IAAI,iBAAiB;gBAAE,OAAO;YAAM;QAChD;IACF;IAEA,4BAA4B;IAC5B,IAAI,CAAC,eAAe;AACtB;AACA,oBAAoB,SAAS,GAAG,OAAO,MAAM,CAAC,SAAS,SAAS;AAEhE,oBAAoB,SAAS,CAAC,KAAK,GAAG;IACpC,eAAe,IAAI,CAAC,eAAe;IACnC,IAAI,CAAC,eAAe,CAAC,KAAK;IAC1B,IAAI,CAAC,IAAI,CAAC;AACZ;AAEA,oBAAoB,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK;IACrD,eAAe,IAAI,CAAC,eAAe,EAAE;IACrC,QAAQ,IAAI,CAAC,IAAI,EAAE;IACnB,OAAO,IAAI;AACb;AAEA,qDAAqD;AACrD,oBAAoB,SAAS,CAAC,KAAK,GAAG,SAAU,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACtE,gDAAgD;IAChD,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,MAAM,IAAI;IACZ;IAEA,mDAAmD;IACnD,IAAI,CAAC,SAAS,SAAS,CAAC,SAAS,OAAO;QACtC,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,WAAW,WAAW;QACxB,WAAW;QACX,WAAW;IACb;IAEA,uEAAuE;IACvE,8CAA8C;IAC9C,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,IAAI,UAAU;YACZ;QACF;QACA;IACF;IACA,0DAA0D;IAC1D,IAAI,IAAI,CAAC,kBAAkB,GAAG,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;QACxE,IAAI,CAAC,kBAAkB,IAAI,KAAK,MAAM;QACtC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAAE,MAAM;YAAM,UAAU;QAAS;QAC/D,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,UAAU;IAC7C,OAEK;QACH,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI;QACvB,IAAI,CAAC,KAAK;IACZ;AACF;AAEA,kCAAkC;AAClC,oBAAoB,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACpE,gCAAgC;IAChC,IAAI,WAAW,OAAO;QACpB,WAAW;QACX,OAAO,WAAW;IACpB,OACK,IAAI,WAAW,WAAW;QAC7B,WAAW;QACX,WAAW;IACb;IAEA,+BAA+B;IAC/B,IAAI,CAAC,MAAM;QACT,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG;QAC7B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,MAAM;IACvC,OACK;QACH,IAAI,OAAO,IAAI;QACf,IAAI,iBAAiB,IAAI,CAAC,eAAe;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,UAAU;YACzB,KAAK,MAAM,GAAG;YACd,eAAe,GAAG,CAAC,MAAM,MAAM;QACjC;QACA,IAAI,CAAC,OAAO,GAAG;IACjB;AACF;AAEA,oDAAoD;AACpD,oBAAoB,SAAS,CAAC,SAAS,GAAG,SAAU,IAAI,EAAE,KAAK;IAC7D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG;IAC9B,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM;AACvC;AAEA,sDAAsD;AACtD,oBAAoB,SAAS,CAAC,YAAY,GAAG,SAAU,IAAI;IACzD,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;IAClC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;AACpC;AAEA,6CAA6C;AAC7C,oBAAoB,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ;IAClE,IAAI,OAAO,IAAI;IAEf,iCAAiC;IACjC,SAAS,iBAAiB,MAAM;QAC9B,OAAO,UAAU,CAAC;QAClB,OAAO,cAAc,CAAC,WAAW,OAAO,OAAO;QAC/C,OAAO,WAAW,CAAC,WAAW,OAAO,OAAO;IAC9C;IAEA,6CAA6C;IAC7C,SAAS,WAAW,MAAM;QACxB,IAAI,KAAK,QAAQ,EAAE;YACjB,aAAa,KAAK,QAAQ;QAC5B;QACA,KAAK,QAAQ,GAAG,WAAW;YACzB,KAAK,IAAI,CAAC;YACV;QACF,GAAG;QACH,iBAAiB;IACnB;IAEA,kCAAkC;IAClC,SAAS;QACP,oBAAoB;QACpB,IAAI,KAAK,QAAQ,EAAE;YACjB,aAAa,KAAK,QAAQ;YAC1B,KAAK,QAAQ,GAAG;QAClB;QAEA,kCAAkC;QAClC,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,YAAY;QAChC,KAAK,cAAc,CAAC,SAAS;QAC7B,IAAI,UAAU;YACZ,KAAK,cAAc,CAAC,WAAW;QACjC;QACA,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,KAAK,eAAe,CAAC,cAAc,CAAC,UAAU;QAChD;IACF;IAEA,4BAA4B;IAC5B,IAAI,UAAU;QACZ,IAAI,CAAC,EAAE,CAAC,WAAW;IACrB;IAEA,kDAAkD;IAClD,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,WAAW,IAAI,CAAC,MAAM;IACxB,OACK;QACH,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU;IACtC;IAEA,qBAAqB;IACrB,IAAI,CAAC,EAAE,CAAC,UAAU;IAClB,IAAI,CAAC,EAAE,CAAC,SAAS;IACjB,IAAI,CAAC,EAAE,CAAC,SAAS;IACjB,IAAI,CAAC,EAAE,CAAC,YAAY;IACpB,IAAI,CAAC,EAAE,CAAC,SAAS;IAEjB,OAAO,IAAI;AACb;AAEA,+CAA+C;AAC/C;IACE;IAAgB;IAChB;IAAc;CACf,CAAC,OAAO,CAAC,SAAU,MAAM;IACxB,oBAAoB,SAAS,CAAC,OAAO,GAAG,SAAU,CAAC,EAAE,CAAC;QACpD,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG;IACzC;AACF;AAEA,4CAA4C;AAC5C;IAAC;IAAW;IAAc;CAAS,CAAC,OAAO,CAAC,SAAU,QAAQ;IAC5D,OAAO,cAAc,CAAC,oBAAoB,SAAS,EAAE,UAAU;QAC7D,KAAK;YAAc,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS;QAAE;IAC5D;AACF;AAEA,oBAAoB,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO;IAChE,oCAAoC;IACpC,IAAI,CAAC,QAAQ,OAAO,EAAE;QACpB,QAAQ,OAAO,GAAG,CAAC;IACrB;IAEA,0DAA0D;IAC1D,4DAA4D;IAC5D,kDAAkD;IAClD,IAAI,QAAQ,IAAI,EAAE;QAChB,iDAAiD;QACjD,IAAI,CAAC,QAAQ,QAAQ,EAAE;YACrB,QAAQ,QAAQ,GAAG,QAAQ,IAAI;QACjC;QACA,OAAO,QAAQ,IAAI;IACrB;IAEA,yCAAyC;IACzC,IAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,IAAI,EAAE;QACrC,IAAI,YAAY,QAAQ,IAAI,CAAC,OAAO,CAAC;QACrC,IAAI,YAAY,GAAG;YACjB,QAAQ,QAAQ,GAAG,QAAQ,IAAI;QACjC,OACK;YACH,QAAQ,QAAQ,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG;YAC7C,QAAQ,MAAM,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC;QAC1C;IACF;AACF;AAGA,yDAAyD;AACzD,oBAAoB,SAAS,CAAC,eAAe,GAAG;IAC9C,2BAA2B;IAC3B,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,QAAQ;IACrC,IAAI,iBAAiB,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS;IAC5D,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,UAAU,0BAA0B;IAChD;IAEA,4DAA4D;IAC5D,iDAAiD;IACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACxB,IAAI,SAAS,SAAS,KAAK,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO;IACpD;IAEA,0DAA0D;IAC1D,IAAI,UAAU,IAAI,CAAC,eAAe,GAC5B,eAAe,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB;IAClE,QAAQ,aAAa,GAAG,IAAI;IAC5B,KAAK,IAAI,SAAS,OAAQ;QACxB,QAAQ,EAAE,CAAC,OAAO,aAAa,CAAC,MAAM;IACxC;IAEA,yEAAyE;IACzE,uEAAuE;IACvE,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAC9C,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,IACxB,wCAAwC;IACxC,0DAA0D;IAC1D,IAAI,CAAC,QAAQ,CAAC,IAAI;IAEpB,2BAA2B;IAC3B,4EAA4E;IAC5E,IAAI,IAAI,CAAC,WAAW,EAAE;QACpB,mCAAmC;QACnC,IAAI,IAAI;QACR,IAAI,OAAO,IAAI;QACf,IAAI,UAAU,IAAI,CAAC,mBAAmB;QACrC,CAAA,SAAS,UAAU,KAAK;YACvB,yDAAyD;YACzD,uBAAuB;YACvB,IAAI,YAAY,KAAK,eAAe,EAAE;gBACpC,0BAA0B;gBAC1B,qBAAqB;gBACrB,IAAI,OAAO;oBACT,KAAK,IAAI,CAAC,SAAS;gBACrB,OAEK,IAAI,IAAI,QAAQ,MAAM,EAAE;oBAC3B,IAAI,SAAS,OAAO,CAAC,IAAI;oBACzB,uBAAuB;oBACvB,IAAI,CAAC,QAAQ,QAAQ,EAAE;wBACrB,QAAQ,KAAK,CAAC,OAAO,IAAI,EAAE,OAAO,QAAQ,EAAE;oBAC9C;gBACF,OAEK,IAAI,KAAK,MAAM,EAAE;oBACpB,QAAQ,GAAG;gBACb;YACF;QACF,CAAA;IACF;AACF;AAEA,uDAAuD;AACvD,oBAAoB,SAAS,CAAC,gBAAgB,GAAG,SAAU,QAAQ;IACjE,gCAAgC;IAChC,IAAI,aAAa,SAAS,UAAU;IACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;QAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnB,KAAK,IAAI,CAAC,WAAW;YACrB,SAAS,SAAS,OAAO;YACzB,YAAY;QACd;IACF;IAEA,oEAAoE;IACpE,sEAAsE;IACtE,+DAA+D;IAC/D,mEAAmE;IACnE,0CAA0C;IAC1C,sDAAsD;IAEtD,qDAAqD;IACrD,IAAI,WAAW,SAAS,OAAO,CAAC,QAAQ;IACxC,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ,CAAC,eAAe,KAAK,SAC/C,aAAa,OAAO,cAAc,KAAK;QACzC,SAAS,WAAW,GAAG,IAAI,CAAC,WAAW;QACvC,SAAS,SAAS,GAAG,IAAI,CAAC,UAAU;QACpC,IAAI,CAAC,IAAI,CAAC,YAAY;QAEtB,WAAW;QACX,IAAI,CAAC,mBAAmB,GAAG,EAAE;QAC7B;IACF;IAEA,2DAA2D;IAC3D,eAAe,IAAI,CAAC,eAAe;IACnC,kEAAkE;IAClE,SAAS,OAAO;IAEhB,oDAAoD;IACpD,iEAAiE;IACjE,IAAI,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;QACtD,MAAM,IAAI;IACZ;IAEA,0CAA0C;IAC1C,IAAI;IACJ,IAAI,iBAAiB,IAAI,CAAC,QAAQ,CAAC,cAAc;IACjD,IAAI,gBAAgB;QAClB,iBAAiB,OAAO,MAAM,CAAC;YAC7B,oDAAoD;YACpD,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC;QAC/B,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO;IAC1B;IAEA,wDAAwD;IACxD,6CAA6C;IAC7C,mEAAmE;IACnE,kEAAkE;IAClE,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;IACjC,IAAI,CAAC,eAAe,OAAO,eAAe,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,UACvE,gEAAgE;IAChE,uEAAuE;IACvE,kEAAkE;IAClE,4CAA4C;IAC3C,eAAe,OAAQ,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;QACxE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;QACvB,mDAAmD;QACnD,IAAI,CAAC,mBAAmB,GAAG,EAAE;QAC7B,sBAAsB,cAAc,IAAI,CAAC,QAAQ,CAAC,OAAO;IAC3D;IAEA,uEAAuE;IACvE,IAAI,oBAAoB,sBAAsB,WAAW,IAAI,CAAC,QAAQ,CAAC,OAAO;IAE9E,uEAAuE;IACvE,IAAI,kBAAkB,SAAS,IAAI,CAAC,WAAW;IAC/C,IAAI,cAAc,qBAAqB,gBAAgB,IAAI;IAC3D,IAAI,aAAa,QAAQ,IAAI,CAAC,YAAY,IAAI,CAAC,WAAW,GACxD,IAAI,MAAM,CAAC,OAAO,MAAM,CAAC,iBAAiB;QAAE,MAAM;IAAY;IAEhE,gCAAgC;IAChC,IAAI,cAAc,WAAW,UAAU;IACvC,MAAM,kBAAkB,YAAY,IAAI;IACxC,IAAI,CAAC,WAAW,GAAG;IACnB,gBAAgB,aAAa,IAAI,CAAC,QAAQ;IAE1C,uEAAuE;IACvE,qDAAqD;IACrD,IAAI,YAAY,QAAQ,KAAK,gBAAgB,QAAQ,IAClD,YAAY,QAAQ,KAAK,YACzB,YAAY,IAAI,KAAK,eACrB,CAAC,YAAY,YAAY,IAAI,EAAE,cAAc;QAC9C,sBAAsB,0CAA0C,IAAI,CAAC,QAAQ,CAAC,OAAO;IACvF;IAEA,uCAAuC;IACvC,IAAI,WAAW,iBAAiB;QAC9B,IAAI,kBAAkB;YACpB,SAAS,SAAS,OAAO;YACzB,YAAY;QACd;QACA,IAAI,iBAAiB;YACnB,KAAK;YACL,QAAQ;YACR,SAAS;QACX;QACA,eAAe,IAAI,CAAC,QAAQ,EAAE,iBAAiB;QAC/C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ;IACrC;IAEA,iCAAiC;IACjC,IAAI,CAAC,eAAe;AACtB;AAEA,sEAAsE;AACtE,SAAS,KAAK,SAAS;IACrB,mBAAmB;IACnB,IAAI,UAAU;QACZ,cAAc;QACd,eAAe,KAAK,OAAO;IAC7B;IAEA,qBAAqB;IACrB,IAAI,kBAAkB,CAAC;IACvB,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,SAAU,MAAM;QAC7C,IAAI,WAAW,SAAS;QACxB,IAAI,iBAAiB,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO;QAClE,IAAI,kBAAkB,OAAO,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC;QAEtD,0CAA0C;QAC1C,SAAS,QAAQ,KAAK,EAAE,OAAO,EAAE,QAAQ;YACvC,qDAAqD;YACrD,IAAI,MAAM,QAAQ;gBAChB,QAAQ,gBAAgB;YAC1B,OACK,IAAI,SAAS,QAAQ;gBACxB,QAAQ,gBAAgB,SAAS;YACnC,OACK;gBACH,WAAW;gBACX,UAAU,YAAY;gBACtB,QAAQ;oBAAE,UAAU;gBAAS;YAC/B;YACA,IAAI,WAAW,UAAU;gBACvB,WAAW;gBACX,UAAU;YACZ;YAEA,eAAe;YACf,UAAU,OAAO,MAAM,CAAC;gBACtB,cAAc,QAAQ,YAAY;gBAClC,eAAe,QAAQ,aAAa;YACtC,GAAG,OAAO;YACV,QAAQ,eAAe,GAAG;YAC1B,IAAI,CAAC,SAAS,QAAQ,IAAI,KAAK,CAAC,SAAS,QAAQ,QAAQ,GAAG;gBAC1D,QAAQ,QAAQ,GAAG;YACrB;YAEA,OAAO,KAAK,CAAC,QAAQ,QAAQ,EAAE,UAAU;YACzC,MAAM,WAAW;YACjB,OAAO,IAAI,oBAAoB,SAAS;QAC1C;QAEA,8CAA8C;QAC9C,SAAS,IAAI,KAAK,EAAE,OAAO,EAAE,QAAQ;YACnC,IAAI,iBAAiB,gBAAgB,OAAO,CAAC,OAAO,SAAS;YAC7D,eAAe,GAAG;YAClB,OAAO;QACT;QAEA,gDAAgD;QAChD,OAAO,gBAAgB,CAAC,iBAAiB;YACvC,SAAS;gBAAE,OAAO;gBAAS,cAAc;gBAAM,YAAY;gBAAM,UAAU;YAAK;YAChF,KAAK;gBAAE,OAAO;gBAAK,cAAc;gBAAM,YAAY;gBAAM,UAAU;YAAK;QAC1E;IACF;IACA,OAAO;AACT;AAEA,SAAS,QAAqB;AAE9B,SAAS,SAAS,KAAK;IACrB,IAAI;IACJ,uBAAuB;IACvB,IAAI,cAAc;QAChB,SAAS,IAAI,IAAI;IACnB,OACK;QACH,uCAAuC;QACvC,SAAS,YAAY,IAAI,KAAK,CAAC;QAC/B,IAAI,CAAC,SAAS,OAAO,QAAQ,GAAG;YAC9B,MAAM,IAAI,gBAAgB;gBAAE;YAAM;QACpC;IACF;IACA,OAAO;AACT;AAEA,SAAS,WAAW,QAAQ,EAAE,IAAI;IAChC,uBAAuB;IACvB,OAAO,eAAe,IAAI,IAAI,UAAU,QAAQ,SAAS,IAAI,OAAO,CAAC,MAAM;AAC7E;AAEA,SAAS,YAAY,KAAK;IACxB,IAAI,MAAM,IAAI,CAAC,MAAM,QAAQ,KAAK,CAAC,oBAAoB,IAAI,CAAC,MAAM,QAAQ,GAAG;QAC3E,MAAM,IAAI,gBAAgB;YAAE,OAAO,MAAM,IAAI,IAAI;QAAM;IACzD;IACA,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,MAAM,IAAI,GAAG;QAC1E,MAAM,IAAI,gBAAgB;YAAE,OAAO,MAAM,IAAI,IAAI;QAAM;IACzD;IACA,OAAO;AACT;AAEA,SAAS,gBAAgB,SAAS,EAAE,MAAM;IACxC,IAAI,SAAS,UAAU,CAAC;IACxB,KAAK,IAAI,OAAO,mBAAoB;QAClC,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;IAC9B;IAEA,oBAAoB;IACpB,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM;QACnC,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;IAC9C;IACA,0BAA0B;IAC1B,IAAI,OAAO,IAAI,KAAK,IAAI;QACtB,OAAO,IAAI,GAAG,OAAO,OAAO,IAAI;IAClC;IACA,mBAAmB;IACnB,OAAO,IAAI,GAAG,OAAO,MAAM,GAAG,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,OAAO,QAAQ;IAE/E,OAAO;AACT;AAEA,SAAS,sBAAsB,KAAK,EAAE,OAAO;IAC3C,IAAI;IACJ,IAAK,IAAI,UAAU,QAAS;QAC1B,IAAI,MAAM,IAAI,CAAC,SAAS;YACtB,YAAY,OAAO,CAAC,OAAO;YAC3B,OAAO,OAAO,CAAC,OAAO;QACxB;IACF;IACA,OAAO,AAAC,cAAc,QAAQ,OAAO,cAAc,cACjD,YAAY,OAAO,WAAW,IAAI;AACtC;AAEA,SAAS,gBAAgB,IAAI,EAAE,OAAO,EAAE,SAAS;IAC/C,qBAAqB;IACrB,SAAS,YAAY,UAAU;QAC7B,uBAAuB;QACvB,IAAI,WAAW,MAAM,iBAAiB,GAAG;YACvC,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD;QACA,OAAO,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;IACpE;IAEA,gDAAgD;IAChD,YAAY,SAAS,GAAG,IAAI,CAAC,aAAa,KAAK;IAC/C,OAAO,gBAAgB,CAAC,YAAY,SAAS,EAAE;QAC7C,aAAa;YACX,OAAO;YACP,YAAY;QACd;QACA,MAAM;YACJ,OAAO,YAAY,OAAO;YAC1B,YAAY;QACd;IACF;IACA,OAAO;AACT;AAEA,SAAS,eAAe,OAAO,EAAE,KAAK;IACpC,KAAK,IAAI,SAAS,OAAQ;QACxB,QAAQ,cAAc,CAAC,OAAO,aAAa,CAAC,MAAM;IACpD;IACA,QAAQ,EAAE,CAAC,SAAS;IACpB,QAAQ,OAAO,CAAC;AAClB;AAEA,SAAS,YAAY,SAAS,EAAE,MAAM;IACpC,OAAO,SAAS,cAAc,SAAS;IACvC,IAAI,MAAM,UAAU,MAAM,GAAG,OAAO,MAAM,GAAG;IAC7C,OAAO,MAAM,KAAK,SAAS,CAAC,IAAI,KAAK,OAAO,UAAU,QAAQ,CAAC;AACjE;AAEA,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,UAAU,YAAY,iBAAiB;AACvD;AAEA,SAAS,WAAW,KAAK;IACvB,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,SAAS,KAAK;IACrB,OAAO,OAAO,UAAU,YAAa,YAAY;AACnD;AAEA,SAAS,MAAM,KAAK;IAClB,OAAO,OAAO,iBAAiB;AACjC;AAEA,UAAU;AACV,OAAO,OAAO,GAAG,KAAK;IAAE,MAAM;IAAM,OAAO;AAAM;AACjD,OAAO,OAAO,CAAC,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/env/data.js"], "sourcesContent": ["export const VERSION = \"1.11.0\";"], "names": [], "mappings": ";;;;AAAO,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/parseProtocol.js"], "sourcesContent": ["'use strict';\n\nexport default function parseProtocol(url) {\n  const match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEe,SAAS,cAAc,GAAG;IACvC,MAAM,QAAQ,4BAA4B,IAAI,CAAC;IAC/C,OAAO,SAAS,KAAK,CAAC,EAAE,IAAI;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/fromDataURI.js"], "sourcesContent": ["'use strict';\n\nimport AxiosError from '../core/AxiosError.js';\nimport parseProtocol from './parseProtocol.js';\nimport platform from '../platform/index.js';\n\nconst DATA_URL_PATTERN = /^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\\s\\S]*)$/;\n\n/**\n * Parse data uri to a Buffer or Blob\n *\n * @param {String} uri\n * @param {?Boolean} asBlob\n * @param {?Object} options\n * @param {?Function} options.Blob\n *\n * @returns {Buffer|Blob}\n */\nexport default function fromDataURI(uri, asBlob, options) {\n  const _Blob = options && options.Blob || platform.classes.Blob;\n  const protocol = parseProtocol(uri);\n\n  if (asBlob === undefined && _Blob) {\n    asBlob = true;\n  }\n\n  if (protocol === 'data') {\n    uri = protocol.length ? uri.slice(protocol.length + 1) : uri;\n\n    const match = DATA_URL_PATTERN.exec(uri);\n\n    if (!match) {\n      throw new AxiosError('Invalid URL', AxiosError.ERR_INVALID_URL);\n    }\n\n    const mime = match[1];\n    const isBase64 = match[2];\n    const body = match[3];\n    const buffer = Buffer.from(decodeURIComponent(body), isBase64 ? 'base64' : 'utf8');\n\n    if (asBlob) {\n      if (!_Blob) {\n        throw new AxiosError('Blob is not supported', AxiosError.ERR_NOT_SUPPORT);\n      }\n\n      return new _Blob([buffer], {type: mime});\n    }\n\n    return buffer;\n  }\n\n  throw new AxiosError('Unsupported protocol ' + protocol, AxiosError.ERR_NOT_SUPPORT);\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;AAMA,MAAM,mBAAmB;AAYV,SAAS,YAAY,GAAG,EAAE,MAAM,EAAE,OAAO;IACtD,MAAM,QAAQ,WAAW,QAAQ,IAAI,IAAI,4JAAQ,CAAC,OAAO,CAAC,IAAI;IAC9D,MAAM,WAAW,IAAA,mKAAa,EAAC;IAE/B,IAAI,WAAW,aAAa,OAAO;QACjC,SAAS;IACX;IAEA,IAAI,aAAa,QAAQ;QACvB,MAAM,SAAS,MAAM,GAAG,IAAI,KAAK,CAAC,SAAS,MAAM,GAAG,KAAK;QAEzD,MAAM,QAAQ,iBAAiB,IAAI,CAAC;QAEpC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,6JAAU,CAAC,eAAe,6JAAU,CAAC,eAAe;QAChE;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,WAAW,KAAK,CAAC,EAAE;QACzB,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,SAAS,OAAO,IAAI,CAAC,mBAAmB,OAAO,WAAW,WAAW;QAE3E,IAAI,QAAQ;YACV,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,6JAAU,CAAC,yBAAyB,6JAAU,CAAC,eAAe;YAC1E;YAEA,OAAO,IAAI,MAAM;gBAAC;aAAO,EAAE;gBAAC,MAAM;YAAI;QACxC;QAEA,OAAO;IACT;IAEA,MAAM,IAAI,6JAAU,CAAC,0BAA0B,UAAU,6JAAU,CAAC,eAAe;AACrF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/AxiosTransformStream.js"], "sourcesContent": ["'use strict';\n\nimport stream from 'stream';\nimport utils from '../utils.js';\n\nconst kInternals = Symbol('internals');\n\nclass AxiosTransformStream extends stream.Transform{\n  constructor(options) {\n    options = utils.toFlatObject(options, {\n      maxRate: 0,\n      chunkSize: 64 * 1024,\n      minChunkSize: 100,\n      timeWindow: 500,\n      ticksRate: 2,\n      samplesCount: 15\n    }, null, (prop, source) => {\n      return !utils.isUndefined(source[prop]);\n    });\n\n    super({\n      readableHighWaterMark: options.chunkSize\n    });\n\n    const internals = this[kInternals] = {\n      timeWindow: options.timeWindow,\n      chunkSize: options.chunkSize,\n      maxRate: options.maxRate,\n      minChunkSize: options.minChunkSize,\n      bytesSeen: 0,\n      isCaptured: false,\n      notifiedBytesLoaded: 0,\n      ts: Date.now(),\n      bytes: 0,\n      onReadCallback: null\n    };\n\n    this.on('newListener', event => {\n      if (event === 'progress') {\n        if (!internals.isCaptured) {\n          internals.isCaptured = true;\n        }\n      }\n    });\n  }\n\n  _read(size) {\n    const internals = this[kInternals];\n\n    if (internals.onReadCallback) {\n      internals.onReadCallback();\n    }\n\n    return super._read(size);\n  }\n\n  _transform(chunk, encoding, callback) {\n    const internals = this[kInternals];\n    const maxRate = internals.maxRate;\n\n    const readableHighWaterMark = this.readableHighWaterMark;\n\n    const timeWindow = internals.timeWindow;\n\n    const divider = 1000 / timeWindow;\n    const bytesThreshold = (maxRate / divider);\n    const minChunkSize = internals.minChunkSize !== false ? Math.max(internals.minChunkSize, bytesThreshold * 0.01) : 0;\n\n    const pushChunk = (_chunk, _callback) => {\n      const bytes = Buffer.byteLength(_chunk);\n      internals.bytesSeen += bytes;\n      internals.bytes += bytes;\n\n      internals.isCaptured && this.emit('progress', internals.bytesSeen);\n\n      if (this.push(_chunk)) {\n        process.nextTick(_callback);\n      } else {\n        internals.onReadCallback = () => {\n          internals.onReadCallback = null;\n          process.nextTick(_callback);\n        };\n      }\n    }\n\n    const transformChunk = (_chunk, _callback) => {\n      const chunkSize = Buffer.byteLength(_chunk);\n      let chunkRemainder = null;\n      let maxChunkSize = readableHighWaterMark;\n      let bytesLeft;\n      let passed = 0;\n\n      if (maxRate) {\n        const now = Date.now();\n\n        if (!internals.ts || (passed = (now - internals.ts)) >= timeWindow) {\n          internals.ts = now;\n          bytesLeft = bytesThreshold - internals.bytes;\n          internals.bytes = bytesLeft < 0 ? -bytesLeft : 0;\n          passed = 0;\n        }\n\n        bytesLeft = bytesThreshold - internals.bytes;\n      }\n\n      if (maxRate) {\n        if (bytesLeft <= 0) {\n          // next time window\n          return setTimeout(() => {\n            _callback(null, _chunk);\n          }, timeWindow - passed);\n        }\n\n        if (bytesLeft < maxChunkSize) {\n          maxChunkSize = bytesLeft;\n        }\n      }\n\n      if (maxChunkSize && chunkSize > maxChunkSize && (chunkSize - maxChunkSize) > minChunkSize) {\n        chunkRemainder = _chunk.subarray(maxChunkSize);\n        _chunk = _chunk.subarray(0, maxChunkSize);\n      }\n\n      pushChunk(_chunk, chunkRemainder ? () => {\n        process.nextTick(_callback, null, chunkRemainder);\n      } : _callback);\n    };\n\n    transformChunk(chunk, function transformNextChunk(err, _chunk) {\n      if (err) {\n        return callback(err);\n      }\n\n      if (_chunk) {\n        transformChunk(_chunk, transformNextChunk);\n      } else {\n        callback(null);\n      }\n    });\n  }\n}\n\nexport default AxiosTransformStream;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAKA,MAAM,aAAa,OAAO;AAE1B,MAAM,6BAA6B,gHAAM,CAAC,SAAS;IACjD,YAAY,OAAO,CAAE;QACnB,UAAU,gJAAK,CAAC,YAAY,CAAC,SAAS;YACpC,SAAS;YACT,WAAW,KAAK;YAChB,cAAc;YACd,YAAY;YACZ,WAAW;YACX,cAAc;QAChB,GAAG,MAAM,CAAC,MAAM;YACd,OAAO,CAAC,gJAAK,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK;QACxC;QAEA,KAAK,CAAC;YACJ,uBAAuB,QAAQ,SAAS;QAC1C;QAEA,MAAM,YAAY,IAAI,CAAC,WAAW,GAAG;YACnC,YAAY,QAAQ,UAAU;YAC9B,WAAW,QAAQ,SAAS;YAC5B,SAAS,QAAQ,OAAO;YACxB,cAAc,QAAQ,YAAY;YAClC,WAAW;YACX,YAAY;YACZ,qBAAqB;YACrB,IAAI,KAAK,GAAG;YACZ,OAAO;YACP,gBAAgB;QAClB;QAEA,IAAI,CAAC,EAAE,CAAC,eAAe,CAAA;YACrB,IAAI,UAAU,YAAY;gBACxB,IAAI,CAAC,UAAU,UAAU,EAAE;oBACzB,UAAU,UAAU,GAAG;gBACzB;YACF;QACF;IACF;IAEA,MAAM,IAAI,EAAE;QACV,MAAM,YAAY,IAAI,CAAC,WAAW;QAElC,IAAI,UAAU,cAAc,EAAE;YAC5B,UAAU,cAAc;QAC1B;QAEA,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,WAAW,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;QACpC,MAAM,YAAY,IAAI,CAAC,WAAW;QAClC,MAAM,UAAU,UAAU,OAAO;QAEjC,MAAM,wBAAwB,IAAI,CAAC,qBAAqB;QAExD,MAAM,aAAa,UAAU,UAAU;QAEvC,MAAM,UAAU,OAAO;QACvB,MAAM,iBAAkB,UAAU;QAClC,MAAM,eAAe,UAAU,YAAY,KAAK,QAAQ,KAAK,GAAG,CAAC,UAAU,YAAY,EAAE,iBAAiB,QAAQ;QAElH,MAAM,YAAY,CAAC,QAAQ;YACzB,MAAM,QAAQ,OAAO,UAAU,CAAC;YAChC,UAAU,SAAS,IAAI;YACvB,UAAU,KAAK,IAAI;YAEnB,UAAU,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,UAAU,SAAS;YAEjE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS;gBACrB,QAAQ,QAAQ,CAAC;YACnB,OAAO;gBACL,UAAU,cAAc,GAAG;oBACzB,UAAU,cAAc,GAAG;oBAC3B,QAAQ,QAAQ,CAAC;gBACnB;YACF;QACF;QAEA,MAAM,iBAAiB,CAAC,QAAQ;YAC9B,MAAM,YAAY,OAAO,UAAU,CAAC;YACpC,IAAI,iBAAiB;YACrB,IAAI,eAAe;YACnB,IAAI;YACJ,IAAI,SAAS;YAEb,IAAI,SAAS;gBACX,MAAM,MAAM,KAAK,GAAG;gBAEpB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAU,MAAM,UAAU,EAAE,AAAC,KAAK,YAAY;oBAClE,UAAU,EAAE,GAAG;oBACf,YAAY,iBAAiB,UAAU,KAAK;oBAC5C,UAAU,KAAK,GAAG,YAAY,IAAI,CAAC,YAAY;oBAC/C,SAAS;gBACX;gBAEA,YAAY,iBAAiB,UAAU,KAAK;YAC9C;YAEA,IAAI,SAAS;gBACX,IAAI,aAAa,GAAG;oBAClB,mBAAmB;oBACnB,OAAO,WAAW;wBAChB,UAAU,MAAM;oBAClB,GAAG,aAAa;gBAClB;gBAEA,IAAI,YAAY,cAAc;oBAC5B,eAAe;gBACjB;YACF;YAEA,IAAI,gBAAgB,YAAY,gBAAgB,AAAC,YAAY,eAAgB,cAAc;gBACzF,iBAAiB,OAAO,QAAQ,CAAC;gBACjC,SAAS,OAAO,QAAQ,CAAC,GAAG;YAC9B;YAEA,UAAU,QAAQ,iBAAiB;gBACjC,QAAQ,QAAQ,CAAC,WAAW,MAAM;YACpC,IAAI;QACN;QAEA,eAAe,OAAO,SAAS,mBAAmB,GAAG,EAAE,MAAM;YAC3D,IAAI,KAAK;gBACP,OAAO,SAAS;YAClB;YAEA,IAAI,QAAQ;gBACV,eAAe,QAAQ;YACzB,OAAO;gBACL,SAAS;YACX;QACF;IACF;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/readBlob.js"], "sourcesContent": ["const {asyncIterator} = Symbol;\n\nconst readBlob = async function* (blob) {\n  if (blob.stream) {\n    yield* blob.stream()\n  } else if (blob.arrayBuffer) {\n    yield await blob.arrayBuffer()\n  } else if (blob[asyncIterator]) {\n    yield* blob[asyncIterator]();\n  } else {\n    yield blob;\n  }\n}\n\nexport default readBlob;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,EAAC,aAAa,EAAC,GAAG;AAExB,MAAM,WAAW,gBAAiB,IAAI;IACpC,IAAI,KAAK,MAAM,EAAE;QACf,OAAO,KAAK,MAAM;IACpB,OAAO,IAAI,KAAK,WAAW,EAAE;QAC3B,MAAM,MAAM,KAAK,WAAW;IAC9B,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;QAC9B,OAAO,IAAI,CAAC,cAAc;IAC5B,OAAO;QACL,MAAM;IACR;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/formDataToStream.js"], "sourcesContent": ["import util from 'util';\nimport {Readable} from 'stream';\nimport utils from \"../utils.js\";\nimport readBlob from \"./readBlob.js\";\nimport platform from \"../platform/index.js\";\n\nconst BOUNDARY_ALPHABET = platform.ALPHABET.ALPHA_DIGIT + '-_';\n\nconst textEncoder = typeof TextEncoder === 'function' ? new TextEncoder() : new util.TextEncoder();\n\nconst CRLF = '\\r\\n';\nconst CRLF_BYTES = textEncoder.encode(CRLF);\nconst CRLF_BYTES_COUNT = 2;\n\nclass FormDataPart {\n  constructor(name, value) {\n    const {escapeName} = this.constructor;\n    const isStringValue = utils.isString(value);\n\n    let headers = `Content-Disposition: form-data; name=\"${escapeName(name)}\"${\n      !isStringValue && value.name ? `; filename=\"${escapeName(value.name)}\"` : ''\n    }${CRLF}`;\n\n    if (isStringValue) {\n      value = textEncoder.encode(String(value).replace(/\\r?\\n|\\r\\n?/g, CRLF));\n    } else {\n      headers += `Content-Type: ${value.type || \"application/octet-stream\"}${CRLF}`\n    }\n\n    this.headers = textEncoder.encode(headers + CRLF);\n\n    this.contentLength = isStringValue ? value.byteLength : value.size;\n\n    this.size = this.headers.byteLength + this.contentLength + CRLF_BYTES_COUNT;\n\n    this.name = name;\n    this.value = value;\n  }\n\n  async *encode(){\n    yield this.headers;\n\n    const {value} = this;\n\n    if(utils.isTypedArray(value)) {\n      yield value;\n    } else {\n      yield* readBlob(value);\n    }\n\n    yield CRLF_BYTES;\n  }\n\n  static escapeName(name) {\n      return String(name).replace(/[\\r\\n\"]/g, (match) => ({\n        '\\r' : '%0D',\n        '\\n' : '%0A',\n        '\"' : '%22',\n      }[match]));\n  }\n}\n\nconst formDataToStream = (form, headersHandler, options) => {\n  const {\n    tag = 'form-data-boundary',\n    size = 25,\n    boundary = tag + '-' + platform.generateString(size, BOUNDARY_ALPHABET)\n  } = options || {};\n\n  if(!utils.isFormData(form)) {\n    throw TypeError('FormData instance required');\n  }\n\n  if (boundary.length < 1 || boundary.length > 70) {\n    throw Error('boundary must be 10-70 characters long')\n  }\n\n  const boundaryBytes = textEncoder.encode('--' + boundary + CRLF);\n  const footerBytes = textEncoder.encode('--' + boundary + '--' + CRLF);\n  let contentLength = footerBytes.byteLength;\n\n  const parts = Array.from(form.entries()).map(([name, value]) => {\n    const part = new FormDataPart(name, value);\n    contentLength += part.size;\n    return part;\n  });\n\n  contentLength += boundaryBytes.byteLength * parts.length;\n\n  contentLength = utils.toFiniteNumber(contentLength);\n\n  const computedHeaders = {\n    'Content-Type': `multipart/form-data; boundary=${boundary}`\n  }\n\n  if (Number.isFinite(contentLength)) {\n    computedHeaders['Content-Length'] = contentLength;\n  }\n\n  headersHandler && headersHandler(computedHeaders);\n\n  return Readable.from((async function *() {\n    for(const part of parts) {\n      yield boundaryBytes;\n      yield* part.encode();\n    }\n\n    yield footerBytes;\n  })());\n};\n\nexport default formDataToStream;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,oBAAoB,4JAAQ,CAAC,QAAQ,CAAC,WAAW,GAAG;AAE1D,MAAM,cAAc,OAAO,gBAAgB,aAAa,IAAI,gBAAgB,IAAI,4GAAI,CAAC,WAAW;AAEhG,MAAM,OAAO;AACb,MAAM,aAAa,YAAY,MAAM,CAAC;AACtC,MAAM,mBAAmB;AAEzB,MAAM;IACJ,YAAY,IAAI,EAAE,KAAK,CAAE;QACvB,MAAM,EAAC,UAAU,EAAC,GAAG,IAAI,CAAC,WAAW;QACrC,MAAM,gBAAgB,gJAAK,CAAC,QAAQ,CAAC;QAErC,IAAI,UAAU,CAAC,sCAAsC,EAAE,WAAW,MAAM,CAAC,EACvE,CAAC,iBAAiB,MAAM,IAAI,GAAG,CAAC,YAAY,EAAE,WAAW,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,KACzE,MAAM;QAET,IAAI,eAAe;YACjB,QAAQ,YAAY,MAAM,CAAC,OAAO,OAAO,OAAO,CAAC,gBAAgB;QACnE,OAAO;YACL,WAAW,CAAC,cAAc,EAAE,MAAM,IAAI,IAAI,6BAA6B,MAAM;QAC/E;QAEA,IAAI,CAAC,OAAO,GAAG,YAAY,MAAM,CAAC,UAAU;QAE5C,IAAI,CAAC,aAAa,GAAG,gBAAgB,MAAM,UAAU,GAAG,MAAM,IAAI;QAElE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,GAAG;QAE3D,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,OAAO,SAAQ;QACb,MAAM,IAAI,CAAC,OAAO;QAElB,MAAM,EAAC,KAAK,EAAC,GAAG,IAAI;QAEpB,IAAG,gJAAK,CAAC,YAAY,CAAC,QAAQ;YAC5B,MAAM;QACR,OAAO;YACL,OAAO,IAAA,8JAAQ,EAAC;QAClB;QAEA,MAAM;IACR;IAEA,OAAO,WAAW,IAAI,EAAE;QACpB,OAAO,OAAO,MAAM,OAAO,CAAC,YAAY,CAAC,QAAW,CAAA;gBAClD,MAAO;gBACP,MAAO;gBACP,KAAM;YACR,CAAA,CAAC,CAAC,MAAM;IACZ;AACF;AAEA,MAAM,mBAAmB,CAAC,MAAM,gBAAgB;IAC9C,MAAM,EACJ,MAAM,oBAAoB,EAC1B,OAAO,EAAE,EACT,WAAW,MAAM,MAAM,4JAAQ,CAAC,cAAc,CAAC,MAAM,kBAAkB,EACxE,GAAG,WAAW,CAAC;IAEhB,IAAG,CAAC,gJAAK,CAAC,UAAU,CAAC,OAAO;QAC1B,MAAM,UAAU;IAClB;IAEA,IAAI,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,IAAI;QAC/C,MAAM,MAAM;IACd;IAEA,MAAM,gBAAgB,YAAY,MAAM,CAAC,OAAO,WAAW;IAC3D,MAAM,cAAc,YAAY,MAAM,CAAC,OAAO,WAAW,OAAO;IAChE,IAAI,gBAAgB,YAAY,UAAU;IAE1C,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM;QACzD,MAAM,OAAO,IAAI,aAAa,MAAM;QACpC,iBAAiB,KAAK,IAAI;QAC1B,OAAO;IACT;IAEA,iBAAiB,cAAc,UAAU,GAAG,MAAM,MAAM;IAExD,gBAAgB,gJAAK,CAAC,cAAc,CAAC;IAErC,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,8BAA8B,EAAE,UAAU;IAC7D;IAEA,IAAI,OAAO,QAAQ,CAAC,gBAAgB;QAClC,eAAe,CAAC,iBAAiB,GAAG;IACtC;IAEA,kBAAkB,eAAe;IAEjC,OAAO,iHAAQ,CAAC,IAAI,CAAC,AAAC;QACpB,KAAI,MAAM,QAAQ,MAAO;YACvB,MAAM;YACN,OAAO,KAAK,MAAM;QACpB;QAEA,MAAM;IACR;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/ZlibHeaderTransformStream.js"], "sourcesContent": ["\"use strict\";\n\nimport stream from \"stream\";\n\nclass ZlibHeaderTransformStream extends stream.Transform {\n  __transform(chunk, encoding, callback) {\n    this.push(chunk);\n    callback();\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk.length !== 0) {\n      this._transform = this.__transform;\n\n      // Add Default Compression headers if no zlib headers are present\n      if (chunk[0] !== 120) { // Hex: 78\n        const header = Buffer.alloc(2);\n        header[0] = 120; // Hex: 78\n        header[1] = 156; // Hex: 9C \n        this.push(header, encoding);\n      }\n    }\n\n    this.__transform(chunk, encoding, callback);\n  }\n}\n\nexport default ZlibHeaderTransformStream;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA,MAAM,kCAAkC,gHAAM,CAAC,SAAS;IACtD,YAAY,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;QACrC,IAAI,CAAC,IAAI,CAAC;QACV;IACF;IAEA,WAAW,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;QACpC,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;YAElC,iEAAiE;YACjE,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;gBACpB,MAAM,SAAS,OAAO,KAAK,CAAC;gBAC5B,MAAM,CAAC,EAAE,GAAG,KAAK,UAAU;gBAC3B,MAAM,CAAC,EAAE,GAAG,KAAK,WAAW;gBAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpB;QACF;QAEA,IAAI,CAAC,WAAW,CAAC,OAAO,UAAU;IACpC;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/callbackify.js"], "sourcesContent": ["import utils from \"../utils.js\";\n\nconst callbackify = (fn, reducer) => {\n  return utils.isAsyncFn(fn) ? function (...args) {\n    const cb = args.pop();\n    fn.apply(this, args).then((value) => {\n      try {\n        reducer ? cb(null, ...reducer(value)) : cb(null, value);\n      } catch (err) {\n        cb(err);\n      }\n    }, cb);\n  } : fn;\n}\n\nexport default callbackify;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc,CAAC,IAAI;IACvB,OAAO,gJAAK,CAAC,SAAS,CAAC,MAAM,SAAU,GAAG,IAAI;QAC5C,MAAM,KAAK,KAAK,GAAG;QACnB,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC;YACzB,IAAI;gBACF,UAAU,GAAG,SAAS,QAAQ,UAAU,GAAG,MAAM;YACnD,EAAE,OAAO,KAAK;gBACZ,GAAG;YACL;QACF,GAAG;IACL,IAAI;AACN;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/speedometer.js"], "sourcesContent": ["'use strict';\n\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */\nfunction speedometer(samplesCount, min) {\n  samplesCount = samplesCount || 10;\n  const bytes = new Array(samplesCount);\n  const timestamps = new Array(samplesCount);\n  let head = 0;\n  let tail = 0;\n  let firstSampleTS;\n\n  min = min !== undefined ? min : 1000;\n\n  return function push(chunkLength) {\n    const now = Date.now();\n\n    const startedAt = timestamps[tail];\n\n    if (!firstSampleTS) {\n      firstSampleTS = now;\n    }\n\n    bytes[head] = chunkLength;\n    timestamps[head] = now;\n\n    let i = tail;\n    let bytesCount = 0;\n\n    while (i !== head) {\n      bytesCount += bytes[i++];\n      i = i % samplesCount;\n    }\n\n    head = (head + 1) % samplesCount;\n\n    if (head === tail) {\n      tail = (tail + 1) % samplesCount;\n    }\n\n    if (now - firstSampleTS < min) {\n      return;\n    }\n\n    const passed = startedAt && now - startedAt;\n\n    return passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n  };\n}\n\nexport default speedometer;\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY,EAAE,GAAG;IACpC,eAAe,gBAAgB;IAC/B,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,aAAa,IAAI,MAAM;IAC7B,IAAI,OAAO;IACX,IAAI,OAAO;IACX,IAAI;IAEJ,MAAM,QAAQ,YAAY,MAAM;IAEhC,OAAO,SAAS,KAAK,WAAW;QAC9B,MAAM,MAAM,KAAK,GAAG;QAEpB,MAAM,YAAY,UAAU,CAAC,KAAK;QAElC,IAAI,CAAC,eAAe;YAClB,gBAAgB;QAClB;QAEA,KAAK,CAAC,KAAK,GAAG;QACd,UAAU,CAAC,KAAK,GAAG;QAEnB,IAAI,IAAI;QACR,IAAI,aAAa;QAEjB,MAAO,MAAM,KAAM;YACjB,cAAc,KAAK,CAAC,IAAI;YACxB,IAAI,IAAI;QACV;QAEA,OAAO,CAAC,OAAO,CAAC,IAAI;QAEpB,IAAI,SAAS,MAAM;YACjB,OAAO,CAAC,OAAO,CAAC,IAAI;QACtB;QAEA,IAAI,MAAM,gBAAgB,KAAK;YAC7B;QACF;QAEA,MAAM,SAAS,aAAa,MAAM;QAElC,OAAO,SAAS,KAAK,KAAK,CAAC,aAAa,OAAO,UAAU;IAC3D;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/throttle.js"], "sourcesContent": ["/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  let threshold = 1000 / freq;\n  let lastArgs;\n  let timer;\n\n  const invoke = (args, now = Date.now()) => {\n    timestamp = now;\n    lastArgs = null;\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n    fn(...args);\n  }\n\n  const throttled = (...args) => {\n    const now = Date.now();\n    const passed = now - timestamp;\n    if ( passed >= threshold) {\n      invoke(args, now);\n    } else {\n      lastArgs = args;\n      if (!timer) {\n        timer = setTimeout(() => {\n          timer = null;\n          invoke(lastArgs)\n        }, threshold - passed);\n      }\n    }\n  }\n\n  const flush = () => lastArgs && invoke(lastArgs);\n\n  return [throttled, flush];\n}\n\nexport default throttle;\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AACD,SAAS,SAAS,EAAE,EAAE,IAAI;IACxB,IAAI,YAAY;IAChB,IAAI,YAAY,OAAO;IACvB,IAAI;IACJ,IAAI;IAEJ,MAAM,SAAS,CAAC,MAAM,MAAM,KAAK,GAAG,EAAE;QACpC,YAAY;QACZ,WAAW;QACX,IAAI,OAAO;YACT,aAAa;YACb,QAAQ;QACV;QACA,MAAM;IACR;IAEA,MAAM,YAAY,CAAC,GAAG;QACpB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,MAAM;QACrB,IAAK,UAAU,WAAW;YACxB,OAAO,MAAM;QACf,OAAO;YACL,WAAW;YACX,IAAI,CAAC,OAAO;gBACV,QAAQ,WAAW;oBACjB,QAAQ;oBACR,OAAO;gBACT,GAAG,YAAY;YACjB;QACF;IACF;IAEA,MAAM,QAAQ,IAAM,YAAY,OAAO;IAEvC,OAAO;QAAC;QAAW;KAAM;AAC3B;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/progressEventReducer.js"], "sourcesContent": ["import speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\n\nexport const progressEventReducer = (listener, isDownloadStream, freq = 3) => {\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n\n  return throttle(e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n\n    bytesNotified = loaded;\n\n    const data = {\n      loaded,\n      total,\n      progress: total ? (loaded / total) : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null,\n      [isDownloadStream ? 'download' : 'upload']: true\n    };\n\n    listener(data);\n  }, freq);\n}\n\nexport const progressEventDecorator = (total, throttled) => {\n  const lengthComputable = total != null;\n\n  return [(loaded) => throttled[0]({\n    lengthComputable,\n    total,\n    loaded\n  }), throttled[1]];\n}\n\nexport const asyncDecorator = (fn) => (...args) => utils.asap(() => fn(...args));\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,uBAAuB,CAAC,UAAU,kBAAkB,OAAO,CAAC;IACvE,IAAI,gBAAgB;IACpB,MAAM,eAAe,IAAA,iKAAW,EAAC,IAAI;IAErC,OAAO,IAAA,8JAAQ,EAAC,CAAA;QACd,MAAM,SAAS,EAAE,MAAM;QACvB,MAAM,QAAQ,EAAE,gBAAgB,GAAG,EAAE,KAAK,GAAG;QAC7C,MAAM,gBAAgB,SAAS;QAC/B,MAAM,OAAO,aAAa;QAC1B,MAAM,UAAU,UAAU;QAE1B,gBAAgB;QAEhB,MAAM,OAAO;YACX;YACA;YACA,UAAU,QAAS,SAAS,QAAS;YACrC,OAAO;YACP,MAAM,OAAO,OAAO;YACpB,WAAW,QAAQ,SAAS,UAAU,CAAC,QAAQ,MAAM,IAAI,OAAO;YAChE,OAAO;YACP,kBAAkB,SAAS;YAC3B,CAAC,mBAAmB,aAAa,SAAS,EAAE;QAC9C;QAEA,SAAS;IACX,GAAG;AACL;AAEO,MAAM,yBAAyB,CAAC,OAAO;IAC5C,MAAM,mBAAmB,SAAS;IAElC,OAAO;QAAC,CAAC,SAAW,SAAS,CAAC,EAAE,CAAC;gBAC/B;gBACA;gBACA;YACF;QAAI,SAAS,CAAC,EAAE;KAAC;AACnB;AAEO,MAAM,iBAAiB,CAAC,KAAO,CAAC,GAAG,OAAS,gJAAK,CAAC,IAAI,CAAC,IAAM,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/adapters/http.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport settle from './../core/settle.js';\nimport buildFullPath from '../core/buildFullPath.js';\nimport buildURL from './../helpers/buildURL.js';\nimport proxyFromEnv from 'proxy-from-env';\nimport http from 'http';\nimport https from 'https';\nimport util from 'util';\nimport followRedirects from 'follow-redirects';\nimport zlib from 'zlib';\nimport {VERSION} from '../env/data.js';\nimport transitionalDefaults from '../defaults/transitional.js';\nimport AxiosError from '../core/AxiosError.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport platform from '../platform/index.js';\nimport fromDataURI from '../helpers/fromDataURI.js';\nimport stream from 'stream';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport AxiosTransformStream from '../helpers/AxiosTransformStream.js';\nimport {EventEmitter} from 'events';\nimport formDataToStream from \"../helpers/formDataToStream.js\";\nimport readBlob from \"../helpers/readBlob.js\";\nimport ZlibHeaderTransformStream from '../helpers/ZlibHeaderTransformStream.js';\nimport callbackify from \"../helpers/callbackify.js\";\nimport {progressEventReducer, progressEventDecorator, asyncDecorator} from \"../helpers/progressEventReducer.js\";\n\nconst zlibOptions = {\n  flush: zlib.constants.Z_SYNC_FLUSH,\n  finishFlush: zlib.constants.Z_SYNC_FLUSH\n};\n\nconst brotliOptions = {\n  flush: zlib.constants.BROTLI_OPERATION_FLUSH,\n  finishFlush: zlib.constants.BROTLI_OPERATION_FLUSH\n}\n\nconst isBrotliSupported = utils.isFunction(zlib.createBrotliDecompress);\n\nconst {http: httpFollow, https: httpsFollow} = followRedirects;\n\nconst isHttps = /https:?/;\n\nconst supportedProtocols = platform.protocols.map(protocol => {\n  return protocol + ':';\n});\n\nconst flushOnFinish = (stream, [throttled, flush]) => {\n  stream\n    .on('end', flush)\n    .on('error', flush);\n\n  return throttled;\n}\n\n/**\n * If the proxy or config beforeRedirects functions are defined, call them with the options\n * object.\n *\n * @param {Object<string, any>} options - The options object that was passed to the request.\n *\n * @returns {Object<string, any>}\n */\nfunction dispatchBeforeRedirect(options, responseDetails) {\n  if (options.beforeRedirects.proxy) {\n    options.beforeRedirects.proxy(options);\n  }\n  if (options.beforeRedirects.config) {\n    options.beforeRedirects.config(options, responseDetails);\n  }\n}\n\n/**\n * If the proxy or config afterRedirects functions are defined, call them with the options\n *\n * @param {http.ClientRequestArgs} options\n * @param {AxiosProxyConfig} configProxy configuration from Axios options object\n * @param {string} location\n *\n * @returns {http.ClientRequestArgs}\n */\nfunction setProxy(options, configProxy, location) {\n  let proxy = configProxy;\n  if (!proxy && proxy !== false) {\n    const proxyUrl = proxyFromEnv.getProxyForUrl(location);\n    if (proxyUrl) {\n      proxy = new URL(proxyUrl);\n    }\n  }\n  if (proxy) {\n    // Basic proxy authorization\n    if (proxy.username) {\n      proxy.auth = (proxy.username || '') + ':' + (proxy.password || '');\n    }\n\n    if (proxy.auth) {\n      // Support proxy auth object form\n      if (proxy.auth.username || proxy.auth.password) {\n        proxy.auth = (proxy.auth.username || '') + ':' + (proxy.auth.password || '');\n      }\n      const base64 = Buffer\n        .from(proxy.auth, 'utf8')\n        .toString('base64');\n      options.headers['Proxy-Authorization'] = 'Basic ' + base64;\n    }\n\n    options.headers.host = options.hostname + (options.port ? ':' + options.port : '');\n    const proxyHost = proxy.hostname || proxy.host;\n    options.hostname = proxyHost;\n    // Replace 'host' since options is not a URL object\n    options.host = proxyHost;\n    options.port = proxy.port;\n    options.path = location;\n    if (proxy.protocol) {\n      options.protocol = proxy.protocol.includes(':') ? proxy.protocol : `${proxy.protocol}:`;\n    }\n  }\n\n  options.beforeRedirects.proxy = function beforeRedirect(redirectOptions) {\n    // Configure proxy for redirected request, passing the original config proxy to apply\n    // the exact same logic as if the redirected request was performed by axios directly.\n    setProxy(redirectOptions, configProxy, redirectOptions.href);\n  };\n}\n\nconst isHttpAdapterSupported = typeof process !== 'undefined' && utils.kindOf(process) === 'process';\n\n// temporary hotfix\n\nconst wrapAsync = (asyncExecutor) => {\n  return new Promise((resolve, reject) => {\n    let onDone;\n    let isDone;\n\n    const done = (value, isRejected) => {\n      if (isDone) return;\n      isDone = true;\n      onDone && onDone(value, isRejected);\n    }\n\n    const _resolve = (value) => {\n      done(value);\n      resolve(value);\n    };\n\n    const _reject = (reason) => {\n      done(reason, true);\n      reject(reason);\n    }\n\n    asyncExecutor(_resolve, _reject, (onDoneHandler) => (onDone = onDoneHandler)).catch(_reject);\n  })\n};\n\nconst resolveFamily = ({address, family}) => {\n  if (!utils.isString(address)) {\n    throw TypeError('address must be a string');\n  }\n  return ({\n    address,\n    family: family || (address.indexOf('.') < 0 ? 6 : 4)\n  });\n}\n\nconst buildAddressEntry = (address, family) => resolveFamily(utils.isObject(address) ? address : {address, family});\n\n/*eslint consistent-return:0*/\nexport default isHttpAdapterSupported && function httpAdapter(config) {\n  return wrapAsync(async function dispatchHttpRequest(resolve, reject, onDone) {\n    let {data, lookup, family} = config;\n    const {responseType, responseEncoding} = config;\n    const method = config.method.toUpperCase();\n    let isDone;\n    let rejected = false;\n    let req;\n\n    if (lookup) {\n      const _lookup = callbackify(lookup, (value) => utils.isArray(value) ? value : [value]);\n      // hotfix to support opt.all option which is required for node 20.x\n      lookup = (hostname, opt, cb) => {\n        _lookup(hostname, opt, (err, arg0, arg1) => {\n          if (err) {\n            return cb(err);\n          }\n\n          const addresses = utils.isArray(arg0) ? arg0.map(addr => buildAddressEntry(addr)) : [buildAddressEntry(arg0, arg1)];\n\n          opt.all ? cb(err, addresses) : cb(err, addresses[0].address, addresses[0].family);\n        });\n      }\n    }\n\n    // temporary internal emitter until the AxiosRequest class will be implemented\n    const emitter = new EventEmitter();\n\n    const onFinished = () => {\n      if (config.cancelToken) {\n        config.cancelToken.unsubscribe(abort);\n      }\n\n      if (config.signal) {\n        config.signal.removeEventListener('abort', abort);\n      }\n\n      emitter.removeAllListeners();\n    }\n\n    onDone((value, isRejected) => {\n      isDone = true;\n      if (isRejected) {\n        rejected = true;\n        onFinished();\n      }\n    });\n\n    function abort(reason) {\n      emitter.emit('abort', !reason || reason.type ? new CanceledError(null, config, req) : reason);\n    }\n\n    emitter.once('abort', reject);\n\n    if (config.cancelToken || config.signal) {\n      config.cancelToken && config.cancelToken.subscribe(abort);\n      if (config.signal) {\n        config.signal.aborted ? abort() : config.signal.addEventListener('abort', abort);\n      }\n    }\n\n    // Parse url\n    const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);\n    const parsed = new URL(fullPath, platform.hasBrowserEnv ? platform.origin : undefined);\n    const protocol = parsed.protocol || supportedProtocols[0];\n\n    if (protocol === 'data:') {\n      let convertedData;\n\n      if (method !== 'GET') {\n        return settle(resolve, reject, {\n          status: 405,\n          statusText: 'method not allowed',\n          headers: {},\n          config\n        });\n      }\n\n      try {\n        convertedData = fromDataURI(config.url, responseType === 'blob', {\n          Blob: config.env && config.env.Blob\n        });\n      } catch (err) {\n        throw AxiosError.from(err, AxiosError.ERR_BAD_REQUEST, config);\n      }\n\n      if (responseType === 'text') {\n        convertedData = convertedData.toString(responseEncoding);\n\n        if (!responseEncoding || responseEncoding === 'utf8') {\n          convertedData = utils.stripBOM(convertedData);\n        }\n      } else if (responseType === 'stream') {\n        convertedData = stream.Readable.from(convertedData);\n      }\n\n      return settle(resolve, reject, {\n        data: convertedData,\n        status: 200,\n        statusText: 'OK',\n        headers: new AxiosHeaders(),\n        config\n      });\n    }\n\n    if (supportedProtocols.indexOf(protocol) === -1) {\n      return reject(new AxiosError(\n        'Unsupported protocol ' + protocol,\n        AxiosError.ERR_BAD_REQUEST,\n        config\n      ));\n    }\n\n    const headers = AxiosHeaders.from(config.headers).normalize();\n\n    // Set User-Agent (required by some servers)\n    // See https://github.com/axios/axios/issues/69\n    // User-Agent is specified; handle case where no UA header is desired\n    // Only set header if it hasn't been set in config\n    headers.set('User-Agent', 'axios/' + VERSION, false);\n\n    const {onUploadProgress, onDownloadProgress} = config;\n    const maxRate = config.maxRate;\n    let maxUploadRate = undefined;\n    let maxDownloadRate = undefined;\n\n    // support for spec compliant FormData objects\n    if (utils.isSpecCompliantForm(data)) {\n      const userBoundary = headers.getContentType(/boundary=([-_\\w\\d]{10,70})/i);\n\n      data = formDataToStream(data, (formHeaders) => {\n        headers.set(formHeaders);\n      }, {\n        tag: `axios-${VERSION}-boundary`,\n        boundary: userBoundary && userBoundary[1] || undefined\n      });\n      // support for https://www.npmjs.com/package/form-data api\n    } else if (utils.isFormData(data) && utils.isFunction(data.getHeaders)) {\n      headers.set(data.getHeaders());\n\n      if (!headers.hasContentLength()) {\n        try {\n          const knownLength = await util.promisify(data.getLength).call(data);\n          Number.isFinite(knownLength) && knownLength >= 0 && headers.setContentLength(knownLength);\n          /*eslint no-empty:0*/\n        } catch (e) {\n        }\n      }\n    } else if (utils.isBlob(data) || utils.isFile(data)) {\n      data.size && headers.setContentType(data.type || 'application/octet-stream');\n      headers.setContentLength(data.size || 0);\n      data = stream.Readable.from(readBlob(data));\n    } else if (data && !utils.isStream(data)) {\n      if (Buffer.isBuffer(data)) {\n        // Nothing to do...\n      } else if (utils.isArrayBuffer(data)) {\n        data = Buffer.from(new Uint8Array(data));\n      } else if (utils.isString(data)) {\n        data = Buffer.from(data, 'utf-8');\n      } else {\n        return reject(new AxiosError(\n          'Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream',\n          AxiosError.ERR_BAD_REQUEST,\n          config\n        ));\n      }\n\n      // Add Content-Length header if data exists\n      headers.setContentLength(data.length, false);\n\n      if (config.maxBodyLength > -1 && data.length > config.maxBodyLength) {\n        return reject(new AxiosError(\n          'Request body larger than maxBodyLength limit',\n          AxiosError.ERR_BAD_REQUEST,\n          config\n        ));\n      }\n    }\n\n    const contentLength = utils.toFiniteNumber(headers.getContentLength());\n\n    if (utils.isArray(maxRate)) {\n      maxUploadRate = maxRate[0];\n      maxDownloadRate = maxRate[1];\n    } else {\n      maxUploadRate = maxDownloadRate = maxRate;\n    }\n\n    if (data && (onUploadProgress || maxUploadRate)) {\n      if (!utils.isStream(data)) {\n        data = stream.Readable.from(data, {objectMode: false});\n      }\n\n      data = stream.pipeline([data, new AxiosTransformStream({\n        maxRate: utils.toFiniteNumber(maxUploadRate)\n      })], utils.noop);\n\n      onUploadProgress && data.on('progress', flushOnFinish(\n        data,\n        progressEventDecorator(\n          contentLength,\n          progressEventReducer(asyncDecorator(onUploadProgress), false, 3)\n        )\n      ));\n    }\n\n    // HTTP basic authentication\n    let auth = undefined;\n    if (config.auth) {\n      const username = config.auth.username || '';\n      const password = config.auth.password || '';\n      auth = username + ':' + password;\n    }\n\n    if (!auth && parsed.username) {\n      const urlUsername = parsed.username;\n      const urlPassword = parsed.password;\n      auth = urlUsername + ':' + urlPassword;\n    }\n\n    auth && headers.delete('authorization');\n\n    let path;\n\n    try {\n      path = buildURL(\n        parsed.pathname + parsed.search,\n        config.params,\n        config.paramsSerializer\n      ).replace(/^\\?/, '');\n    } catch (err) {\n      const customErr = new Error(err.message);\n      customErr.config = config;\n      customErr.url = config.url;\n      customErr.exists = true;\n      return reject(customErr);\n    }\n\n    headers.set(\n      'Accept-Encoding',\n      'gzip, compress, deflate' + (isBrotliSupported ? ', br' : ''), false\n      );\n\n    const options = {\n      path,\n      method: method,\n      headers: headers.toJSON(),\n      agents: { http: config.httpAgent, https: config.httpsAgent },\n      auth,\n      protocol,\n      family,\n      beforeRedirect: dispatchBeforeRedirect,\n      beforeRedirects: {}\n    };\n\n    // cacheable-lookup integration hotfix\n    !utils.isUndefined(lookup) && (options.lookup = lookup);\n\n    if (config.socketPath) {\n      options.socketPath = config.socketPath;\n    } else {\n      options.hostname = parsed.hostname.startsWith(\"[\") ? parsed.hostname.slice(1, -1) : parsed.hostname;\n      options.port = parsed.port;\n      setProxy(options, config.proxy, protocol + '//' + parsed.hostname + (parsed.port ? ':' + parsed.port : '') + options.path);\n    }\n\n    let transport;\n    const isHttpsRequest = isHttps.test(options.protocol);\n    options.agent = isHttpsRequest ? config.httpsAgent : config.httpAgent;\n    if (config.transport) {\n      transport = config.transport;\n    } else if (config.maxRedirects === 0) {\n      transport = isHttpsRequest ? https : http;\n    } else {\n      if (config.maxRedirects) {\n        options.maxRedirects = config.maxRedirects;\n      }\n      if (config.beforeRedirect) {\n        options.beforeRedirects.config = config.beforeRedirect;\n      }\n      transport = isHttpsRequest ? httpsFollow : httpFollow;\n    }\n\n    if (config.maxBodyLength > -1) {\n      options.maxBodyLength = config.maxBodyLength;\n    } else {\n      // follow-redirects does not skip comparison, so it should always succeed for axios -1 unlimited\n      options.maxBodyLength = Infinity;\n    }\n\n    if (config.insecureHTTPParser) {\n      options.insecureHTTPParser = config.insecureHTTPParser;\n    }\n\n    // Create the request\n    req = transport.request(options, function handleResponse(res) {\n      if (req.destroyed) return;\n\n      const streams = [res];\n\n      const responseLength = +res.headers['content-length'];\n\n      if (onDownloadProgress || maxDownloadRate) {\n        const transformStream = new AxiosTransformStream({\n          maxRate: utils.toFiniteNumber(maxDownloadRate)\n        });\n\n        onDownloadProgress && transformStream.on('progress', flushOnFinish(\n          transformStream,\n          progressEventDecorator(\n            responseLength,\n            progressEventReducer(asyncDecorator(onDownloadProgress), true, 3)\n          )\n        ));\n\n        streams.push(transformStream);\n      }\n\n      // decompress the response body transparently if required\n      let responseStream = res;\n\n      // return the last request in case of redirects\n      const lastRequest = res.req || req;\n\n      // if decompress disabled we should not decompress\n      if (config.decompress !== false && res.headers['content-encoding']) {\n        // if no content, but headers still say that it is encoded,\n        // remove the header not confuse downstream operations\n        if (method === 'HEAD' || res.statusCode === 204) {\n          delete res.headers['content-encoding'];\n        }\n\n        switch ((res.headers['content-encoding'] || '').toLowerCase()) {\n        /*eslint default-case:0*/\n        case 'gzip':\n        case 'x-gzip':\n        case 'compress':\n        case 'x-compress':\n          // add the unzipper to the body stream processing pipeline\n          streams.push(zlib.createUnzip(zlibOptions));\n\n          // remove the content-encoding in order to not confuse downstream operations\n          delete res.headers['content-encoding'];\n          break;\n        case 'deflate':\n          streams.push(new ZlibHeaderTransformStream());\n\n          // add the unzipper to the body stream processing pipeline\n          streams.push(zlib.createUnzip(zlibOptions));\n\n          // remove the content-encoding in order to not confuse downstream operations\n          delete res.headers['content-encoding'];\n          break;\n        case 'br':\n          if (isBrotliSupported) {\n            streams.push(zlib.createBrotliDecompress(brotliOptions));\n            delete res.headers['content-encoding'];\n          }\n        }\n      }\n\n      responseStream = streams.length > 1 ? stream.pipeline(streams, utils.noop) : streams[0];\n\n      const offListeners = stream.finished(responseStream, () => {\n        offListeners();\n        onFinished();\n      });\n\n      const response = {\n        status: res.statusCode,\n        statusText: res.statusMessage,\n        headers: new AxiosHeaders(res.headers),\n        config,\n        request: lastRequest\n      };\n\n      if (responseType === 'stream') {\n        response.data = responseStream;\n        settle(resolve, reject, response);\n      } else {\n        const responseBuffer = [];\n        let totalResponseBytes = 0;\n\n        responseStream.on('data', function handleStreamData(chunk) {\n          responseBuffer.push(chunk);\n          totalResponseBytes += chunk.length;\n\n          // make sure the content length is not over the maxContentLength if specified\n          if (config.maxContentLength > -1 && totalResponseBytes > config.maxContentLength) {\n            // stream.destroy() emit aborted event before calling reject() on Node.js v16\n            rejected = true;\n            responseStream.destroy();\n            reject(new AxiosError('maxContentLength size of ' + config.maxContentLength + ' exceeded',\n              AxiosError.ERR_BAD_RESPONSE, config, lastRequest));\n          }\n        });\n\n        responseStream.on('aborted', function handlerStreamAborted() {\n          if (rejected) {\n            return;\n          }\n\n          const err = new AxiosError(\n            'stream has been aborted',\n            AxiosError.ERR_BAD_RESPONSE,\n            config,\n            lastRequest\n          );\n          responseStream.destroy(err);\n          reject(err);\n        });\n\n        responseStream.on('error', function handleStreamError(err) {\n          if (req.destroyed) return;\n          reject(AxiosError.from(err, null, config, lastRequest));\n        });\n\n        responseStream.on('end', function handleStreamEnd() {\n          try {\n            let responseData = responseBuffer.length === 1 ? responseBuffer[0] : Buffer.concat(responseBuffer);\n            if (responseType !== 'arraybuffer') {\n              responseData = responseData.toString(responseEncoding);\n              if (!responseEncoding || responseEncoding === 'utf8') {\n                responseData = utils.stripBOM(responseData);\n              }\n            }\n            response.data = responseData;\n          } catch (err) {\n            return reject(AxiosError.from(err, null, config, response.request, response));\n          }\n          settle(resolve, reject, response);\n        });\n      }\n\n      emitter.once('abort', err => {\n        if (!responseStream.destroyed) {\n          responseStream.emit('error', err);\n          responseStream.destroy();\n        }\n      });\n    });\n\n    emitter.once('abort', err => {\n      reject(err);\n      req.destroy(err);\n    });\n\n    // Handle errors\n    req.on('error', function handleRequestError(err) {\n      // @todo remove\n      // if (req.aborted && err.code !== AxiosError.ERR_FR_TOO_MANY_REDIRECTS) return;\n      reject(AxiosError.from(err, null, config, req));\n    });\n\n    // set tcp keep alive to prevent drop connection by peer\n    req.on('socket', function handleRequestSocket(socket) {\n      // default interval of sending ack packet is 1 minute\n      socket.setKeepAlive(true, 1000 * 60);\n    });\n\n    // Handle request timeout\n    if (config.timeout) {\n      // This is forcing a int timeout to avoid problems if the `req` interface doesn't handle other types.\n      const timeout = parseInt(config.timeout, 10);\n\n      if (Number.isNaN(timeout)) {\n        reject(new AxiosError(\n          'error trying to parse `config.timeout` to int',\n          AxiosError.ERR_BAD_OPTION_VALUE,\n          config,\n          req\n        ));\n\n        return;\n      }\n\n      // Sometime, the response will be very slow, and does not respond, the connect event will be block by event loop system.\n      // And timer callback will be fired, and abort() will be invoked before connection, then get \"socket hang up\" and code ECONNRESET.\n      // At this time, if we have a large number of request, nodejs will hang up some socket on background. and the number will up and up.\n      // And then these socket which be hang up will devouring CPU little by little.\n      // ClientRequest.setTimeout will be fired on the specify milliseconds, and can make sure that abort() will be fired after connect.\n      req.setTimeout(timeout, function handleRequestTimeout() {\n        if (isDone) return;\n        let timeoutErrorMessage = config.timeout ? 'timeout of ' + config.timeout + 'ms exceeded' : 'timeout exceeded';\n        const transitional = config.transitional || transitionalDefaults;\n        if (config.timeoutErrorMessage) {\n          timeoutErrorMessage = config.timeoutErrorMessage;\n        }\n        reject(new AxiosError(\n          timeoutErrorMessage,\n          transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,\n          config,\n          req\n        ));\n        abort();\n      });\n    }\n\n\n    // Send the request\n    if (utils.isStream(data)) {\n      let ended = false;\n      let errored = false;\n\n      data.on('end', () => {\n        ended = true;\n      });\n\n      data.once('error', err => {\n        errored = true;\n        req.destroy(err);\n      });\n\n      data.on('close', () => {\n        if (!ended && !errored) {\n          abort(new CanceledError('Request stream has been aborted', config, req));\n        }\n      });\n\n      data.pipe(req);\n    } else {\n      req.end(data);\n    }\n  });\n}\n\nexport const __setProxy = setProxy;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,MAAM,cAAc;IAClB,OAAO,4GAAI,CAAC,SAAS,CAAC,YAAY;IAClC,aAAa,4GAAI,CAAC,SAAS,CAAC,YAAY;AAC1C;AAEA,MAAM,gBAAgB;IACpB,OAAO,4GAAI,CAAC,SAAS,CAAC,sBAAsB;IAC5C,aAAa,4GAAI,CAAC,SAAS,CAAC,sBAAsB;AACpD;AAEA,MAAM,oBAAoB,gJAAK,CAAC,UAAU,CAAC,4GAAI,CAAC,sBAAsB;AAEtE,MAAM,EAAC,MAAM,UAAU,EAAE,OAAO,WAAW,EAAC,GAAG,uJAAe;AAE9D,MAAM,UAAU;AAEhB,MAAM,qBAAqB,4JAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;IAChD,OAAO,WAAW;AACpB;AAEA,MAAM,gBAAgB,CAAC,QAAQ,CAAC,WAAW,MAAM;IAC/C,OACG,EAAE,CAAC,OAAO,OACV,EAAE,CAAC,SAAS;IAEf,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,uBAAuB,OAAO,EAAE,eAAe;IACtD,IAAI,QAAQ,eAAe,CAAC,KAAK,EAAE;QACjC,QAAQ,eAAe,CAAC,KAAK,CAAC;IAChC;IACA,IAAI,QAAQ,eAAe,CAAC,MAAM,EAAE;QAClC,QAAQ,eAAe,CAAC,MAAM,CAAC,SAAS;IAC1C;AACF;AAEA;;;;;;;;CAQC,GACD,SAAS,SAAS,OAAO,EAAE,WAAW,EAAE,QAAQ;IAC9C,IAAI,QAAQ;IACZ,IAAI,CAAC,SAAS,UAAU,OAAO;QAC7B,MAAM,WAAW,wJAAY,CAAC,cAAc,CAAC;QAC7C,IAAI,UAAU;YACZ,QAAQ,IAAI,IAAI;QAClB;IACF;IACA,IAAI,OAAO;QACT,4BAA4B;QAC5B,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,IAAI,GAAG,CAAC,MAAM,QAAQ,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,QAAQ,IAAI,EAAE;QACnE;QAEA,IAAI,MAAM,IAAI,EAAE;YACd,iCAAiC;YACjC,IAAI,MAAM,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;gBAC9C,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,EAAE;YAC7E;YACA,MAAM,SAAS,OACZ,IAAI,CAAC,MAAM,IAAI,EAAE,QACjB,QAAQ,CAAC;YACZ,QAAQ,OAAO,CAAC,sBAAsB,GAAG,WAAW;QACtD;QAEA,QAAQ,OAAO,CAAC,IAAI,GAAG,QAAQ,QAAQ,GAAG,CAAC,QAAQ,IAAI,GAAG,MAAM,QAAQ,IAAI,GAAG,EAAE;QACjF,MAAM,YAAY,MAAM,QAAQ,IAAI,MAAM,IAAI;QAC9C,QAAQ,QAAQ,GAAG;QACnB,mDAAmD;QACnD,QAAQ,IAAI,GAAG;QACf,QAAQ,IAAI,GAAG,MAAM,IAAI;QACzB,QAAQ,IAAI,GAAG;QACf,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,QAAQ,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,OAAO,MAAM,QAAQ,GAAG,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC;QACzF;IACF;IAEA,QAAQ,eAAe,CAAC,KAAK,GAAG,SAAS,eAAe,eAAe;QACrE,qFAAqF;QACrF,qFAAqF;QACrF,SAAS,iBAAiB,aAAa,gBAAgB,IAAI;IAC7D;AACF;AAEA,MAAM,yBAAyB,OAAO,YAAY,eAAe,gJAAK,CAAC,MAAM,CAAC,aAAa;AAE3F,mBAAmB;AAEnB,MAAM,YAAY,CAAC;IACjB,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;QACJ,IAAI;QAEJ,MAAM,OAAO,CAAC,OAAO;YACnB,IAAI,QAAQ;YACZ,SAAS;YACT,UAAU,OAAO,OAAO;QAC1B;QAEA,MAAM,WAAW,CAAC;YAChB,KAAK;YACL,QAAQ;QACV;QAEA,MAAM,UAAU,CAAC;YACf,KAAK,QAAQ;YACb,OAAO;QACT;QAEA,cAAc,UAAU,SAAS,CAAC,gBAAmB,SAAS,eAAgB,KAAK,CAAC;IACtF;AACF;AAEA,MAAM,gBAAgB,CAAC,EAAC,OAAO,EAAE,MAAM,EAAC;IACtC,IAAI,CAAC,gJAAK,CAAC,QAAQ,CAAC,UAAU;QAC5B,MAAM,UAAU;IAClB;IACA,OAAQ;QACN;QACA,QAAQ,UAAU,CAAC,QAAQ,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;IACrD;AACF;AAEA,MAAM,oBAAoB,CAAC,SAAS,SAAW,cAAc,gJAAK,CAAC,QAAQ,CAAC,WAAW,UAAU;QAAC;QAAS;IAAM;uCAGlG,0BAA0B,SAAS,YAAY,MAAM;IAClE,OAAO,UAAU,eAAe,oBAAoB,OAAO,EAAE,MAAM,EAAE,MAAM;QACzE,IAAI,EAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAC,GAAG;QAC7B,MAAM,EAAC,YAAY,EAAE,gBAAgB,EAAC,GAAG;QACzC,MAAM,SAAS,OAAO,MAAM,CAAC,WAAW;QACxC,IAAI;QACJ,IAAI,WAAW;QACf,IAAI;QAEJ,IAAI,QAAQ;YACV,MAAM,UAAU,IAAA,iKAAW,EAAC,QAAQ,CAAC,QAAU,gJAAK,CAAC,OAAO,CAAC,SAAS,QAAQ;oBAAC;iBAAM;YACrF,mEAAmE;YACnE,SAAS,CAAC,UAAU,KAAK;gBACvB,QAAQ,UAAU,KAAK,CAAC,KAAK,MAAM;oBACjC,IAAI,KAAK;wBACP,OAAO,GAAG;oBACZ;oBAEA,MAAM,YAAY,gJAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,kBAAkB,SAAS;wBAAC,kBAAkB,MAAM;qBAAM;oBAEnH,IAAI,GAAG,GAAG,GAAG,KAAK,aAAa,GAAG,KAAK,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC,MAAM;gBAClF;YACF;QACF;QAEA,8EAA8E;QAC9E,MAAM,UAAU,IAAI,qHAAY;QAEhC,MAAM,aAAa;YACjB,IAAI,OAAO,WAAW,EAAE;gBACtB,OAAO,WAAW,CAAC,WAAW,CAAC;YACjC;YAEA,IAAI,OAAO,MAAM,EAAE;gBACjB,OAAO,MAAM,CAAC,mBAAmB,CAAC,SAAS;YAC7C;YAEA,QAAQ,kBAAkB;QAC5B;QAEA,OAAO,CAAC,OAAO;YACb,SAAS;YACT,IAAI,YAAY;gBACd,WAAW;gBACX;YACF;QACF;QAEA,SAAS,MAAM,MAAM;YACnB,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,OAAO,IAAI,GAAG,IAAI,kKAAa,CAAC,MAAM,QAAQ,OAAO;QACxF;QAEA,QAAQ,IAAI,CAAC,SAAS;QAEtB,IAAI,OAAO,WAAW,IAAI,OAAO,MAAM,EAAE;YACvC,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,SAAS,CAAC;YACnD,IAAI,OAAO,MAAM,EAAE;gBACjB,OAAO,MAAM,CAAC,OAAO,GAAG,UAAU,OAAO,MAAM,CAAC,gBAAgB,CAAC,SAAS;YAC5E;QACF;QAEA,YAAY;QACZ,MAAM,WAAW,IAAA,gKAAa,EAAC,OAAO,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,iBAAiB;QACnF,MAAM,SAAS,IAAI,IAAI,UAAU,4JAAQ,CAAC,aAAa,GAAG,4JAAQ,CAAC,MAAM,GAAG;QAC5E,MAAM,WAAW,OAAO,QAAQ,IAAI,kBAAkB,CAAC,EAAE;QAEzD,IAAI,aAAa,SAAS;YACxB,IAAI;YAEJ,IAAI,WAAW,OAAO;gBACpB,OAAO,IAAA,yJAAM,EAAC,SAAS,QAAQ;oBAC7B,QAAQ;oBACR,YAAY;oBACZ,SAAS,CAAC;oBACV;gBACF;YACF;YAEA,IAAI;gBACF,gBAAgB,IAAA,iKAAW,EAAC,OAAO,GAAG,EAAE,iBAAiB,QAAQ;oBAC/D,MAAM,OAAO,GAAG,IAAI,OAAO,GAAG,CAAC,IAAI;gBACrC;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,6JAAU,CAAC,IAAI,CAAC,KAAK,6JAAU,CAAC,eAAe,EAAE;YACzD;YAEA,IAAI,iBAAiB,QAAQ;gBAC3B,gBAAgB,cAAc,QAAQ,CAAC;gBAEvC,IAAI,CAAC,oBAAoB,qBAAqB,QAAQ;oBACpD,gBAAgB,gJAAK,CAAC,QAAQ,CAAC;gBACjC;YACF,OAAO,IAAI,iBAAiB,UAAU;gBACpC,gBAAgB,gHAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACvC;YAEA,OAAO,IAAA,yJAAM,EAAC,SAAS,QAAQ;gBAC7B,MAAM;gBACN,QAAQ;gBACR,YAAY;gBACZ,SAAS,IAAI,+JAAY;gBACzB;YACF;QACF;QAEA,IAAI,mBAAmB,OAAO,CAAC,cAAc,CAAC,GAAG;YAC/C,OAAO,OAAO,IAAI,6JAAU,CAC1B,0BAA0B,UAC1B,6JAAU,CAAC,eAAe,EAC1B;QAEJ;QAEA,MAAM,UAAU,+JAAY,CAAC,IAAI,CAAC,OAAO,OAAO,EAAE,SAAS;QAE3D,4CAA4C;QAC5C,+CAA+C;QAC/C,qEAAqE;QACrE,kDAAkD;QAClD,QAAQ,GAAG,CAAC,cAAc,WAAW,sJAAO,EAAE;QAE9C,MAAM,EAAC,gBAAgB,EAAE,kBAAkB,EAAC,GAAG;QAC/C,MAAM,UAAU,OAAO,OAAO;QAC9B,IAAI,gBAAgB;QACpB,IAAI,kBAAkB;QAEtB,8CAA8C;QAC9C,IAAI,gJAAK,CAAC,mBAAmB,CAAC,OAAO;YACnC,MAAM,eAAe,QAAQ,cAAc,CAAC;YAE5C,OAAO,IAAA,sKAAgB,EAAC,MAAM,CAAC;gBAC7B,QAAQ,GAAG,CAAC;YACd,GAAG;gBACD,KAAK,CAAC,MAAM,EAAE,sJAAO,CAAC,SAAS,CAAC;gBAChC,UAAU,gBAAgB,YAAY,CAAC,EAAE,IAAI;YAC/C;QACA,0DAA0D;QAC5D,OAAO,IAAI,gJAAK,CAAC,UAAU,CAAC,SAAS,gJAAK,CAAC,UAAU,CAAC,KAAK,UAAU,GAAG;YACtE,QAAQ,GAAG,CAAC,KAAK,UAAU;YAE3B,IAAI,CAAC,QAAQ,gBAAgB,IAAI;gBAC/B,IAAI;oBACF,MAAM,cAAc,MAAM,4GAAI,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE,IAAI,CAAC;oBAC9D,OAAO,QAAQ,CAAC,gBAAgB,eAAe,KAAK,QAAQ,gBAAgB,CAAC;gBAC7E,mBAAmB,GACrB,EAAE,OAAO,GAAG,CACZ;YACF;QACF,OAAO,IAAI,gJAAK,CAAC,MAAM,CAAC,SAAS,gJAAK,CAAC,MAAM,CAAC,OAAO;YACnD,KAAK,IAAI,IAAI,QAAQ,cAAc,CAAC,KAAK,IAAI,IAAI;YACjD,QAAQ,gBAAgB,CAAC,KAAK,IAAI,IAAI;YACtC,OAAO,gHAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAA,8JAAQ,EAAC;QACvC,OAAO,IAAI,QAAQ,CAAC,gJAAK,CAAC,QAAQ,CAAC,OAAO;YACxC,IAAI,OAAO,QAAQ,CAAC,OAAO;YACzB,mBAAmB;YACrB,OAAO,IAAI,gJAAK,CAAC,aAAa,CAAC,OAAO;gBACpC,OAAO,OAAO,IAAI,CAAC,IAAI,WAAW;YACpC,OAAO,IAAI,gJAAK,CAAC,QAAQ,CAAC,OAAO;gBAC/B,OAAO,OAAO,IAAI,CAAC,MAAM;YAC3B,OAAO;gBACL,OAAO,OAAO,IAAI,6JAAU,CAC1B,qFACA,6JAAU,CAAC,eAAe,EAC1B;YAEJ;YAEA,2CAA2C;YAC3C,QAAQ,gBAAgB,CAAC,KAAK,MAAM,EAAE;YAEtC,IAAI,OAAO,aAAa,GAAG,CAAC,KAAK,KAAK,MAAM,GAAG,OAAO,aAAa,EAAE;gBACnE,OAAO,OAAO,IAAI,6JAAU,CAC1B,gDACA,6JAAU,CAAC,eAAe,EAC1B;YAEJ;QACF;QAEA,MAAM,gBAAgB,gJAAK,CAAC,cAAc,CAAC,QAAQ,gBAAgB;QAEnE,IAAI,gJAAK,CAAC,OAAO,CAAC,UAAU;YAC1B,gBAAgB,OAAO,CAAC,EAAE;YAC1B,kBAAkB,OAAO,CAAC,EAAE;QAC9B,OAAO;YACL,gBAAgB,kBAAkB;QACpC;QAEA,IAAI,QAAQ,CAAC,oBAAoB,aAAa,GAAG;YAC/C,IAAI,CAAC,gJAAK,CAAC,QAAQ,CAAC,OAAO;gBACzB,OAAO,gHAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM;oBAAC,YAAY;gBAAK;YACtD;YAEA,OAAO,gHAAM,CAAC,QAAQ,CAAC;gBAAC;gBAAM,IAAI,0KAAoB,CAAC;oBACrD,SAAS,gJAAK,CAAC,cAAc,CAAC;gBAChC;aAAG,EAAE,gJAAK,CAAC,IAAI;YAEf,oBAAoB,KAAK,EAAE,CAAC,YAAY,cACtC,MACA,IAAA,yLAAsB,EACpB,eACA,IAAA,uLAAoB,EAAC,IAAA,iLAAc,EAAC,mBAAmB,OAAO;QAGpE;QAEA,4BAA4B;QAC5B,IAAI,OAAO;QACX,IAAI,OAAO,IAAI,EAAE;YACf,MAAM,WAAW,OAAO,IAAI,CAAC,QAAQ,IAAI;YACzC,MAAM,WAAW,OAAO,IAAI,CAAC,QAAQ,IAAI;YACzC,OAAO,WAAW,MAAM;QAC1B;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,EAAE;YAC5B,MAAM,cAAc,OAAO,QAAQ;YACnC,MAAM,cAAc,OAAO,QAAQ;YACnC,OAAO,cAAc,MAAM;QAC7B;QAEA,QAAQ,QAAQ,MAAM,CAAC;QAEvB,IAAI;QAEJ,IAAI;YACF,OAAO,IAAA,8JAAQ,EACb,OAAO,QAAQ,GAAG,OAAO,MAAM,EAC/B,OAAO,MAAM,EACb,OAAO,gBAAgB,EACvB,OAAO,CAAC,OAAO;QACnB,EAAE,OAAO,KAAK;YACZ,MAAM,YAAY,IAAI,MAAM,IAAI,OAAO;YACvC,UAAU,MAAM,GAAG;YACnB,UAAU,GAAG,GAAG,OAAO,GAAG;YAC1B,UAAU,MAAM,GAAG;YACnB,OAAO,OAAO;QAChB;QAEA,QAAQ,GAAG,CACT,mBACA,4BAA4B,CAAC,oBAAoB,SAAS,EAAE,GAAG;QAGjE,MAAM,UAAU;YACd;YACA,QAAQ;YACR,SAAS,QAAQ,MAAM;YACvB,QAAQ;gBAAE,MAAM,OAAO,SAAS;gBAAE,OAAO,OAAO,UAAU;YAAC;YAC3D;YACA;YACA;YACA,gBAAgB;YAChB,iBAAiB,CAAC;QACpB;QAEA,sCAAsC;QACtC,CAAC,gJAAK,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,MAAM,GAAG,MAAM;QAEtD,IAAI,OAAO,UAAU,EAAE;YACrB,QAAQ,UAAU,GAAG,OAAO,UAAU;QACxC,OAAO;YACL,QAAQ,QAAQ,GAAG,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ;YACnG,QAAQ,IAAI,GAAG,OAAO,IAAI;YAC1B,SAAS,SAAS,OAAO,KAAK,EAAE,WAAW,OAAO,OAAO,QAAQ,GAAG,CAAC,OAAO,IAAI,GAAG,MAAM,OAAO,IAAI,GAAG,EAAE,IAAI,QAAQ,IAAI;QAC3H;QAEA,IAAI;QACJ,MAAM,iBAAiB,QAAQ,IAAI,CAAC,QAAQ,QAAQ;QACpD,QAAQ,KAAK,GAAG,iBAAiB,OAAO,UAAU,GAAG,OAAO,SAAS;QACrE,IAAI,OAAO,SAAS,EAAE;YACpB,YAAY,OAAO,SAAS;QAC9B,OAAO,IAAI,OAAO,YAAY,KAAK,GAAG;YACpC,YAAY,iBAAiB,8GAAK,GAAG,4GAAI;QAC3C,OAAO;YACL,IAAI,OAAO,YAAY,EAAE;gBACvB,QAAQ,YAAY,GAAG,OAAO,YAAY;YAC5C;YACA,IAAI,OAAO,cAAc,EAAE;gBACzB,QAAQ,eAAe,CAAC,MAAM,GAAG,OAAO,cAAc;YACxD;YACA,YAAY,iBAAiB,cAAc;QAC7C;QAEA,IAAI,OAAO,aAAa,GAAG,CAAC,GAAG;YAC7B,QAAQ,aAAa,GAAG,OAAO,aAAa;QAC9C,OAAO;YACL,gGAAgG;YAChG,QAAQ,aAAa,GAAG;QAC1B;QAEA,IAAI,OAAO,kBAAkB,EAAE;YAC7B,QAAQ,kBAAkB,GAAG,OAAO,kBAAkB;QACxD;QAEA,qBAAqB;QACrB,MAAM,UAAU,OAAO,CAAC,SAAS,SAAS,eAAe,GAAG;YAC1D,IAAI,IAAI,SAAS,EAAE;YAEnB,MAAM,UAAU;gBAAC;aAAI;YAErB,MAAM,iBAAiB,CAAC,IAAI,OAAO,CAAC,iBAAiB;YAErD,IAAI,sBAAsB,iBAAiB;gBACzC,MAAM,kBAAkB,IAAI,0KAAoB,CAAC;oBAC/C,SAAS,gJAAK,CAAC,cAAc,CAAC;gBAChC;gBAEA,sBAAsB,gBAAgB,EAAE,CAAC,YAAY,cACnD,iBACA,+LACE,gBACA,6LAAqB,uLAAe,qBAAqB,MAAM;gBAInE,QAAQ,IAAI,CAAC;YACf;YAEA,yDAAyD;YACzD,IAAI,iBAAiB;YAErB,+CAA+C;YAC/C,MAAM,cAAc,IAAI,GAAG,IAAI;YAE/B,kDAAkD;YAClD,IAAI,OAAO,UAAU,KAAK,SAAS,IAAI,OAAO,CAAC,mBAAmB,EAAE;gBAClE,2DAA2D;gBAC3D,sDAAsD;gBACtD,IAAI,WAAW,UAAU,IAAI,UAAU,KAAK,KAAK;oBAC/C,OAAO,IAAI,OAAO,CAAC,mBAAmB;gBACxC;gBAEA,OAAQ,CAAC,IAAI,OAAO,CAAC,mBAAmB,IAAI,EAAE,EAAE,WAAW;oBAC3D,uBAAuB,GACvB,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,0DAA0D;wBAC1D,QAAQ,IAAI,CAAC,4GAAI,CAAC,WAAW,CAAC;wBAE9B,4EAA4E;wBAC5E,OAAO,IAAI,OAAO,CAAC,mBAAmB;wBACtC;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,IAAI,+KAAyB;wBAE1C,0DAA0D;wBAC1D,QAAQ,IAAI,CAAC,4GAAI,CAAC,WAAW,CAAC;wBAE9B,4EAA4E;wBAC5E,OAAO,IAAI,OAAO,CAAC,mBAAmB;wBACtC;oBACF,KAAK;wBACH,IAAI,mBAAmB;4BACrB,QAAQ,IAAI,CAAC,4GAAI,CAAC,sBAAsB,CAAC;4BACzC,OAAO,IAAI,OAAO,CAAC,mBAAmB;wBACxC;gBACF;YACF;YAEA,iBAAiB,QAAQ,MAAM,GAAG,IAAI,gHAAM,CAAC,QAAQ,CAAC,SAAS,gJAAK,CAAC,IAAI,IAAI,OAAO,CAAC,EAAE;YAEvF,MAAM,eAAe,gHAAM,CAAC,QAAQ,CAAC,gBAAgB;gBACnD;gBACA;YACF;YAEA,MAAM,WAAW;gBACf,QAAQ,IAAI,UAAU;gBACtB,YAAY,IAAI,aAAa;gBAC7B,SAAS,IAAI,+JAAY,CAAC,IAAI,OAAO;gBACrC;gBACA,SAAS;YACX;YAEA,IAAI,iBAAiB,UAAU;gBAC7B,SAAS,IAAI,GAAG;gBAChB,+JAAO,SAAS,QAAQ;YAC1B,OAAO;gBACL,MAAM,iBAAiB,EAAE;gBACzB,IAAI,qBAAqB;gBAEzB,eAAe,EAAE,CAAC,QAAQ,SAAS,iBAAiB,KAAK;oBACvD,eAAe,IAAI,CAAC;oBACpB,sBAAsB,MAAM,MAAM;oBAElC,6EAA6E;oBAC7E,IAAI,OAAO,gBAAgB,GAAG,CAAC,KAAK,qBAAqB,OAAO,gBAAgB,EAAE;wBAChF,6EAA6E;wBAC7E,WAAW;wBACX,eAAe,OAAO;wBACtB,OAAO,IAAI,6JAAU,CAAC,8BAA8B,OAAO,gBAAgB,GAAG,aAC5E,6JAAU,CAAC,gBAAgB,EAAE,QAAQ;oBACzC;gBACF;gBAEA,eAAe,EAAE,CAAC,WAAW,SAAS;oBACpC,IAAI,UAAU;wBACZ;oBACF;oBAEA,MAAM,MAAM,IAAI,6JAAU,CACxB,2BACA,6JAAU,CAAC,gBAAgB,EAC3B,QACA;oBAEF,eAAe,OAAO,CAAC;oBACvB,OAAO;gBACT;gBAEA,eAAe,EAAE,CAAC,SAAS,SAAS,kBAAkB,GAAG;oBACvD,IAAI,IAAI,SAAS,EAAE;oBACnB,OAAO,6JAAU,CAAC,IAAI,CAAC,KAAK,MAAM,QAAQ;gBAC5C;gBAEA,eAAe,EAAE,CAAC,OAAO,SAAS;oBAChC,IAAI;wBACF,IAAI,eAAe,eAAe,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,GAAG,OAAO,MAAM,CAAC;wBACnF,IAAI,iBAAiB,eAAe;4BAClC,eAAe,aAAa,QAAQ,CAAC;4BACrC,IAAI,CAAC,oBAAoB,qBAAqB,QAAQ;gCACpD,eAAe,gJAAK,CAAC,QAAQ,CAAC;4BAChC;wBACF;wBACA,SAAS,IAAI,GAAG;oBAClB,EAAE,OAAO,KAAK;wBACZ,OAAO,OAAO,6JAAU,CAAC,IAAI,CAAC,KAAK,MAAM,QAAQ,SAAS,OAAO,EAAE;oBACrE;oBACA,+JAAO,SAAS,QAAQ;gBAC1B;YACF;YAEA,QAAQ,IAAI,CAAC,SAAS,CAAA;gBACpB,IAAI,CAAC,eAAe,SAAS,EAAE;oBAC7B,eAAe,IAAI,CAAC,SAAS;oBAC7B,eAAe,OAAO;gBACxB;YACF;QACF;QAEA,QAAQ,IAAI,CAAC,SAAS,CAAA;YACpB,OAAO;YACP,IAAI,OAAO,CAAC;QACd;QAEA,gBAAgB;QAChB,IAAI,EAAE,CAAC,SAAS,SAAS,mBAAmB,GAAG;YAC7C,eAAe;YACf,gFAAgF;YAChF,OAAO,6JAAU,CAAC,IAAI,CAAC,KAAK,MAAM,QAAQ;QAC5C;QAEA,wDAAwD;QACxD,IAAI,EAAE,CAAC,UAAU,SAAS,oBAAoB,MAAM;YAClD,qDAAqD;YACrD,OAAO,YAAY,CAAC,MAAM,OAAO;QACnC;QAEA,yBAAyB;QACzB,IAAI,OAAO,OAAO,EAAE;YAClB,qGAAqG;YACrG,MAAM,UAAU,SAAS,OAAO,OAAO,EAAE;YAEzC,IAAI,OAAO,KAAK,CAAC,UAAU;gBACzB,OAAO,IAAI,6JAAU,CACnB,iDACA,6JAAU,CAAC,oBAAoB,EAC/B,QACA;gBAGF;YACF;YAEA,wHAAwH;YACxH,kIAAkI;YAClI,oIAAoI;YACpI,8EAA8E;YAC9E,kIAAkI;YAClI,IAAI,UAAU,CAAC,SAAS,SAAS;gBAC/B,IAAI,QAAQ;gBACZ,IAAI,sBAAsB,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO,GAAG,gBAAgB;gBAC5F,MAAM,eAAe,OAAO,YAAY,IAAI,mKAAoB;gBAChE,IAAI,OAAO,mBAAmB,EAAE;oBAC9B,sBAAsB,OAAO,mBAAmB;gBAClD;gBACA,OAAO,IAAI,6JAAU,CACnB,qBACA,aAAa,mBAAmB,GAAG,6JAAU,CAAC,SAAS,GAAG,6JAAU,CAAC,YAAY,EACjF,QACA;gBAEF;YACF;QACF;QAGA,mBAAmB;QACnB,IAAI,gJAAK,CAAC,QAAQ,CAAC,OAAO;YACxB,IAAI,QAAQ;YACZ,IAAI,UAAU;YAEd,KAAK,EAAE,CAAC,OAAO;gBACb,QAAQ;YACV;YAEA,KAAK,IAAI,CAAC,SAAS,CAAA;gBACjB,UAAU;gBACV,IAAI,OAAO,CAAC;YACd;YAEA,KAAK,EAAE,CAAC,SAAS;gBACf,IAAI,CAAC,SAAS,CAAC,SAAS;oBACtB,MAAM,IAAI,kKAAa,CAAC,mCAAmC,QAAQ;gBACrE;YACF;YAEA,KAAK,IAAI,CAAC;QACZ,OAAO;YACL,IAAI,GAAG,CAAC;QACV;IACF;AACF;AAEO,MAAM,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/isURLSameOrigin.js"], "sourcesContent": ["import platform from '../platform/index.js';\n\nexport default platform.hasStandardBrowserEnv ? ((origin, isMSIE) => (url) => {\n  url = new URL(url, platform.origin);\n\n  return (\n    origin.protocol === url.protocol &&\n    origin.host === url.host &&\n    (isMSIE || origin.port === url.port)\n  );\n})(\n  new URL(platform.origin),\n  platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)\n) : () => true;\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,4JAAQ,CAAC,qBAAqB,GAAG,CAAC,CAAC,QAAQ,SAAW,CAAC;QACpE,MAAM,IAAI,IAAI,KAAK,4JAAQ,CAAC,MAAM;QAElC,OACE,OAAO,QAAQ,KAAK,IAAI,QAAQ,IAChC,OAAO,IAAI,KAAK,IAAI,IAAI,IACxB,CAAC,UAAU,OAAO,IAAI,KAAK,IAAI,IAAI;IAEvC,CAAC,EACC,IAAI,IAAI,4JAAQ,CAAC,MAAM,GACvB,4JAAQ,CAAC,SAAS,IAAI,kBAAkB,IAAI,CAAC,4JAAQ,CAAC,SAAS,CAAC,SAAS,KACvE,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/cookies.js"], "sourcesContent": ["import utils from './../utils.js';\nimport platform from '../platform/index.js';\n\nexport default platform.hasStandardBrowserEnv ?\n\n  // Standard browser envs support document.cookie\n  {\n    write(name, value, expires, path, domain, secure) {\n      const cookie = [name + '=' + encodeURIComponent(value)];\n\n      utils.isNumber(expires) && cookie.push('expires=' + new Date(expires).toGMTString());\n\n      utils.isString(path) && cookie.push('path=' + path);\n\n      utils.isString(domain) && cookie.push('domain=' + domain);\n\n      secure === true && cookie.push('secure');\n\n      document.cookie = cookie.join('; ');\n    },\n\n    read(name) {\n      const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n      return (match ? decodeURIComponent(match[3]) : null);\n    },\n\n    remove(name) {\n      this.write(name, '', Date.now() - 86400000);\n    }\n  }\n\n  :\n\n  // Non-standard browser env (web workers, react-native) lack needed support.\n  {\n    write() {},\n    read() {\n      return null;\n    },\n    remove() {}\n  };\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,4JAAQ,CAAC,qBAAqB,GAE3C,gDAAgD;AAChD;IACE,OAAM,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM;QAC9C,MAAM,SAAS;YAAC,OAAO,MAAM,mBAAmB;SAAO;QAEvD,gJAAK,CAAC,QAAQ,CAAC,YAAY,OAAO,IAAI,CAAC,aAAa,IAAI,KAAK,SAAS,WAAW;QAEjF,gJAAK,CAAC,QAAQ,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU;QAE9C,gJAAK,CAAC,QAAQ,CAAC,WAAW,OAAO,IAAI,CAAC,YAAY;QAElD,WAAW,QAAQ,OAAO,IAAI,CAAC;QAE/B,SAAS,MAAM,GAAG,OAAO,IAAI,CAAC;IAChC;IAEA,MAAK,IAAI;QACP,MAAM,QAAQ,SAAS,MAAM,CAAC,KAAK,CAAC,IAAI,OAAO,eAAe,OAAO;QACrE,OAAQ,QAAQ,mBAAmB,KAAK,CAAC,EAAE,IAAI;IACjD;IAEA,QAAO,IAAI;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,GAAG,KAAK;IACpC;AACF,IAIA,4EAA4E;AAC5E;IACE,UAAS;IACT;QACE,OAAO;IACT;IACA,WAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/mergeConfig.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosHeaders from \"./AxiosHeaders.js\";\n\nconst headersToObject = (thing) => thing instanceof AxiosHeaders ? { ...thing } : thing;\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  const config = {};\n\n  function getMergedValue(target, source, prop, caseless) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge.call({caseless}, target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(a, b, prop , caseless) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(a, b, prop , caseless);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a, prop , caseless);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(a, b, prop) {\n    if (prop in config2) {\n      return getMergedValue(a, b);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  const mergeMap = {\n    url: valueFromConfig2,\n    method: valueFromConfig2,\n    data: valueFromConfig2,\n    baseURL: defaultToConfig2,\n    transformRequest: defaultToConfig2,\n    transformResponse: defaultToConfig2,\n    paramsSerializer: defaultToConfig2,\n    timeout: defaultToConfig2,\n    timeoutMessage: defaultToConfig2,\n    withCredentials: defaultToConfig2,\n    withXSRFToken: defaultToConfig2,\n    adapter: defaultToConfig2,\n    responseType: defaultToConfig2,\n    xsrfCookieName: defaultToConfig2,\n    xsrfHeaderName: defaultToConfig2,\n    onUploadProgress: defaultToConfig2,\n    onDownloadProgress: defaultToConfig2,\n    decompress: defaultToConfig2,\n    maxContentLength: defaultToConfig2,\n    maxBodyLength: defaultToConfig2,\n    beforeRedirect: defaultToConfig2,\n    transport: defaultToConfig2,\n    httpAgent: defaultToConfig2,\n    httpsAgent: defaultToConfig2,\n    cancelToken: defaultToConfig2,\n    socketPath: defaultToConfig2,\n    responseEncoding: defaultToConfig2,\n    validateStatus: mergeDirectKeys,\n    headers: (a, b , prop) => mergeDeepProperties(headersToObject(a), headersToObject(b),prop, true)\n  };\n\n  utils.forEach(Object.keys({...config1, ...config2}), function computeConfigValue(prop) {\n    const merge = mergeMap[prop] || mergeDeepProperties;\n    const configValue = merge(config1[prop], config2[prop], prop);\n    (utils.isUndefined(configValue) && merge !== mergeDirectKeys) || (config[prop] = configValue);\n  });\n\n  return config;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAKA,MAAM,kBAAkB,CAAC,QAAU,iBAAiB,+JAAY,GAAG;QAAE,GAAG,KAAK;IAAC,IAAI;AAWnE,SAAS,YAAY,OAAO,EAAE,OAAO;IAClD,6CAA6C;IAC7C,UAAU,WAAW,CAAC;IACtB,MAAM,SAAS,CAAC;IAEhB,SAAS,eAAe,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ;QACpD,IAAI,gJAAK,CAAC,aAAa,CAAC,WAAW,gJAAK,CAAC,aAAa,CAAC,SAAS;YAC9D,OAAO,gJAAK,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAC;YAAQ,GAAG,QAAQ;QAC9C,OAAO,IAAI,gJAAK,CAAC,aAAa,CAAC,SAAS;YACtC,OAAO,gJAAK,CAAC,KAAK,CAAC,CAAC,GAAG;QACzB,OAAO,IAAI,gJAAK,CAAC,OAAO,CAAC,SAAS;YAChC,OAAO,OAAO,KAAK;QACrB;QACA,OAAO;IACT;IAEA,6CAA6C;IAC7C,SAAS,oBAAoB,CAAC,EAAE,CAAC,EAAE,IAAI,EAAG,QAAQ;QAChD,IAAI,CAAC,gJAAK,CAAC,WAAW,CAAC,IAAI;YACzB,OAAO,eAAe,GAAG,GAAG,MAAO;QACrC,OAAO,IAAI,CAAC,gJAAK,CAAC,WAAW,CAAC,IAAI;YAChC,OAAO,eAAe,WAAW,GAAG,MAAO;QAC7C;IACF;IAEA,6CAA6C;IAC7C,SAAS,iBAAiB,CAAC,EAAE,CAAC;QAC5B,IAAI,CAAC,gJAAK,CAAC,WAAW,CAAC,IAAI;YACzB,OAAO,eAAe,WAAW;QACnC;IACF;IAEA,6CAA6C;IAC7C,SAAS,iBAAiB,CAAC,EAAE,CAAC;QAC5B,IAAI,CAAC,gJAAK,CAAC,WAAW,CAAC,IAAI;YACzB,OAAO,eAAe,WAAW;QACnC,OAAO,IAAI,CAAC,gJAAK,CAAC,WAAW,CAAC,IAAI;YAChC,OAAO,eAAe,WAAW;QACnC;IACF;IAEA,6CAA6C;IAC7C,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,IAAI;QACjC,IAAI,QAAQ,SAAS;YACnB,OAAO,eAAe,GAAG;QAC3B,OAAO,IAAI,QAAQ,SAAS;YAC1B,OAAO,eAAe,WAAW;QACnC;IACF;IAEA,MAAM,WAAW;QACf,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;QACT,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,SAAS;QACT,gBAAgB;QAChB,iBAAiB;QACjB,eAAe;QACf,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,oBAAoB;QACpB,YAAY;QACZ,kBAAkB;QAClB,eAAe;QACf,gBAAgB;QAChB,WAAW;QACX,WAAW;QACX,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,gBAAgB;QAChB,SAAS,CAAC,GAAG,GAAI,OAAS,oBAAoB,gBAAgB,IAAI,gBAAgB,IAAG,MAAM;IAC7F;IAEA,gJAAK,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC;QAAC,GAAG,OAAO;QAAE,GAAG,OAAO;IAAA,IAAI,SAAS,mBAAmB,IAAI;QACnF,MAAM,QAAQ,QAAQ,CAAC,KAAK,IAAI;QAChC,MAAM,cAAc,MAAM,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE;QACvD,gJAAK,CAAC,WAAW,CAAC,gBAAgB,UAAU,mBAAoB,CAAC,MAAM,CAAC,KAAK,GAAG,WAAW;IAC9F;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/resolveConfig.js"], "sourcesContent": ["import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport isURLSameOrigin from \"./isURLSameOrigin.js\";\nimport cookies from \"./cookies.js\";\nimport buildFullPath from \"../core/buildFullPath.js\";\nimport mergeConfig from \"../core/mergeConfig.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport buildURL from \"./buildURL.js\";\n\nexport default (config) => {\n  const newConfig = mergeConfig({}, config);\n\n  let {data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth} = newConfig;\n\n  newConfig.headers = headers = AxiosHeaders.from(headers);\n\n  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);\n\n  // HTTP basic authentication\n  if (auth) {\n    headers.set('Authorization', 'Basic ' +\n      btoa((auth.username || '') + ':' + (auth.password ? unescape(encodeURIComponent(auth.password)) : ''))\n    );\n  }\n\n  let contentType;\n\n  if (utils.isFormData(data)) {\n    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n      headers.setContentType(undefined); // Let the browser set it\n    } else if ((contentType = headers.getContentType()) !== false) {\n      // fix semicolon duplication issue for ReactNative FormData implementation\n      const [type, ...tokens] = contentType ? contentType.split(';').map(token => token.trim()).filter(Boolean) : [];\n      headers.setContentType([type || 'multipart/form-data', ...tokens].join('; '));\n    }\n  }\n\n  // Add xsrf header\n  // This is only done if running in a standard browser environment.\n  // Specifically not if we're in a web worker, or react-native.\n\n  if (platform.hasStandardBrowserEnv) {\n    withXSRFToken && utils.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n\n    if (withXSRFToken || (withXSRFToken !== false && isURLSameOrigin(newConfig.url))) {\n      // Add xsrf header\n      const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n\n      if (xsrfValue) {\n        headers.set(xsrfHeaderName, xsrfValue);\n      }\n    }\n  }\n\n  return newConfig;\n}\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;uCAEe,CAAC;IACd,MAAM,YAAY,IAAA,8JAAW,EAAC,CAAC,GAAG;IAElC,IAAI,EAAC,IAAI,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAC,GAAG;IAE3E,UAAU,OAAO,GAAG,UAAU,+JAAY,CAAC,IAAI,CAAC;IAEhD,UAAU,GAAG,GAAG,IAAA,8JAAQ,EAAC,IAAA,gKAAa,EAAC,UAAU,OAAO,EAAE,UAAU,GAAG,EAAE,UAAU,iBAAiB,GAAG,OAAO,MAAM,EAAE,OAAO,gBAAgB;IAE7I,4BAA4B;IAC5B,IAAI,MAAM;QACR,QAAQ,GAAG,CAAC,iBAAiB,WAC3B,KAAK,CAAC,KAAK,QAAQ,IAAI,EAAE,IAAI,MAAM,CAAC,KAAK,QAAQ,GAAG,SAAS,mBAAmB,KAAK,QAAQ,KAAK,EAAE;IAExG;IAEA,IAAI;IAEJ,IAAI,gJAAK,CAAC,UAAU,CAAC,OAAO;QAC1B,IAAI,4JAAQ,CAAC,qBAAqB,IAAI,4JAAQ,CAAC,8BAA8B,EAAE;YAC7E,QAAQ,cAAc,CAAC,YAAY,yBAAyB;QAC9D,OAAO,IAAI,CAAC,cAAc,QAAQ,cAAc,EAAE,MAAM,OAAO;YAC7D,0EAA0E;YAC1E,MAAM,CAAC,MAAM,GAAG,OAAO,GAAG,cAAc,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;YAC9G,QAAQ,cAAc,CAAC;gBAAC,QAAQ;mBAA0B;aAAO,CAAC,IAAI,CAAC;QACzE;IACF;IAEA,kBAAkB;IAClB,kEAAkE;IAClE,8DAA8D;IAE9D,IAAI,4JAAQ,CAAC,qBAAqB,EAAE;QAClC,iBAAiB,gJAAK,CAAC,UAAU,CAAC,kBAAkB,CAAC,gBAAgB,cAAc,UAAU;QAE7F,IAAI,iBAAkB,kBAAkB,SAAS,IAAA,qKAAe,EAAC,UAAU,GAAG,GAAI;YAChF,kBAAkB;YAClB,MAAM,YAAY,kBAAkB,kBAAkB,6JAAO,CAAC,IAAI,CAAC;YAEnE,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,gBAAgB;YAC9B;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/adapters/xhr.js"], "sourcesContent": ["import utils from './../utils.js';\nimport settle from './../core/settle.js';\nimport transitionalDefaults from '../defaults/transitional.js';\nimport AxiosError from '../core/AxiosError.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport parseProtocol from '../helpers/parseProtocol.js';\nimport platform from '../platform/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport {progressEventReducer} from '../helpers/progressEventReducer.js';\nimport resolveConfig from \"../helpers/resolveConfig.js\";\n\nconst isXHRAdapterSupported = typeof XMLHttpRequest !== 'undefined';\n\nexport default isXHRAdapterSupported && function (config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    const _config = resolveConfig(config);\n    let requestData = _config.data;\n    const requestHeaders = AxiosHeaders.from(_config.headers).normalize();\n    let {responseType, onUploadProgress, onDownloadProgress} = _config;\n    let onCanceled;\n    let uploadThrottled, downloadThrottled;\n    let flushUpload, flushDownload;\n\n    function done() {\n      flushUpload && flushUpload(); // flush events\n      flushDownload && flushDownload(); // flush events\n\n      _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);\n\n      _config.signal && _config.signal.removeEventListener('abort', onCanceled);\n    }\n\n    let request = new XMLHttpRequest();\n\n    request.open(_config.method.toUpperCase(), _config.url, true);\n\n    // Set the request timeout in MS\n    request.timeout = _config.timeout;\n\n    function onloadend() {\n      if (!request) {\n        return;\n      }\n      // Prepare the response\n      const responseHeaders = AxiosHeaders.from(\n        'getAllResponseHeaders' in request && request.getAllResponseHeaders()\n      );\n      const responseData = !responseType || responseType === 'text' || responseType === 'json' ?\n        request.responseText : request.response;\n      const response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config,\n        request\n      };\n\n      settle(function _resolve(value) {\n        resolve(value);\n        done();\n      }, function _reject(err) {\n        reject(err);\n        done();\n      }, response);\n\n      // Clean up request\n      request = null;\n    }\n\n    if ('onloadend' in request) {\n      // Use onloadend if available\n      request.onloadend = onloadend;\n    } else {\n      // Listen for ready state to emulate onloadend\n      request.onreadystatechange = function handleLoad() {\n        if (!request || request.readyState !== 4) {\n          return;\n        }\n\n        // The request errored out and we didn't get a response, this will be\n        // handled by onerror instead\n        // With one exception: request that using file: protocol, most browsers\n        // will return status as 0 even though it's a successful request\n        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n          return;\n        }\n        // readystate handler is calling before onerror or ontimeout handlers,\n        // so we should call onloadend on the next 'tick'\n        setTimeout(onloadend);\n      };\n    }\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(new AxiosError('Request aborted', AxiosError.ECONNABORTED, config, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      let timeoutErrorMessage = _config.timeout ? 'timeout of ' + _config.timeout + 'ms exceeded' : 'timeout exceeded';\n      const transitional = _config.transitional || transitionalDefaults;\n      if (_config.timeoutErrorMessage) {\n        timeoutErrorMessage = _config.timeoutErrorMessage;\n      }\n      reject(new AxiosError(\n        timeoutErrorMessage,\n        transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,\n        config,\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Remove Content-Type if data is undefined\n    requestData === undefined && requestHeaders.setContentType(null);\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {\n        request.setRequestHeader(key, val);\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(_config.withCredentials)) {\n      request.withCredentials = !!_config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (responseType && responseType !== 'json') {\n      request.responseType = _config.responseType;\n    }\n\n    // Handle progress if needed\n    if (onDownloadProgress) {\n      ([downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true));\n      request.addEventListener('progress', downloadThrottled);\n    }\n\n    // Not all browsers support upload events\n    if (onUploadProgress && request.upload) {\n      ([uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress));\n\n      request.upload.addEventListener('progress', uploadThrottled);\n\n      request.upload.addEventListener('loadend', flushUpload);\n    }\n\n    if (_config.cancelToken || _config.signal) {\n      // Handle cancellation\n      // eslint-disable-next-line func-names\n      onCanceled = cancel => {\n        if (!request) {\n          return;\n        }\n        reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);\n        request.abort();\n        request = null;\n      };\n\n      _config.cancelToken && _config.cancelToken.subscribe(onCanceled);\n      if (_config.signal) {\n        _config.signal.aborted ? onCanceled() : _config.signal.addEventListener('abort', onCanceled);\n      }\n    }\n\n    const protocol = parseProtocol(_config.url);\n\n    if (protocol && platform.protocols.indexOf(protocol) === -1) {\n      reject(new AxiosError('Unsupported protocol ' + protocol + ':', AxiosError.ERR_BAD_REQUEST, config));\n      return;\n    }\n\n\n    // Send the request\n    request.send(requestData || null);\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,wBAAwB,OAAO,mBAAmB;uCAEzC,yBAAyB,SAAU,MAAM;IACtD,OAAO,IAAI,QAAQ,SAAS,mBAAmB,OAAO,EAAE,MAAM;QAC5D,MAAM,UAAU,IAAA,mKAAa,EAAC;QAC9B,IAAI,cAAc,QAAQ,IAAI;QAC9B,MAAM,iBAAiB,+JAAY,CAAC,IAAI,CAAC,QAAQ,OAAO,EAAE,SAAS;QACnE,IAAI,EAAC,YAAY,EAAE,gBAAgB,EAAE,kBAAkB,EAAC,GAAG;QAC3D,IAAI;QACJ,IAAI,iBAAiB;QACrB,IAAI,aAAa;QAEjB,SAAS;YACP,eAAe,eAAe,eAAe;YAC7C,iBAAiB,iBAAiB,eAAe;YAEjD,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,WAAW,CAAC;YAEvD,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,mBAAmB,CAAC,SAAS;QAChE;QAEA,IAAI,UAAU,IAAI;QAElB,QAAQ,IAAI,CAAC,QAAQ,MAAM,CAAC,WAAW,IAAI,QAAQ,GAAG,EAAE;QAExD,gCAAgC;QAChC,QAAQ,OAAO,GAAG,QAAQ,OAAO;QAEjC,SAAS;YACP,IAAI,CAAC,SAAS;gBACZ;YACF;YACA,uBAAuB;YACvB,MAAM,kBAAkB,+JAAY,CAAC,IAAI,CACvC,2BAA2B,WAAW,QAAQ,qBAAqB;YAErE,MAAM,eAAe,CAAC,gBAAgB,iBAAiB,UAAU,iBAAiB,SAChF,QAAQ,YAAY,GAAG,QAAQ,QAAQ;YACzC,MAAM,WAAW;gBACf,MAAM;gBACN,QAAQ,QAAQ,MAAM;gBACtB,YAAY,QAAQ,UAAU;gBAC9B,SAAS;gBACT;gBACA;YACF;YAEA,IAAA,yJAAM,EAAC,SAAS,SAAS,KAAK;gBAC5B,QAAQ;gBACR;YACF,GAAG,SAAS,QAAQ,GAAG;gBACrB,OAAO;gBACP;YACF,GAAG;YAEH,mBAAmB;YACnB,UAAU;QACZ;QAEA,IAAI,eAAe,SAAS;YAC1B,6BAA6B;YAC7B,QAAQ,SAAS,GAAG;QACtB,OAAO;YACL,8CAA8C;YAC9C,QAAQ,kBAAkB,GAAG,SAAS;gBACpC,IAAI,CAAC,WAAW,QAAQ,UAAU,KAAK,GAAG;oBACxC;gBACF;gBAEA,qEAAqE;gBACrE,6BAA6B;gBAC7B,uEAAuE;gBACvE,gEAAgE;gBAChE,IAAI,QAAQ,MAAM,KAAK,KAAK,CAAC,CAAC,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG;oBAChG;gBACF;gBACA,sEAAsE;gBACtE,iDAAiD;gBACjD,WAAW;YACb;QACF;QAEA,4EAA4E;QAC5E,QAAQ,OAAO,GAAG,SAAS;YACzB,IAAI,CAAC,SAAS;gBACZ;YACF;YAEA,OAAO,IAAI,6JAAU,CAAC,mBAAmB,6JAAU,CAAC,YAAY,EAAE,QAAQ;YAE1E,mBAAmB;YACnB,UAAU;QACZ;QAEA,kCAAkC;QAClC,QAAQ,OAAO,GAAG,SAAS;YACzB,gDAAgD;YAChD,mDAAmD;YACnD,OAAO,IAAI,6JAAU,CAAC,iBAAiB,6JAAU,CAAC,WAAW,EAAE,QAAQ;YAEvE,mBAAmB;YACnB,UAAU;QACZ;QAEA,iBAAiB;QACjB,QAAQ,SAAS,GAAG,SAAS;YAC3B,IAAI,sBAAsB,QAAQ,OAAO,GAAG,gBAAgB,QAAQ,OAAO,GAAG,gBAAgB;YAC9F,MAAM,eAAe,QAAQ,YAAY,IAAI,mKAAoB;YACjE,IAAI,QAAQ,mBAAmB,EAAE;gBAC/B,sBAAsB,QAAQ,mBAAmB;YACnD;YACA,OAAO,IAAI,6JAAU,CACnB,qBACA,aAAa,mBAAmB,GAAG,6JAAU,CAAC,SAAS,GAAG,6JAAU,CAAC,YAAY,EACjF,QACA;YAEF,mBAAmB;YACnB,UAAU;QACZ;QAEA,2CAA2C;QAC3C,gBAAgB,aAAa,eAAe,cAAc,CAAC;QAE3D,6BAA6B;QAC7B,IAAI,sBAAsB,SAAS;YACjC,gJAAK,CAAC,OAAO,CAAC,eAAe,MAAM,IAAI,SAAS,iBAAiB,GAAG,EAAE,GAAG;gBACvE,QAAQ,gBAAgB,CAAC,KAAK;YAChC;QACF;QAEA,2CAA2C;QAC3C,IAAI,CAAC,gJAAK,CAAC,WAAW,CAAC,QAAQ,eAAe,GAAG;YAC/C,QAAQ,eAAe,GAAG,CAAC,CAAC,QAAQ,eAAe;QACrD;QAEA,wCAAwC;QACxC,IAAI,gBAAgB,iBAAiB,QAAQ;YAC3C,QAAQ,YAAY,GAAG,QAAQ,YAAY;QAC7C;QAEA,4BAA4B;QAC5B,IAAI,oBAAoB;YACrB,CAAC,mBAAmB,cAAc,GAAG,IAAA,uLAAoB,EAAC,oBAAoB;YAC/E,QAAQ,gBAAgB,CAAC,YAAY;QACvC;QAEA,yCAAyC;QACzC,IAAI,oBAAoB,QAAQ,MAAM,EAAE;YACrC,CAAC,iBAAiB,YAAY,GAAG,IAAA,uLAAoB,EAAC;YAEvD,QAAQ,MAAM,CAAC,gBAAgB,CAAC,YAAY;YAE5C,QAAQ,MAAM,CAAC,gBAAgB,CAAC,WAAW;QAC7C;QAEA,IAAI,QAAQ,WAAW,IAAI,QAAQ,MAAM,EAAE;YACzC,sBAAsB;YACtB,sCAAsC;YACtC,aAAa,CAAA;gBACX,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,OAAO,CAAC,UAAU,OAAO,IAAI,GAAG,IAAI,kKAAa,CAAC,MAAM,QAAQ,WAAW;gBAC3E,QAAQ,KAAK;gBACb,UAAU;YACZ;YAEA,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,SAAS,CAAC;YACrD,IAAI,QAAQ,MAAM,EAAE;gBAClB,QAAQ,MAAM,CAAC,OAAO,GAAG,eAAe,QAAQ,MAAM,CAAC,gBAAgB,CAAC,SAAS;YACnF;QACF;QAEA,MAAM,WAAW,IAAA,mKAAa,EAAC,QAAQ,GAAG;QAE1C,IAAI,YAAY,4JAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG;YAC3D,OAAO,IAAI,6JAAU,CAAC,0BAA0B,WAAW,KAAK,6JAAU,CAAC,eAAe,EAAE;YAC5F;QACF;QAGA,mBAAmB;QACnB,QAAQ,IAAI,CAAC,eAAe;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/composeSignals.js"], "sourcesContent": ["import CanceledError from \"../cancel/CanceledError.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport utils from '../utils.js';\n\nconst composeSignals = (signals, timeout) => {\n  const {length} = (signals = signals ? signals.filter(Boolean) : []);\n\n  if (timeout || length) {\n    let controller = new AbortController();\n\n    let aborted;\n\n    const onabort = function (reason) {\n      if (!aborted) {\n        aborted = true;\n        unsubscribe();\n        const err = reason instanceof Error ? reason : this.reason;\n        controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n      }\n    }\n\n    let timer = timeout && setTimeout(() => {\n      timer = null;\n      onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT))\n    }, timeout)\n\n    const unsubscribe = () => {\n      if (signals) {\n        timer && clearTimeout(timer);\n        timer = null;\n        signals.forEach(signal => {\n          signal.unsubscribe ? signal.unsubscribe(onabort) : signal.removeEventListener('abort', onabort);\n        });\n        signals = null;\n      }\n    }\n\n    signals.forEach((signal) => signal.addEventListener('abort', onabort));\n\n    const {signal} = controller;\n\n    signal.unsubscribe = () => utils.asap(unsubscribe);\n\n    return signal;\n  }\n}\n\nexport default composeSignals;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,iBAAiB,CAAC,SAAS;IAC/B,MAAM,EAAC,MAAM,EAAC,GAAI,UAAU,UAAU,QAAQ,MAAM,CAAC,WAAW,EAAE;IAElE,IAAI,WAAW,QAAQ;QACrB,IAAI,aAAa,IAAI;QAErB,IAAI;QAEJ,MAAM,UAAU,SAAU,MAAM;YAC9B,IAAI,CAAC,SAAS;gBACZ,UAAU;gBACV;gBACA,MAAM,MAAM,kBAAkB,QAAQ,SAAS,IAAI,CAAC,MAAM;gBAC1D,WAAW,KAAK,CAAC,eAAe,6JAAU,GAAG,MAAM,IAAI,kKAAa,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC5G;QACF;QAEA,IAAI,QAAQ,WAAW,WAAW;YAChC,QAAQ;YACR,QAAQ,IAAI,6JAAU,CAAC,CAAC,QAAQ,EAAE,QAAQ,eAAe,CAAC,EAAE,6JAAU,CAAC,SAAS;QAClF,GAAG;QAEH,MAAM,cAAc;YAClB,IAAI,SAAS;gBACX,SAAS,aAAa;gBACtB,QAAQ;gBACR,QAAQ,OAAO,CAAC,CAAA;oBACd,OAAO,WAAW,GAAG,OAAO,WAAW,CAAC,WAAW,OAAO,mBAAmB,CAAC,SAAS;gBACzF;gBACA,UAAU;YACZ;QACF;QAEA,QAAQ,OAAO,CAAC,CAAC,SAAW,OAAO,gBAAgB,CAAC,SAAS;QAE7D,MAAM,EAAC,MAAM,EAAC,GAAG;QAEjB,OAAO,WAAW,GAAG,IAAM,gJAAK,CAAC,IAAI,CAAC;QAEtC,OAAO;IACT;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/trackStream.js"], "sourcesContent": ["\nexport const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n\n  let pos = 0;\n  let end;\n\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n}\n\nexport const readBytes = async function* (iterable, chunkSize) {\n  for await (const chunk of readStream(iterable)) {\n    yield* streamChunk(chunk, chunkSize);\n  }\n}\n\nconst readStream = async function* (stream) {\n  if (stream[Symbol.asyncIterator]) {\n    yield* stream;\n    return;\n  }\n\n  const reader = stream.getReader();\n  try {\n    for (;;) {\n      const {done, value} = await reader.read();\n      if (done) {\n        break;\n      }\n      yield value;\n    }\n  } finally {\n    await reader.cancel();\n  }\n}\n\nexport const trackStream = (stream, chunkSize, onProgress, onFinish) => {\n  const iterator = readBytes(stream, chunkSize);\n\n  let bytes = 0;\n  let done;\n  let _onFinish = (e) => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  }\n\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {done, value} = await iterator.next();\n\n        if (done) {\n         _onFinish();\n          controller.close();\n          return;\n        }\n\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;AACO,MAAM,cAAc,UAAW,KAAK,EAAE,SAAS;IACpD,IAAI,MAAM,MAAM,UAAU;IAE1B,IAAI,CAAC,aAAa,MAAM,WAAW;QACjC,MAAM;QACN;IACF;IAEA,IAAI,MAAM;IACV,IAAI;IAEJ,MAAO,MAAM,IAAK;QAChB,MAAM,MAAM;QACZ,MAAM,MAAM,KAAK,CAAC,KAAK;QACvB,MAAM;IACR;AACF;AAEO,MAAM,YAAY,gBAAiB,QAAQ,EAAE,SAAS;IAC3D,WAAW,MAAM,SAAS,WAAW,UAAW;QAC9C,OAAO,YAAY,OAAO;IAC5B;AACF;AAEA,MAAM,aAAa,gBAAiB,MAAM;IACxC,IAAI,MAAM,CAAC,OAAO,aAAa,CAAC,EAAE;QAChC,OAAO;QACP;IACF;IAEA,MAAM,SAAS,OAAO,SAAS;IAC/B,IAAI;QACF,OAAS;YACP,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM,OAAO,IAAI;YACvC,IAAI,MAAM;gBACR;YACF;YACA,MAAM;QACR;IACF,SAAU;QACR,MAAM,OAAO,MAAM;IACrB;AACF;AAEO,MAAM,cAAc,CAAC,QAAQ,WAAW,YAAY;IACzD,MAAM,WAAW,UAAU,QAAQ;IAEnC,IAAI,QAAQ;IACZ,IAAI;IACJ,IAAI,YAAY,CAAC;QACf,IAAI,CAAC,MAAM;YACT,OAAO;YACP,YAAY,SAAS;QACvB;IACF;IAEA,OAAO,IAAI,eAAe;QACxB,MAAM,MAAK,UAAU;YACnB,IAAI;gBACF,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM,SAAS,IAAI;gBAEzC,IAAI,MAAM;oBACT;oBACC,WAAW,KAAK;oBAChB;gBACF;gBAEA,IAAI,MAAM,MAAM,UAAU;gBAC1B,IAAI,YAAY;oBACd,IAAI,cAAc,SAAS;oBAC3B,WAAW;gBACb;gBACA,WAAW,OAAO,CAAC,IAAI,WAAW;YACpC,EAAE,OAAO,KAAK;gBACZ,UAAU;gBACV,MAAM;YACR;QACF;QACA,QAAO,MAAM;YACX,UAAU;YACV,OAAO,SAAS,MAAM;QACxB;IACF,GAAG;QACD,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/adapters/fetch.js"], "sourcesContent": ["import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport composeSignals from \"../helpers/composeSignals.js\";\nimport {trackStream} from \"../helpers/trackStream.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport {progressEventReducer, progressEventDecorator, asyncDecorator} from \"../helpers/progressEventReducer.js\";\nimport resolveConfig from \"../helpers/resolveConfig.js\";\nimport settle from \"../core/settle.js\";\n\nconst isFetchSupported = typeof fetch === 'function' && typeof Request === 'function' && typeof Response === 'function';\nconst isReadableStreamSupported = isFetchSupported && typeof ReadableStream === 'function';\n\n// used only inside the fetch adapter\nconst encodeText = isFetchSupported && (typeof TextEncoder === 'function' ?\n    ((encoder) => (str) => encoder.encode(str))(new TextEncoder()) :\n    async (str) => new Uint8Array(await new Response(str).arrayBuffer())\n);\n\nconst test = (fn, ...args) => {\n  try {\n    return !!fn(...args);\n  } catch (e) {\n    return false\n  }\n}\n\nconst supportsRequestStream = isReadableStreamSupported && test(() => {\n  let duplexAccessed = false;\n\n  const hasContentType = new Request(platform.origin, {\n    body: new ReadableStream(),\n    method: 'POST',\n    get duplex() {\n      duplexAccessed = true;\n      return 'half';\n    },\n  }).headers.has('Content-Type');\n\n  return duplexAccessed && !hasContentType;\n});\n\nconst DEFAULT_CHUNK_SIZE = 64 * 1024;\n\nconst supportsResponseStream = isReadableStreamSupported &&\n  test(() => utils.isReadableStream(new Response('').body));\n\n\nconst resolvers = {\n  stream: supportsResponseStream && ((res) => res.body)\n};\n\nisFetchSupported && (((res) => {\n  ['text', 'arrayBuffer', 'blob', 'formData', 'stream'].forEach(type => {\n    !resolvers[type] && (resolvers[type] = utils.isFunction(res[type]) ? (res) => res[type]() :\n      (_, config) => {\n        throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);\n      })\n  });\n})(new Response));\n\nconst getBodyLength = async (body) => {\n  if (body == null) {\n    return 0;\n  }\n\n  if(utils.isBlob(body)) {\n    return body.size;\n  }\n\n  if(utils.isSpecCompliantForm(body)) {\n    const _request = new Request(platform.origin, {\n      method: 'POST',\n      body,\n    });\n    return (await _request.arrayBuffer()).byteLength;\n  }\n\n  if(utils.isArrayBufferView(body) || utils.isArrayBuffer(body)) {\n    return body.byteLength;\n  }\n\n  if(utils.isURLSearchParams(body)) {\n    body = body + '';\n  }\n\n  if(utils.isString(body)) {\n    return (await encodeText(body)).byteLength;\n  }\n}\n\nconst resolveBodyLength = async (headers, body) => {\n  const length = utils.toFiniteNumber(headers.getContentLength());\n\n  return length == null ? getBodyLength(body) : length;\n}\n\nexport default isFetchSupported && (async (config) => {\n  let {\n    url,\n    method,\n    data,\n    signal,\n    cancelToken,\n    timeout,\n    onDownloadProgress,\n    onUploadProgress,\n    responseType,\n    headers,\n    withCredentials = 'same-origin',\n    fetchOptions\n  } = resolveConfig(config);\n\n  responseType = responseType ? (responseType + '').toLowerCase() : 'text';\n\n  let composedSignal = composeSignals([signal, cancelToken && cancelToken.toAbortSignal()], timeout);\n\n  let request;\n\n  const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {\n      composedSignal.unsubscribe();\n  });\n\n  let requestContentLength;\n\n  try {\n    if (\n      onUploadProgress && supportsRequestStream && method !== 'get' && method !== 'head' &&\n      (requestContentLength = await resolveBodyLength(headers, data)) !== 0\n    ) {\n      let _request = new Request(url, {\n        method: 'POST',\n        body: data,\n        duplex: \"half\"\n      });\n\n      let contentTypeHeader;\n\n      if (utils.isFormData(data) && (contentTypeHeader = _request.headers.get('content-type'))) {\n        headers.setContentType(contentTypeHeader)\n      }\n\n      if (_request.body) {\n        const [onProgress, flush] = progressEventDecorator(\n          requestContentLength,\n          progressEventReducer(asyncDecorator(onUploadProgress))\n        );\n\n        data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);\n      }\n    }\n\n    if (!utils.isString(withCredentials)) {\n      withCredentials = withCredentials ? 'include' : 'omit';\n    }\n\n    // Cloudflare Workers throws when credentials are defined\n    // see https://github.com/cloudflare/workerd/issues/902\n    const isCredentialsSupported = \"credentials\" in Request.prototype;\n    request = new Request(url, {\n      ...fetchOptions,\n      signal: composedSignal,\n      method: method.toUpperCase(),\n      headers: headers.normalize().toJSON(),\n      body: data,\n      duplex: \"half\",\n      credentials: isCredentialsSupported ? withCredentials : undefined\n    });\n\n    let response = await fetch(request, fetchOptions);\n\n    const isStreamResponse = supportsResponseStream && (responseType === 'stream' || responseType === 'response');\n\n    if (supportsResponseStream && (onDownloadProgress || (isStreamResponse && unsubscribe))) {\n      const options = {};\n\n      ['status', 'statusText', 'headers'].forEach(prop => {\n        options[prop] = response[prop];\n      });\n\n      const responseContentLength = utils.toFiniteNumber(response.headers.get('content-length'));\n\n      const [onProgress, flush] = onDownloadProgress && progressEventDecorator(\n        responseContentLength,\n        progressEventReducer(asyncDecorator(onDownloadProgress), true)\n      ) || [];\n\n      response = new Response(\n        trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {\n          flush && flush();\n          unsubscribe && unsubscribe();\n        }),\n        options\n      );\n    }\n\n    responseType = responseType || 'text';\n\n    let responseData = await resolvers[utils.findKey(resolvers, responseType) || 'text'](response, config);\n\n    !isStreamResponse && unsubscribe && unsubscribe();\n\n    return await new Promise((resolve, reject) => {\n      settle(resolve, reject, {\n        data: responseData,\n        headers: AxiosHeaders.from(response.headers),\n        status: response.status,\n        statusText: response.statusText,\n        config,\n        request\n      })\n    })\n  } catch (err) {\n    unsubscribe && unsubscribe();\n\n    if (err && err.name === 'TypeError' && /Load failed|fetch/i.test(err.message)) {\n      throw Object.assign(\n        new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request),\n        {\n          cause: err.cause || err\n        }\n      )\n    }\n\n    throw AxiosError.from(err, err && err.code, config, request);\n  }\n});\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,mBAAmB,OAAO,UAAU,cAAc,OAAO,YAAY,cAAc,OAAO,aAAa;AAC7G,MAAM,4BAA4B,oBAAoB,OAAO,mBAAmB;AAEhF,qCAAqC;AACrC,MAAM,aAAa,oBAAoB,CAAC,OAAO,gBAAgB,aAC3D,CAAC,CAAC,UAAY,CAAC,MAAQ,QAAQ,MAAM,CAAC,IAAI,EAAE,IAAI,iBAChD,OAAO,MAAQ,IAAI,WAAW,MAAM,IAAI,SAAS,KAAK,WAAW,GACrE;AAEA,MAAM,OAAO,CAAC,IAAI,GAAG;IACnB,IAAI;QACF,OAAO,CAAC,CAAC,MAAM;IACjB,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF;AAEA,MAAM,wBAAwB,6BAA6B,KAAK;IAC9D,IAAI,iBAAiB;IAErB,MAAM,iBAAiB,IAAI,QAAQ,4JAAQ,CAAC,MAAM,EAAE;QAClD,MAAM,IAAI;QACV,QAAQ;QACR,IAAI,UAAS;YACX,iBAAiB;YACjB,OAAO;QACT;IACF,GAAG,OAAO,CAAC,GAAG,CAAC;IAEf,OAAO,kBAAkB,CAAC;AAC5B;AAEA,MAAM,qBAAqB,KAAK;AAEhC,MAAM,yBAAyB,6BAC7B,KAAK,IAAM,gJAAK,CAAC,gBAAgB,CAAC,IAAI,SAAS,IAAI,IAAI;AAGzD,MAAM,YAAY;IAChB,QAAQ,0BAA0B,CAAC,CAAC,MAAQ,IAAI,IAAI;AACtD;AAEA,oBAAqB,CAAC,CAAC;IACrB;QAAC;QAAQ;QAAe;QAAQ;QAAY;KAAS,CAAC,OAAO,CAAC,CAAA;QAC5D,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,gJAAK,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAQ,GAAG,CAAC,KAAK,KACrF,CAAC,GAAG;YACF,MAAM,IAAI,6JAAU,CAAC,CAAC,eAAe,EAAE,KAAK,kBAAkB,CAAC,EAAE,6JAAU,CAAC,eAAe,EAAE;QAC/F,CAAC;IACL;AACF,CAAC,EAAE,IAAI;AAEP,MAAM,gBAAgB,OAAO;IAC3B,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IAEA,IAAG,gJAAK,CAAC,MAAM,CAAC,OAAO;QACrB,OAAO,KAAK,IAAI;IAClB;IAEA,IAAG,gJAAK,CAAC,mBAAmB,CAAC,OAAO;QAClC,MAAM,WAAW,IAAI,QAAQ,4JAAQ,CAAC,MAAM,EAAE;YAC5C,QAAQ;YACR;QACF;QACA,OAAO,CAAC,MAAM,SAAS,WAAW,EAAE,EAAE,UAAU;IAClD;IAEA,IAAG,gJAAK,CAAC,iBAAiB,CAAC,SAAS,gJAAK,CAAC,aAAa,CAAC,OAAO;QAC7D,OAAO,KAAK,UAAU;IACxB;IAEA,IAAG,gJAAK,CAAC,iBAAiB,CAAC,OAAO;QAChC,OAAO,OAAO;IAChB;IAEA,IAAG,gJAAK,CAAC,QAAQ,CAAC,OAAO;QACvB,OAAO,CAAC,MAAM,WAAW,KAAK,EAAE,UAAU;IAC5C;AACF;AAEA,MAAM,oBAAoB,OAAO,SAAS;IACxC,MAAM,SAAS,gJAAK,CAAC,cAAc,CAAC,QAAQ,gBAAgB;IAE5D,OAAO,UAAU,OAAO,cAAc,QAAQ;AAChD;uCAEe,oBAAoB,CAAC,OAAO;IACzC,IAAI,EACF,GAAG,EACH,MAAM,EACN,IAAI,EACJ,MAAM,EACN,WAAW,EACX,OAAO,EACP,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,kBAAkB,aAAa,EAC/B,YAAY,EACb,GAAG,IAAA,mKAAa,EAAC;IAElB,eAAe,eAAe,CAAC,eAAe,EAAE,EAAE,WAAW,KAAK;IAElE,IAAI,iBAAiB,IAAA,oKAAc,EAAC;QAAC;QAAQ,eAAe,YAAY,aAAa;KAAG,EAAE;IAE1F,IAAI;IAEJ,MAAM,cAAc,kBAAkB,eAAe,WAAW,IAAI,CAAC;QACjE,eAAe,WAAW;IAC9B,CAAC;IAED,IAAI;IAEJ,IAAI;QACF,IACE,oBAAoB,yBAAyB,WAAW,SAAS,WAAW,UAC5E,CAAC,uBAAuB,MAAM,kBAAkB,SAAS,KAAK,MAAM,GACpE;YACA,IAAI,WAAW,IAAI,QAAQ,KAAK;gBAC9B,QAAQ;gBACR,MAAM;gBACN,QAAQ;YACV;YAEA,IAAI;YAEJ,IAAI,gJAAK,CAAC,UAAU,CAAC,SAAS,CAAC,oBAAoB,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG;gBACxF,QAAQ,cAAc,CAAC;YACzB;YAEA,IAAI,SAAS,IAAI,EAAE;gBACjB,MAAM,CAAC,YAAY,MAAM,GAAG,IAAA,yLAAsB,EAChD,sBACA,IAAA,uLAAoB,EAAC,IAAA,iLAAc,EAAC;gBAGtC,OAAO,IAAA,qKAAW,EAAC,SAAS,IAAI,EAAE,oBAAoB,YAAY;YACpE;QACF;QAEA,IAAI,CAAC,gJAAK,CAAC,QAAQ,CAAC,kBAAkB;YACpC,kBAAkB,kBAAkB,YAAY;QAClD;QAEA,yDAAyD;QACzD,uDAAuD;QACvD,MAAM,yBAAyB,iBAAiB,QAAQ,SAAS;QACjE,UAAU,IAAI,QAAQ,KAAK;YACzB,GAAG,YAAY;YACf,QAAQ;YACR,QAAQ,OAAO,WAAW;YAC1B,SAAS,QAAQ,SAAS,GAAG,MAAM;YACnC,MAAM;YACN,QAAQ;YACR,aAAa,yBAAyB,kBAAkB;QAC1D;QAEA,IAAI,WAAW,MAAM,MAAM,SAAS;QAEpC,MAAM,mBAAmB,0BAA0B,CAAC,iBAAiB,YAAY,iBAAiB,UAAU;QAE5G,IAAI,0BAA0B,CAAC,sBAAuB,oBAAoB,WAAY,GAAG;YACvF,MAAM,UAAU,CAAC;YAEjB;gBAAC;gBAAU;gBAAc;aAAU,CAAC,OAAO,CAAC,CAAA;gBAC1C,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;YAChC;YAEA,MAAM,wBAAwB,gJAAK,CAAC,cAAc,CAAC,SAAS,OAAO,CAAC,GAAG,CAAC;YAExE,MAAM,CAAC,YAAY,MAAM,GAAG,sBAAsB,IAAA,yLAAsB,EACtE,uBACA,IAAA,uLAAoB,EAAC,IAAA,iLAAc,EAAC,qBAAqB,UACtD,EAAE;YAEP,WAAW,IAAI,SACb,IAAA,qKAAW,EAAC,SAAS,IAAI,EAAE,oBAAoB,YAAY;gBACzD,SAAS;gBACT,eAAe;YACjB,IACA;QAEJ;QAEA,eAAe,gBAAgB;QAE/B,IAAI,eAAe,MAAM,SAAS,CAAC,gJAAK,CAAC,OAAO,CAAC,WAAW,iBAAiB,OAAO,CAAC,UAAU;QAE/F,CAAC,oBAAoB,eAAe;QAEpC,OAAO,MAAM,IAAI,QAAQ,CAAC,SAAS;YACjC,IAAA,yJAAM,EAAC,SAAS,QAAQ;gBACtB,MAAM;gBACN,SAAS,+JAAY,CAAC,IAAI,CAAC,SAAS,OAAO;gBAC3C,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B;gBACA;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,eAAe;QAEf,IAAI,OAAO,IAAI,IAAI,KAAK,eAAe,qBAAqB,IAAI,CAAC,IAAI,OAAO,GAAG;YAC7E,MAAM,OAAO,MAAM,CACjB,IAAI,6JAAU,CAAC,iBAAiB,6JAAU,CAAC,WAAW,EAAE,QAAQ,UAChE;gBACE,OAAO,IAAI,KAAK,IAAI;YACtB;QAEJ;QAEA,MAAM,6JAAU,CAAC,IAAI,CAAC,KAAK,OAAO,IAAI,IAAI,EAAE,QAAQ;IACtD;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/adapters/adapters.js"], "sourcesContent": ["import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport fetchAdapter from './fetch.js';\nimport AxiosError from \"../core/AxiosError.js\";\n\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter,\n  fetch: fetchAdapter\n}\n\nutils.forEach(knownAdapters, (fn, value) => {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {value});\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {value});\n  }\n});\n\nconst renderReason = (reason) => `- ${reason}`;\n\nconst isResolvedHandle = (adapter) => utils.isFunction(adapter) || adapter === null || adapter === false;\n\nexport default {\n  getAdapter: (adapters) => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n\n    const {length} = adapters;\n    let nameOrAdapter;\n    let adapter;\n\n    const rejectedReasons = {};\n\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      let id;\n\n      adapter = nameOrAdapter;\n\n      if (!isResolvedHandle(nameOrAdapter)) {\n        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n\n        if (adapter === undefined) {\n          throw new AxiosError(`Unknown adapter '${id}'`);\n        }\n      }\n\n      if (adapter) {\n        break;\n      }\n\n      rejectedReasons[id || '#' + i] = adapter;\n    }\n\n    if (!adapter) {\n\n      const reasons = Object.entries(rejectedReasons)\n        .map(([id, state]) => `adapter ${id} ` +\n          (state === false ? 'is not supported by the environment' : 'is not available in the build')\n        );\n\n      let s = length ?\n        (reasons.length > 1 ? 'since :\\n' + reasons.map(renderReason).join('\\n') : ' ' + renderReason(reasons[0])) :\n        'as no adapter specified';\n\n      throw new AxiosError(\n        `There is no suitable adapter to dispatch the request ` + s,\n        'ERR_NOT_SUPPORT'\n      );\n    }\n\n    return adapter;\n  },\n  adapters: knownAdapters\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB;IACpB,MAAM,2JAAW;IACjB,KAAK,0JAAU;IACf,OAAO,4JAAY;AACrB;AAEA,gJAAK,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI;IAChC,IAAI,IAAI;QACN,IAAI;YACF,OAAO,cAAc,CAAC,IAAI,QAAQ;gBAAC;YAAK;QAC1C,EAAE,OAAO,GAAG;QACV,oCAAoC;QACtC;QACA,OAAO,cAAc,CAAC,IAAI,eAAe;YAAC;QAAK;IACjD;AACF;AAEA,MAAM,eAAe,CAAC,SAAW,CAAC,EAAE,EAAE,QAAQ;AAE9C,MAAM,mBAAmB,CAAC,UAAY,gJAAK,CAAC,UAAU,CAAC,YAAY,YAAY,QAAQ,YAAY;uCAEpF;IACb,YAAY,CAAC;QACX,WAAW,gJAAK,CAAC,OAAO,CAAC,YAAY,WAAW;YAAC;SAAS;QAE1D,MAAM,EAAC,MAAM,EAAC,GAAG;QACjB,IAAI;QACJ,IAAI;QAEJ,MAAM,kBAAkB,CAAC;QAEzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,gBAAgB,QAAQ,CAAC,EAAE;YAC3B,IAAI;YAEJ,UAAU;YAEV,IAAI,CAAC,iBAAiB,gBAAgB;gBACpC,UAAU,aAAa,CAAC,CAAC,KAAK,OAAO,cAAc,EAAE,WAAW,GAAG;gBAEnE,IAAI,YAAY,WAAW;oBACzB,MAAM,IAAI,6JAAU,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;gBAChD;YACF;YAEA,IAAI,SAAS;gBACX;YACF;YAEA,eAAe,CAAC,MAAM,MAAM,EAAE,GAAG;QACnC;QAEA,IAAI,CAAC,SAAS;YAEZ,MAAM,UAAU,OAAO,OAAO,CAAC,iBAC5B,GAAG,CAAC,CAAC,CAAC,IAAI,MAAM,GAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,GACpC,CAAC,UAAU,QAAQ,wCAAwC,+BAA+B;YAG9F,IAAI,IAAI,SACL,QAAQ,MAAM,GAAG,IAAI,cAAc,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC,QAAQ,MAAM,aAAa,OAAO,CAAC,EAAE,IACxG;YAEF,MAAM,IAAI,6JAAU,CAClB,CAAC,qDAAqD,CAAC,GAAG,GAC1D;QAEJ;QAEA,OAAO;IACT;IACA,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/dispatchRequest.js"], "sourcesContent": ["'use strict';\n\nimport transformData from './transformData.js';\nimport isCancel from '../cancel/isCancel.js';\nimport defaults from '../defaults/index.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport adapters from \"../adapters/adapters.js\";\n\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError(null, config);\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */\nexport default function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  config.headers = AxiosHeaders.from(config.headers);\n\n  // Transform request data\n  config.data = transformData.call(\n    config,\n    config.transformRequest\n  );\n\n  if (['post', 'put', 'patch'].indexOf(config.method) !== -1) {\n    config.headers.setContentType('application/x-www-form-urlencoded', false);\n  }\n\n  const adapter = adapters.getAdapter(config.adapter || defaults.adapter);\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(\n      config,\n      config.transformResponse,\n      response\n    );\n\n    response.headers = AxiosHeaders.from(response.headers);\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(\n          config,\n          config.transformResponse,\n          reason.response\n        );\n        reason.response.headers = AxiosHeaders.from(reason.response.headers);\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AASA;;;;;;CAMC,GACD,SAAS,6BAA6B,MAAM;IAC1C,IAAI,OAAO,WAAW,EAAE;QACtB,OAAO,WAAW,CAAC,gBAAgB;IACrC;IAEA,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE;QAC1C,MAAM,IAAI,kKAAa,CAAC,MAAM;IAChC;AACF;AASe,SAAS,gBAAgB,MAAM;IAC5C,6BAA6B;IAE7B,OAAO,OAAO,GAAG,+JAAY,CAAC,IAAI,CAAC,OAAO,OAAO;IAEjD,yBAAyB;IACzB,OAAO,IAAI,GAAG,gKAAa,CAAC,IAAI,CAC9B,QACA,OAAO,gBAAgB;IAGzB,IAAI;QAAC;QAAQ;QAAO;KAAQ,CAAC,OAAO,CAAC,OAAO,MAAM,MAAM,CAAC,GAAG;QAC1D,OAAO,OAAO,CAAC,cAAc,CAAC,qCAAqC;IACrE;IAEA,MAAM,UAAU,+JAAQ,CAAC,UAAU,CAAC,OAAO,OAAO,IAAI,4JAAQ,CAAC,OAAO;IAEtE,OAAO,QAAQ,QAAQ,IAAI,CAAC,SAAS,oBAAoB,QAAQ;QAC/D,6BAA6B;QAE7B,0BAA0B;QAC1B,SAAS,IAAI,GAAG,gKAAa,CAAC,IAAI,CAChC,QACA,OAAO,iBAAiB,EACxB;QAGF,SAAS,OAAO,GAAG,+JAAY,CAAC,IAAI,CAAC,SAAS,OAAO;QAErD,OAAO;IACT,GAAG,SAAS,mBAAmB,MAAM;QACnC,IAAI,CAAC,IAAA,6JAAQ,EAAC,SAAS;YACrB,6BAA6B;YAE7B,0BAA0B;YAC1B,IAAI,UAAU,OAAO,QAAQ,EAAE;gBAC7B,OAAO,QAAQ,CAAC,IAAI,GAAG,gKAAa,CAAC,IAAI,CACvC,QACA,OAAO,iBAAiB,EACxB,OAAO,QAAQ;gBAEjB,OAAO,QAAQ,CAAC,OAAO,GAAG,+JAAY,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC,OAAO;YACrE;QACF;QAEA,OAAO,QAAQ,MAAM,CAAC;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/validator.js"], "sourcesContent": ["'use strict';\n\nimport {VERSION} from '../env/data.js';\nimport AxiosError from '../core/AxiosError.js';\n\nconst validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach((type, i) => {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\n\nconst deprecatedWarnings = {};\n\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return (value, opt, opts) => {\n    if (validator === false) {\n      throw new AxiosError(\n        formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')),\n        AxiosError.ERR_DEPRECATED\n      );\n    }\n\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(\n        formatMessage(\n          opt,\n          ' has been deprecated since v' + version + ' and will be removed in the near future'\n        )\n      );\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\nvalidators.spelling = function spelling(correctSpelling) {\n  return (value, opt) => {\n    // eslint-disable-next-line no-console\n    console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);\n    return true;\n  }\n};\n\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n  const keys = Object.keys(options);\n  let i = keys.length;\n  while (i-- > 0) {\n    const opt = keys[i];\n    const validator = schema[opt];\n    if (validator) {\n      const value = options[opt];\n      const result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\n\nexport default {\n  assertOptions,\n  validators\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAKA,MAAM,aAAa,CAAC;AAEpB,sCAAsC;AACtC;IAAC;IAAU;IAAW;IAAU;IAAY;IAAU;CAAS,CAAC,OAAO,CAAC,CAAC,MAAM;IAC7E,UAAU,CAAC,KAAK,GAAG,SAAS,UAAU,KAAK;QACzC,OAAO,OAAO,UAAU,QAAQ,MAAM,CAAC,IAAI,IAAI,OAAO,GAAG,IAAI;IAC/D;AACF;AAEA,MAAM,qBAAqB,CAAC;AAE5B;;;;;;;;CAQC,GACD,WAAW,YAAY,GAAG,SAAS,aAAa,SAAS,EAAE,OAAO,EAAE,OAAO;IACzE,SAAS,cAAc,GAAG,EAAE,IAAI;QAC9B,OAAO,aAAa,sJAAO,GAAG,6BAA6B,MAAM,OAAO,OAAO,CAAC,UAAU,OAAO,UAAU,EAAE;IAC/G;IAEA,sCAAsC;IACtC,OAAO,CAAC,OAAO,KAAK;QAClB,IAAI,cAAc,OAAO;YACvB,MAAM,IAAI,6JAAU,CAClB,cAAc,KAAK,sBAAsB,CAAC,UAAU,SAAS,UAAU,EAAE,IACzE,6JAAU,CAAC,cAAc;QAE7B;QAEA,IAAI,WAAW,CAAC,kBAAkB,CAAC,IAAI,EAAE;YACvC,kBAAkB,CAAC,IAAI,GAAG;YAC1B,sCAAsC;YACtC,QAAQ,IAAI,CACV,cACE,KACA,iCAAiC,UAAU;QAGjD;QAEA,OAAO,YAAY,UAAU,OAAO,KAAK,QAAQ;IACnD;AACF;AAEA,WAAW,QAAQ,GAAG,SAAS,SAAS,eAAe;IACrD,OAAO,CAAC,OAAO;QACb,sCAAsC;QACtC,QAAQ,IAAI,CAAC,GAAG,IAAI,4BAA4B,EAAE,iBAAiB;QACnE,OAAO;IACT;AACF;AAEA;;;;;;;;CAQC,GAED,SAAS,cAAc,OAAO,EAAE,MAAM,EAAE,YAAY;IAClD,IAAI,OAAO,YAAY,UAAU;QAC/B,MAAM,IAAI,6JAAU,CAAC,6BAA6B,6JAAU,CAAC,oBAAoB;IACnF;IACA,MAAM,OAAO,OAAO,IAAI,CAAC;IACzB,IAAI,IAAI,KAAK,MAAM;IACnB,MAAO,MAAM,EAAG;QACd,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,MAAM,YAAY,MAAM,CAAC,IAAI;QAC7B,IAAI,WAAW;YACb,MAAM,QAAQ,OAAO,CAAC,IAAI;YAC1B,MAAM,SAAS,UAAU,aAAa,UAAU,OAAO,KAAK;YAC5D,IAAI,WAAW,MAAM;gBACnB,MAAM,IAAI,6JAAU,CAAC,YAAY,MAAM,cAAc,QAAQ,6JAAU,CAAC,oBAAoB;YAC9F;YACA;QACF;QACA,IAAI,iBAAiB,MAAM;YACzB,MAAM,IAAI,6JAAU,CAAC,oBAAoB,KAAK,6JAAU,CAAC,cAAc;QACzE;IACF;AACF;uCAEe;IACb;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/core/Axios.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport buildURL from '../helpers/buildURL.js';\nimport InterceptorManager from './InterceptorManager.js';\nimport dispatchRequest from './dispatchRequest.js';\nimport mergeConfig from './mergeConfig.js';\nimport buildFullPath from './buildFullPath.js';\nimport validator from '../helpers/validator.js';\nimport AxiosHeaders from './AxiosHeaders.js';\n\nconst validators = validator.validators;\n\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n *\n * @return {Axios} A new instance of Axios\n */\nclass Axios {\n  constructor(instanceConfig) {\n    this.defaults = instanceConfig || {};\n    this.interceptors = {\n      request: new InterceptorManager(),\n      response: new InterceptorManager()\n    };\n  }\n\n  /**\n   * Dispatch a request\n   *\n   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)\n   * @param {?Object} config\n   *\n   * @returns {Promise} The Promise to be fulfilled\n   */\n  async request(configOrUrl, config) {\n    try {\n      return await this._request(configOrUrl, config);\n    } catch (err) {\n      if (err instanceof Error) {\n        let dummy = {};\n\n        Error.captureStackTrace ? Error.captureStackTrace(dummy) : (dummy = new Error());\n\n        // slice off the Error: ... line\n        const stack = dummy.stack ? dummy.stack.replace(/^.+\\n/, '') : '';\n        try {\n          if (!err.stack) {\n            err.stack = stack;\n            // match without the 2 top stack lines\n          } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\\n.+\\n/, ''))) {\n            err.stack += '\\n' + stack\n          }\n        } catch (e) {\n          // ignore the case where \"stack\" is an un-writable property\n        }\n      }\n\n      throw err;\n    }\n  }\n\n  _request(configOrUrl, config) {\n    /*eslint no-param-reassign:0*/\n    // Allow for axios('example/url'[, config]) a la fetch API\n    if (typeof configOrUrl === 'string') {\n      config = config || {};\n      config.url = configOrUrl;\n    } else {\n      config = configOrUrl || {};\n    }\n\n    config = mergeConfig(this.defaults, config);\n\n    const {transitional, paramsSerializer, headers} = config;\n\n    if (transitional !== undefined) {\n      validator.assertOptions(transitional, {\n        silentJSONParsing: validators.transitional(validators.boolean),\n        forcedJSONParsing: validators.transitional(validators.boolean),\n        clarifyTimeoutError: validators.transitional(validators.boolean)\n      }, false);\n    }\n\n    if (paramsSerializer != null) {\n      if (utils.isFunction(paramsSerializer)) {\n        config.paramsSerializer = {\n          serialize: paramsSerializer\n        }\n      } else {\n        validator.assertOptions(paramsSerializer, {\n          encode: validators.function,\n          serialize: validators.function\n        }, true);\n      }\n    }\n\n    // Set config.allowAbsoluteUrls\n    if (config.allowAbsoluteUrls !== undefined) {\n      // do nothing\n    } else if (this.defaults.allowAbsoluteUrls !== undefined) {\n      config.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls;\n    } else {\n      config.allowAbsoluteUrls = true;\n    }\n\n    validator.assertOptions(config, {\n      baseUrl: validators.spelling('baseURL'),\n      withXsrfToken: validators.spelling('withXSRFToken')\n    }, true);\n\n    // Set config.method\n    config.method = (config.method || this.defaults.method || 'get').toLowerCase();\n\n    // Flatten headers\n    let contextHeaders = headers && utils.merge(\n      headers.common,\n      headers[config.method]\n    );\n\n    headers && utils.forEach(\n      ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],\n      (method) => {\n        delete headers[method];\n      }\n    );\n\n    config.headers = AxiosHeaders.concat(contextHeaders, headers);\n\n    // filter out skipped interceptors\n    const requestInterceptorChain = [];\n    let synchronousRequestInterceptors = true;\n    this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n      if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {\n        return;\n      }\n\n      synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n\n      requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n    });\n\n    const responseInterceptorChain = [];\n    this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n      responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n    });\n\n    let promise;\n    let i = 0;\n    let len;\n\n    if (!synchronousRequestInterceptors) {\n      const chain = [dispatchRequest.bind(this), undefined];\n      chain.unshift(...requestInterceptorChain);\n      chain.push(...responseInterceptorChain);\n      len = chain.length;\n\n      promise = Promise.resolve(config);\n\n      while (i < len) {\n        promise = promise.then(chain[i++], chain[i++]);\n      }\n\n      return promise;\n    }\n\n    len = requestInterceptorChain.length;\n\n    let newConfig = config;\n\n    i = 0;\n\n    while (i < len) {\n      const onFulfilled = requestInterceptorChain[i++];\n      const onRejected = requestInterceptorChain[i++];\n      try {\n        newConfig = onFulfilled(newConfig);\n      } catch (error) {\n        onRejected.call(this, error);\n        break;\n      }\n    }\n\n    try {\n      promise = dispatchRequest.call(this, newConfig);\n    } catch (error) {\n      return Promise.reject(error);\n    }\n\n    i = 0;\n    len = responseInterceptorChain.length;\n\n    while (i < len) {\n      promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);\n    }\n\n    return promise;\n  }\n\n  getUri(config) {\n    config = mergeConfig(this.defaults, config);\n    const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);\n    return buildURL(fullPath, config.params, config.paramsSerializer);\n  }\n}\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method,\n      url,\n      data: (config || {}).data\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n\n  function generateHTTPMethod(isForm) {\n    return function httpMethod(url, data, config) {\n      return this.request(mergeConfig(config || {}, {\n        method,\n        headers: isForm ? {\n          'Content-Type': 'multipart/form-data'\n        } : {},\n        url,\n        data\n      }));\n    };\n  }\n\n  Axios.prototype[method] = generateHTTPMethod();\n\n  Axios.prototype[method + 'Form'] = generateHTTPMethod(true);\n});\n\nexport default Axios;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWA,MAAM,aAAa,+JAAS,CAAC,UAAU;AAEvC;;;;;;CAMC,GACD,MAAM;IACJ,YAAY,cAAc,CAAE;QAC1B,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG;YAClB,SAAS,IAAI,qKAAkB;YAC/B,UAAU,IAAI,qKAAkB;QAClC;IACF;IAEA;;;;;;;GAOC,GACD,MAAM,QAAQ,WAAW,EAAE,MAAM,EAAE;QACjC,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa;QAC1C,EAAE,OAAO,KAAK;YACZ,IAAI,eAAe,OAAO;gBACxB,IAAI,QAAQ,CAAC;gBAEb,MAAM,iBAAiB,GAAG,MAAM,iBAAiB,CAAC,SAAU,QAAQ,IAAI;gBAExE,gCAAgC;gBAChC,MAAM,QAAQ,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM;gBAC/D,IAAI;oBACF,IAAI,CAAC,IAAI,KAAK,EAAE;wBACd,IAAI,KAAK,GAAG;oBACZ,sCAAsC;oBACxC,OAAO,IAAI,SAAS,CAAC,OAAO,IAAI,KAAK,EAAE,QAAQ,CAAC,MAAM,OAAO,CAAC,aAAa,MAAM;wBAC/E,IAAI,KAAK,IAAI,OAAO;oBACtB;gBACF,EAAE,OAAO,GAAG;gBACV,2DAA2D;gBAC7D;YACF;YAEA,MAAM;QACR;IACF;IAEA,SAAS,WAAW,EAAE,MAAM,EAAE;QAC5B,4BAA4B,GAC5B,0DAA0D;QAC1D,IAAI,OAAO,gBAAgB,UAAU;YACnC,SAAS,UAAU,CAAC;YACpB,OAAO,GAAG,GAAG;QACf,OAAO;YACL,SAAS,eAAe,CAAC;QAC3B;QAEA,SAAS,IAAA,8JAAW,EAAC,IAAI,CAAC,QAAQ,EAAE;QAEpC,MAAM,EAAC,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAC,GAAG;QAElD,IAAI,iBAAiB,WAAW;YAC9B,+JAAS,CAAC,aAAa,CAAC,cAAc;gBACpC,mBAAmB,WAAW,YAAY,CAAC,WAAW,OAAO;gBAC7D,mBAAmB,WAAW,YAAY,CAAC,WAAW,OAAO;gBAC7D,qBAAqB,WAAW,YAAY,CAAC,WAAW,OAAO;YACjE,GAAG;QACL;QAEA,IAAI,oBAAoB,MAAM;YAC5B,IAAI,gJAAK,CAAC,UAAU,CAAC,mBAAmB;gBACtC,OAAO,gBAAgB,GAAG;oBACxB,WAAW;gBACb;YACF,OAAO;gBACL,+JAAS,CAAC,aAAa,CAAC,kBAAkB;oBACxC,QAAQ,WAAW,QAAQ;oBAC3B,WAAW,WAAW,QAAQ;gBAChC,GAAG;YACL;QACF;QAEA,+BAA+B;QAC/B,IAAI,OAAO,iBAAiB,KAAK,WAAW;QAC1C,aAAa;QACf,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,KAAK,WAAW;YACxD,OAAO,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB;QAC5D,OAAO;YACL,OAAO,iBAAiB,GAAG;QAC7B;QAEA,+JAAS,CAAC,aAAa,CAAC,QAAQ;YAC9B,SAAS,WAAW,QAAQ,CAAC;YAC7B,eAAe,WAAW,QAAQ,CAAC;QACrC,GAAG;QAEH,oBAAoB;QACpB,OAAO,MAAM,GAAG,CAAC,OAAO,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,KAAK,EAAE,WAAW;QAE5E,kBAAkB;QAClB,IAAI,iBAAiB,WAAW,gJAAK,CAAC,KAAK,CACzC,QAAQ,MAAM,EACd,OAAO,CAAC,OAAO,MAAM,CAAC;QAGxB,WAAW,gJAAK,CAAC,OAAO,CACtB;YAAC;YAAU;YAAO;YAAQ;YAAQ;YAAO;YAAS;SAAS,EAC3D,CAAC;YACC,OAAO,OAAO,CAAC,OAAO;QACxB;QAGF,OAAO,OAAO,GAAG,+JAAY,CAAC,MAAM,CAAC,gBAAgB;QAErD,kCAAkC;QAClC,MAAM,0BAA0B,EAAE;QAClC,IAAI,iCAAiC;QACrC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,2BAA2B,WAAW;YAC/E,IAAI,OAAO,YAAY,OAAO,KAAK,cAAc,YAAY,OAAO,CAAC,YAAY,OAAO;gBACtF;YACF;YAEA,iCAAiC,kCAAkC,YAAY,WAAW;YAE1F,wBAAwB,OAAO,CAAC,YAAY,SAAS,EAAE,YAAY,QAAQ;QAC7E;QAEA,MAAM,2BAA2B,EAAE;QACnC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,yBAAyB,WAAW;YAC9E,yBAAyB,IAAI,CAAC,YAAY,SAAS,EAAE,YAAY,QAAQ;QAC3E;QAEA,IAAI;QACJ,IAAI,IAAI;QACR,IAAI;QAEJ,IAAI,CAAC,gCAAgC;YACnC,MAAM,QAAQ;gBAAC,kKAAe,CAAC,IAAI,CAAC,IAAI;gBAAG;aAAU;YACrD,MAAM,OAAO,IAAI;YACjB,MAAM,IAAI,IAAI;YACd,MAAM,MAAM,MAAM;YAElB,UAAU,QAAQ,OAAO,CAAC;YAE1B,MAAO,IAAI,IAAK;gBACd,UAAU,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI;YAC/C;YAEA,OAAO;QACT;QAEA,MAAM,wBAAwB,MAAM;QAEpC,IAAI,YAAY;QAEhB,IAAI;QAEJ,MAAO,IAAI,IAAK;YACd,MAAM,cAAc,uBAAuB,CAAC,IAAI;YAChD,MAAM,aAAa,uBAAuB,CAAC,IAAI;YAC/C,IAAI;gBACF,YAAY,YAAY;YAC1B,EAAE,OAAO,OAAO;gBACd,WAAW,IAAI,CAAC,IAAI,EAAE;gBACtB;YACF;QACF;QAEA,IAAI;YACF,UAAU,kKAAe,CAAC,IAAI,CAAC,IAAI,EAAE;QACvC,EAAE,OAAO,OAAO;YACd,OAAO,QAAQ,MAAM,CAAC;QACxB;QAEA,IAAI;QACJ,MAAM,yBAAyB,MAAM;QAErC,MAAO,IAAI,IAAK;YACd,UAAU,QAAQ,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,wBAAwB,CAAC,IAAI;QACrF;QAEA,OAAO;IACT;IAEA,OAAO,MAAM,EAAE;QACb,SAAS,IAAA,8JAAW,EAAC,IAAI,CAAC,QAAQ,EAAE;QACpC,MAAM,WAAW,IAAA,gKAAa,EAAC,OAAO,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,iBAAiB;QACnF,OAAO,IAAA,8JAAQ,EAAC,UAAU,OAAO,MAAM,EAAE,OAAO,gBAAgB;IAClE;AACF;AAEA,gDAAgD;AAChD,gJAAK,CAAC,OAAO,CAAC;IAAC;IAAU;IAAO;IAAQ;CAAU,EAAE,SAAS,oBAAoB,MAAM;IACrF,qBAAqB,GACrB,MAAM,SAAS,CAAC,OAAO,GAAG,SAAS,GAAG,EAAE,MAAM;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,IAAA,8JAAW,EAAC,UAAU,CAAC,GAAG;YAC5C;YACA;YACA,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI;QAC3B;IACF;AACF;AAEA,gJAAK,CAAC,OAAO,CAAC;IAAC;IAAQ;IAAO;CAAQ,EAAE,SAAS,sBAAsB,MAAM;IAC3E,qBAAqB,GAErB,SAAS,mBAAmB,MAAM;QAChC,OAAO,SAAS,WAAW,GAAG,EAAE,IAAI,EAAE,MAAM;YAC1C,OAAO,IAAI,CAAC,OAAO,CAAC,IAAA,8JAAW,EAAC,UAAU,CAAC,GAAG;gBAC5C;gBACA,SAAS,SAAS;oBAChB,gBAAgB;gBAClB,IAAI,CAAC;gBACL;gBACA;YACF;QACF;IACF;IAEA,MAAM,SAAS,CAAC,OAAO,GAAG;IAE1B,MAAM,SAAS,CAAC,SAAS,OAAO,GAAG,mBAAmB;AACxD;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/cancel/CancelToken.js"], "sourcesContent": ["'use strict';\n\nimport CanceledError from './CanceledError.js';\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */\nclass CancelToken {\n  constructor(executor) {\n    if (typeof executor !== 'function') {\n      throw new TypeError('executor must be a function.');\n    }\n\n    let resolvePromise;\n\n    this.promise = new Promise(function promiseExecutor(resolve) {\n      resolvePromise = resolve;\n    });\n\n    const token = this;\n\n    // eslint-disable-next-line func-names\n    this.promise.then(cancel => {\n      if (!token._listeners) return;\n\n      let i = token._listeners.length;\n\n      while (i-- > 0) {\n        token._listeners[i](cancel);\n      }\n      token._listeners = null;\n    });\n\n    // eslint-disable-next-line func-names\n    this.promise.then = onfulfilled => {\n      let _resolve;\n      // eslint-disable-next-line func-names\n      const promise = new Promise(resolve => {\n        token.subscribe(resolve);\n        _resolve = resolve;\n      }).then(onfulfilled);\n\n      promise.cancel = function reject() {\n        token.unsubscribe(_resolve);\n      };\n\n      return promise;\n    };\n\n    executor(function cancel(message, config, request) {\n      if (token.reason) {\n        // Cancellation has already been requested\n        return;\n      }\n\n      token.reason = new CanceledError(message, config, request);\n      resolvePromise(token.reason);\n    });\n  }\n\n  /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */\n  throwIfRequested() {\n    if (this.reason) {\n      throw this.reason;\n    }\n  }\n\n  /**\n   * Subscribe to the cancel signal\n   */\n\n  subscribe(listener) {\n    if (this.reason) {\n      listener(this.reason);\n      return;\n    }\n\n    if (this._listeners) {\n      this._listeners.push(listener);\n    } else {\n      this._listeners = [listener];\n    }\n  }\n\n  /**\n   * Unsubscribe from the cancel signal\n   */\n\n  unsubscribe(listener) {\n    if (!this._listeners) {\n      return;\n    }\n    const index = this._listeners.indexOf(listener);\n    if (index !== -1) {\n      this._listeners.splice(index, 1);\n    }\n  }\n\n  toAbortSignal() {\n    const controller = new AbortController();\n\n    const abort = (err) => {\n      controller.abort(err);\n    };\n\n    this.subscribe(abort);\n\n    controller.signal.unsubscribe = () => this.unsubscribe(abort);\n\n    return controller.signal;\n  }\n\n  /**\n   * Returns an object that contains a new `CancelToken` and a function that, when called,\n   * cancels the `CancelToken`.\n   */\n  static source() {\n    let cancel;\n    const token = new CancelToken(function executor(c) {\n      cancel = c;\n    });\n    return {\n      token,\n      cancel\n    };\n  }\n}\n\nexport default CancelToken;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIA;;;;;;CAMC,GACD,MAAM;IACJ,YAAY,QAAQ,CAAE;QACpB,IAAI,OAAO,aAAa,YAAY;YAClC,MAAM,IAAI,UAAU;QACtB;QAEA,IAAI;QAEJ,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,SAAS,gBAAgB,OAAO;YACzD,iBAAiB;QACnB;QAEA,MAAM,QAAQ,IAAI;QAElB,sCAAsC;QACtC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAChB,IAAI,CAAC,MAAM,UAAU,EAAE;YAEvB,IAAI,IAAI,MAAM,UAAU,CAAC,MAAM;YAE/B,MAAO,MAAM,EAAG;gBACd,MAAM,UAAU,CAAC,EAAE,CAAC;YACtB;YACA,MAAM,UAAU,GAAG;QACrB;QAEA,sCAAsC;QACtC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAA;YAClB,IAAI;YACJ,sCAAsC;YACtC,MAAM,UAAU,IAAI,QAAQ,CAAA;gBAC1B,MAAM,SAAS,CAAC;gBAChB,WAAW;YACb,GAAG,IAAI,CAAC;YAER,QAAQ,MAAM,GAAG,SAAS;gBACxB,MAAM,WAAW,CAAC;YACpB;YAEA,OAAO;QACT;QAEA,SAAS,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,OAAO;YAC/C,IAAI,MAAM,MAAM,EAAE;gBAChB,0CAA0C;gBAC1C;YACF;YAEA,MAAM,MAAM,GAAG,IAAI,kKAAa,CAAC,SAAS,QAAQ;YAClD,eAAe,MAAM,MAAM;QAC7B;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,CAAC,MAAM;QACnB;IACF;IAEA;;GAEC,GAED,UAAU,QAAQ,EAAE;QAClB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,SAAS,IAAI,CAAC,MAAM;YACpB;QACF;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACvB,OAAO;YACL,IAAI,CAAC,UAAU,GAAG;gBAAC;aAAS;QAC9B;IACF;IAEA;;GAEC,GAED,YAAY,QAAQ,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB;QACF;QACA,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACtC,IAAI,UAAU,CAAC,GAAG;YAChB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO;QAChC;IACF;IAEA,gBAAgB;QACd,MAAM,aAAa,IAAI;QAEvB,MAAM,QAAQ,CAAC;YACb,WAAW,KAAK,CAAC;QACnB;QAEA,IAAI,CAAC,SAAS,CAAC;QAEf,WAAW,MAAM,CAAC,WAAW,GAAG,IAAM,IAAI,CAAC,WAAW,CAAC;QAEvD,OAAO,WAAW,MAAM;IAC1B;IAEA;;;GAGC,GACD,OAAO,SAAS;QACd,IAAI;QACJ,MAAM,QAAQ,IAAI,YAAY,SAAS,SAAS,CAAC;YAC/C,SAAS;QACX;QACA,OAAO;YACL;YACA;QACF;IACF;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/spread.js"], "sourcesContent": ["'use strict';\n\n/**\n * Syntactic sugar for invoking a function and expanding an array for arguments.\n *\n * Common use case would be to use `Function.prototype.apply`.\n *\n *  ```js\n *  function f(x, y, z) {}\n *  var args = [1, 2, 3];\n *  f.apply(null, args);\n *  ```\n *\n * With `spread` this example can be re-written.\n *\n *  ```js\n *  spread(function(x, y, z) {})([1, 2, 3]);\n *  ```\n *\n * @param {Function} callback\n *\n * @returns {Function}\n */\nexport default function spread(callback) {\n  return function wrap(arr) {\n    return callback.apply(null, arr);\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AAuBe,SAAS,OAAO,QAAQ;IACrC,OAAO,SAAS,KAAK,GAAG;QACtB,OAAO,SAAS,KAAK,CAAC,MAAM;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/isAxiosError.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\n/**\n * Determines whether the payload is an error thrown by <PERSON>xios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\nexport default function isAxiosError(payload) {\n  return utils.isObject(payload) && (payload.isAxiosError === true);\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAWe,SAAS,aAAa,OAAO;IAC1C,OAAO,gJAAK,CAAC,QAAQ,CAAC,YAAa,QAAQ,YAAY,KAAK;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/helpers/HttpStatusCode.js"], "sourcesContent": ["const HttpStatusCode = {\n  Continue: 100,\n  SwitchingProtocols: 101,\n  Processing: 102,\n  EarlyHints: 103,\n  Ok: 200,\n  Created: 201,\n  Accepted: 202,\n  NonAuthoritativeInformation: 203,\n  NoContent: 204,\n  ResetContent: 205,\n  PartialContent: 206,\n  MultiStatus: 207,\n  AlreadyReported: 208,\n  ImUsed: 226,\n  MultipleChoices: 300,\n  MovedPermanently: 301,\n  Found: 302,\n  SeeOther: 303,\n  NotModified: 304,\n  UseProxy: 305,\n  Unused: 306,\n  TemporaryRedirect: 307,\n  PermanentRedirect: 308,\n  BadRequest: 400,\n  Unauthorized: 401,\n  PaymentRequired: 402,\n  Forbidden: 403,\n  NotFound: 404,\n  MethodNotAllowed: 405,\n  NotAcceptable: 406,\n  ProxyAuthenticationRequired: 407,\n  RequestTimeout: 408,\n  Conflict: 409,\n  Gone: 410,\n  LengthRequired: 411,\n  PreconditionFailed: 412,\n  PayloadTooLarge: 413,\n  UriTooLong: 414,\n  UnsupportedMediaType: 415,\n  RangeNotSatisfiable: 416,\n  ExpectationFailed: 417,\n  ImATeapot: 418,\n  MisdirectedRequest: 421,\n  UnprocessableEntity: 422,\n  Locked: 423,\n  FailedDependency: 424,\n  TooEarly: 425,\n  UpgradeRequired: 426,\n  PreconditionRequired: 428,\n  TooManyRequests: 429,\n  RequestHeaderFieldsTooLarge: 431,\n  UnavailableForLegalReasons: 451,\n  InternalServerError: 500,\n  NotImplemented: 501,\n  BadGateway: 502,\n  ServiceUnavailable: 503,\n  GatewayTimeout: 504,\n  HttpVersionNotSupported: 505,\n  VariantAlsoNegotiates: 506,\n  InsufficientStorage: 507,\n  LoopDetected: 508,\n  NotExtended: 510,\n  NetworkAuthenticationRequired: 511,\n};\n\nObject.entries(HttpStatusCode).forEach(([key, value]) => {\n  HttpStatusCode[value] = key;\n});\n\nexport default HttpStatusCode;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,iBAAiB;IACrB,UAAU;IACV,oBAAoB;IACpB,YAAY;IACZ,YAAY;IACZ,IAAI;IACJ,SAAS;IACT,UAAU;IACV,6BAA6B;IAC7B,WAAW;IACX,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,QAAQ;IACR,iBAAiB;IACjB,kBAAkB;IAClB,OAAO;IACP,UAAU;IACV,aAAa;IACb,UAAU;IACV,QAAQ;IACR,mBAAmB;IACnB,mBAAmB;IACnB,YAAY;IACZ,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,UAAU;IACV,kBAAkB;IAClB,eAAe;IACf,6BAA6B;IAC7B,gBAAgB;IAChB,UAAU;IACV,MAAM;IACN,gBAAgB;IAChB,oBAAoB;IACpB,iBAAiB;IACjB,YAAY;IACZ,sBAAsB;IACtB,qBAAqB;IACrB,mBAAmB;IACnB,WAAW;IACX,oBAAoB;IACpB,qBAAqB;IACrB,QAAQ;IACR,kBAAkB;IAClB,UAAU;IACV,iBAAiB;IACjB,sBAAsB;IACtB,iBAAiB;IACjB,6BAA6B;IAC7B,4BAA4B;IAC5B,qBAAqB;IACrB,gBAAgB;IAChB,YAAY;IACZ,oBAAoB;IACpB,gBAAgB;IAChB,yBAAyB;IACzB,uBAAuB;IACvB,qBAAqB;IACrB,cAAc;IACd,aAAa;IACb,+BAA+B;AACjC;AAEA,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;IAClD,cAAc,CAAC,MAAM,GAAG;AAC1B;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/axios/lib/axios.js"], "sourcesContent": ["'use strict';\n\nimport utils from './utils.js';\nimport bind from './helpers/bind.js';\nimport Axios from './core/Axios.js';\nimport mergeConfig from './core/mergeConfig.js';\nimport defaults from './defaults/index.js';\nimport formDataToJSON from './helpers/formDataToJSON.js';\nimport CanceledError from './cancel/CanceledError.js';\nimport CancelToken from './cancel/CancelToken.js';\nimport isCancel from './cancel/isCancel.js';\nimport {VERSION} from './env/data.js';\nimport toFormData from './helpers/toFormData.js';\nimport AxiosError from './core/AxiosError.js';\nimport spread from './helpers/spread.js';\nimport isAxiosError from './helpers/isAxiosError.js';\nimport AxiosHeaders from \"./core/AxiosHeaders.js\";\nimport adapters from './adapters/adapters.js';\nimport HttpStatusCode from './helpers/HttpStatusCode.js';\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  const context = new Axios(defaultConfig);\n  const instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context, {allOwnKeys: true});\n\n  // Copy context to instance\n  utils.extend(instance, context, null, {allOwnKeys: true});\n\n  // Factory for creating new instances\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n\n  return instance;\n}\n\n// Create the default instance to be exported\nconst axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Expose Cancel & CancelToken\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData;\n\n// Expose AxiosError class\naxios.AxiosError = AxiosError;\n\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\n\naxios.spread = spread;\n\n// Expose isAxiosError\naxios.isAxiosError = isAxiosError;\n\n// Expose mergeConfig\naxios.mergeConfig = mergeConfig;\n\naxios.AxiosHeaders = AxiosHeaders;\n\naxios.formToJSON = thing => formDataToJSON(utils.isHTMLForm(thing) ? new FormData(thing) : thing);\n\naxios.getAdapter = adapters.getAdapter;\n\naxios.HttpStatusCode = HttpStatusCode;\n\naxios.default = axios;\n\n// this module should only have a default export\nexport default axios\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;;;;AAoBA;;;;;;CAMC,GACD,SAAS,eAAe,aAAa;IACnC,MAAM,UAAU,IAAI,wJAAK,CAAC;IAC1B,MAAM,WAAW,IAAA,0JAAI,EAAC,wJAAK,CAAC,SAAS,CAAC,OAAO,EAAE;IAE/C,mCAAmC;IACnC,gJAAK,CAAC,MAAM,CAAC,UAAU,wJAAK,CAAC,SAAS,EAAE,SAAS;QAAC,YAAY;IAAI;IAElE,2BAA2B;IAC3B,gJAAK,CAAC,MAAM,CAAC,UAAU,SAAS,MAAM;QAAC,YAAY;IAAI;IAEvD,qCAAqC;IACrC,SAAS,MAAM,GAAG,SAAS,OAAO,cAAc;QAC9C,OAAO,eAAe,IAAA,8JAAW,EAAC,eAAe;IACnD;IAEA,OAAO;AACT;AAEA,6CAA6C;AAC7C,MAAM,QAAQ,eAAe,4JAAQ;AAErC,gDAAgD;AAChD,MAAM,KAAK,GAAG,wJAAK;AAEnB,8BAA8B;AAC9B,MAAM,aAAa,GAAG,kKAAa;AACnC,MAAM,WAAW,GAAG,gKAAW;AAC/B,MAAM,QAAQ,GAAG,6JAAQ;AACzB,MAAM,OAAO,GAAG,sJAAO;AACvB,MAAM,UAAU,GAAG,gKAAU;AAE7B,0BAA0B;AAC1B,MAAM,UAAU,GAAG,6JAAU;AAE7B,qDAAqD;AACrD,MAAM,MAAM,GAAG,MAAM,aAAa;AAElC,oBAAoB;AACpB,MAAM,GAAG,GAAG,SAAS,IAAI,QAAQ;IAC/B,OAAO,QAAQ,GAAG,CAAC;AACrB;AAEA,MAAM,MAAM,GAAG,4JAAM;AAErB,sBAAsB;AACtB,MAAM,YAAY,GAAG,kKAAY;AAEjC,qBAAqB;AACrB,MAAM,WAAW,GAAG,8JAAW;AAE/B,MAAM,YAAY,GAAG,+JAAY;AAEjC,MAAM,UAAU,GAAG,CAAA,QAAS,IAAA,oKAAc,EAAC,gJAAK,CAAC,UAAU,CAAC,SAAS,IAAI,SAAS,SAAS;AAE3F,MAAM,UAAU,GAAG,+JAAQ,CAAC,UAAU;AAEtC,MAAM,cAAc,GAAG,oKAAc;AAErC,MAAM,OAAO,GAAG;uCAGD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8808, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Is empty string\n *\n * @param {unknown} value\n * @returns {boolean} Whether the value is an empty string\n */\nexport const isEmptyString = (value: unknown): boolean => value === '';\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAQO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAC1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,WAAA,CAAA,CAAA,CAAc,CAAmB,MAAA,CAAA,CAAA,CAC5C,AAD4C,CAAA,AAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,CAAA,CAAA,CAAA,CAAA,GAAO,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,AAClD,CADkD,AAClD,IAAK,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAI,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AASxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC;IAC7E,MAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,MAAM,CAAA;IAEpC,OAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,GAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,YAAA,CAAA,CAAA,CAAe,CAAA,GAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GACrE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,GAAO,KAAA,CAAA,CAAA,CAAA,CAAA,CAAU;QACnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACE,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CACjC,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,KAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAG,CAAA,CACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAgBE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA,CAAA,KAAQ,KAAA,CAAA,AAAO;QACxB,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,KAAK,CAAA,CAAA,CAAA,CAAA,KAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA,CAAA,MAAS,OAAA,CAAA,CAAS;YACnE,OAAO,CAAA,CAAA,CAAA,CAAA;QACT;IACF;AACF,CAAA", "debugId": null}}, {"offset": {"line": 8847, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAA,EAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "debugId": null}}, {"offset": {"line": 8873, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/Icon.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAwBA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,OAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,0MAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,GAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAA,GAAO,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAc,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAY,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,GAAA,EAEL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,0MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,IAAI,CAAA,CAAA,GAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAI,CAAA,GAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAA,KAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6KAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,KAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6KAAA,EAAY,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAA;QAAA,CAAO;QAC/D,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA;IAAA,CACL,EACA;WACK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,OAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,0MAAA,EAAc,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;YAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;SAAA;KAAA", "debugId": null}}, {"offset": {"line": 8912, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAWA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,gBAAA,CAAA,CAAA,CAAmB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,0MAAA,EAAuC,CAAC,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,GAAM,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GACjF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,0MAAA,EAAc,iKAAA,CAAA,CAAM;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6KAAA,EACT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6KAAA,MAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6KAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,EAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CACJ;IAGH,SAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6KAAA,EAAa,QAAQ,CAAA;IAE7C,OAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "debugId": null}}, {"offset": {"line": 8943, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/wifi.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/wifi.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20h.01', key: 'zekei9' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 20 0', key: 'dnpr2z' }],\n  ['path', { d: 'M5 12.859a10 10 0 0 1 14 0', key: '1x1e6c' }],\n  ['path', { d: 'M8.5 16.429a5 5 0 0 1 7 0', key: '1bycff' }],\n];\n\n/**\n * @component @name Wifi\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0yIDguODJhMTUgMTUgMCAwIDEgMjAgMCIgLz4KICA8cGF0aCBkPSJNNSAxMi44NTlhMTAgMTAgMCAwIDEgMTQgMCIgLz4KICA8cGF0aCBkPSJNOC41IDE2LjQyOWE1IDUgMCAwIDEgNyAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/wifi\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wifi = createLucideIcon('wifi', __iconNode);\n\nexport default Wifi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA4B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA8B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA6B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC5D;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,IAAA,CAAA,CAAA,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9002, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/wifi-off.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/wifi-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20h.01', key: 'zekei9' }],\n  ['path', { d: 'M8.5 16.429a5 5 0 0 1 7 0', key: '1bycff' }],\n  ['path', { d: 'M5 12.859a10 10 0 0 1 5.17-2.69', key: '1dl1wf' }],\n  ['path', { d: 'M19 12.859a10 10 0 0 0-2.007-1.523', key: '4k23kn' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 4.177-2.643', key: '1grhjp' }],\n  ['path', { d: 'M22 8.82a15 15 0 0 0-11.288-3.764', key: 'z3jwby' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n];\n\n/**\n * @component @name WifiOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBoLjAxIiAvPgogIDxwYXRoIGQ9Ik04LjUgMTYuNDI5YTUgNSAwIDAgMSA3IDAiIC8+CiAgPHBhdGggZD0iTTUgMTIuODU5YTEwIDEwIDAgMCAxIDUuMTctMi42OSIgLz4KICA8cGF0aCBkPSJNMTkgMTIuODU5YTEwIDEwIDAgMCAwLTIuMDA3LTEuNTIzIiAvPgogIDxwYXRoIGQ9Ik0yIDguODJhMTUgMTUgMCAwIDEgNC4xNzctMi42NDMiIC8+CiAgPHBhdGggZD0iTTIyIDguODJhMTUgMTUgMCAwIDAtMTEuMjg4LTMuNzY0IiAvPgogIDxwYXRoIGQ9Im0yIDIgMjAgMjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/wifi-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst WifiOff = createLucideIcon('wifi-off', __iconNode);\n\nexport default WifiOff;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA6B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC1D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAmC,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAsC,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAmC,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAqC,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC7C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAA,CAAA,CAAA,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9082, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,EAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAqD,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACpF;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,KAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9130, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAa,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA0B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,KAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9175, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 17h6v-6', key: 't6n2it' }],\n  ['path', { d: 'm22 17-8.5-8.5-5 5L2 7', key: 'x473p' }],\n];\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTdoNnYtNiIgLz4KICA8cGF0aCBkPSJtMjIgMTctOC41LTguNS01IDVMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('trending-down', __iconNode);\n\nexport default TrendingDown;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAe,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA0B,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAS;KAAA;CACxD;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,YAAA,CAAA,CAAA,KAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9220, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/wallet.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/wallet.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1',\n      key: '18etb6',\n    },\n  ],\n  ['path', { d: 'M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4', key: 'xoc0q4' }],\n];\n\n/**\n * @component @name Wallet\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgN1Y0YTEgMSAwIDAgMC0xLTFINWEyIDIgMCAwIDAgMCA0aDE1YTEgMSAwIDAgMSAxIDF2NGgtM2EyIDIgMCAwIDAgMCA0aDNhMSAxIDAgMCAwIDEtMXYtMmExIDEgMCAwIDAtMS0xIiAvPgogIDxwYXRoIGQ9Ik0zIDV2MTRhMiAyIDAgMCAwIDIgMmgxNWExIDEgMCAwIDAgMS0xdi00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/wallet\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wallet = createLucideIcon('wallet', __iconNode);\n\nexport default Wallet;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA6C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC5E;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,MAAA,CAAA,CAAA,KAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9265, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/x.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('x', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC7C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,KAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9310, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/clock.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAe,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU;YAAE,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAA,CAAK,QAAA;QAAA,CAAU;KAAA;CAC3D;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,KAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 9357, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Bureau/augment/bingx-trading-app/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAsD,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAuD,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACpF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAa,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC5C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAA,CAAA,CAAA,KAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}]}