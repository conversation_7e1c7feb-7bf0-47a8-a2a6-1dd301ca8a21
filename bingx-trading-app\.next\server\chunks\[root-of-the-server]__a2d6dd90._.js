module.exports = [
"[project]/.next-internal/server/app/api/bingx/orders/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/src/lib/bingx-auth.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "buildSignedUrl",
    ()=>buildSignedUrl,
    "createQueryString",
    ()=>createQueryString,
    "generateAuthHeaders",
    ()=>generateAuthHeaders,
    "generateSignature",
    ()=>generateSignature,
    "getBingXConfig",
    ()=>getBingXConfig,
    "validateBingXConfig",
    ()=>validateBingXConfig
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
function generateSignature(queryString, secretKey) {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHmac('sha256', secretKey).update(queryString).digest('hex');
}
function createQueryString(params) {
    const sortedParams = Object.keys(params).sort().reduce((result, key)=>{
        result[key] = params[key];
        return result;
    }, {});
    return new URLSearchParams(Object.entries(sortedParams).map(([key, value])=>[
            key,
            String(value)
        ])).toString();
}
function generateAuthHeaders(method, endpoint, params, config) {
    return {
        'X-BX-APIKEY': config.apiKey,
        'Content-Type': 'application/json',
        'User-Agent': 'BingX-Trading-App/1.0.0'
    };
}
function buildSignedUrl(endpoint, params, config) {
    const timestamp = Date.now();
    const paramsWithTimestamp = {
        ...params,
        timestamp
    };
    // Créer la query string pour la signature
    const queryString = createQueryString(paramsWithTimestamp);
    // Générer la signature avec la query string
    const signature = generateSignature(queryString, config.secretKey);
    // Ajouter la signature aux paramètres
    const finalParams = {
        ...paramsWithTimestamp,
        signature
    };
    const finalQueryString = createQueryString(finalParams);
    return `${config.baseUrl}${endpoint}?${finalQueryString}`;
}
function validateBingXConfig(config) {
    return !!(config.apiKey && config.secretKey && config.baseUrl && typeof config.apiKey === 'string' && typeof config.secretKey === 'string' && typeof config.baseUrl === 'string');
}
function getBingXConfig() {
    const config = {
        apiKey: process.env.BINGX_API_KEY,
        secretKey: process.env.BINGX_SECRET_KEY,
        baseUrl: process.env.BINGX_BASE_URL || 'https://open-api.bingx.com'
    };
    if (!validateBingXConfig(config)) {
        throw new Error('Configuration BingX invalide. Vérifiez vos variables d\'environnement.');
    }
    return config;
}
}),
"[project]/src/lib/rate-limiter.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getRateLimitHeaders",
    ()=>getRateLimitHeaders,
    "rateLimiter",
    ()=>rateLimiter
]);
class RateLimiter {
    requests = new Map();
    maxRequests;
    windowMs;
    constructor(maxRequests = 100, windowMs = 60000){
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
    }
    isAllowed(identifier) {
        const now = Date.now();
        const entry = this.requests.get(identifier);
        if (!entry) {
            // Première requête pour cet identifiant
            this.requests.set(identifier, {
                count: 1,
                resetTime: now + this.windowMs
            });
            return true;
        }
        if (now > entry.resetTime) {
            // La fenêtre de temps est expirée, réinitialiser
            entry.count = 1;
            entry.resetTime = now + this.windowMs;
            return true;
        }
        if (entry.count >= this.maxRequests) {
            // Limite atteinte
            return false;
        }
        // Incrémenter le compteur
        entry.count++;
        return true;
    }
    getRemainingRequests(identifier) {
        const entry = this.requests.get(identifier);
        if (!entry) return this.maxRequests;
        const now = Date.now();
        if (now > entry.resetTime) {
            return this.maxRequests;
        }
        return Math.max(0, this.maxRequests - entry.count);
    }
    getResetTime(identifier) {
        const entry = this.requests.get(identifier);
        if (!entry) return 0;
        const now = Date.now();
        if (now > entry.resetTime) {
            return 0;
        }
        return entry.resetTime - now;
    }
    // Nettoyer les entrées expirées
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.requests.entries()){
            if (now > entry.resetTime) {
                this.requests.delete(key);
            }
        }
    }
}
const rateLimiter = new RateLimiter(parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'));
// Nettoyer les entrées expirées toutes les 5 minutes
setInterval(()=>{
    rateLimiter.cleanup();
}, 5 * 60 * 1000);
function getRateLimitHeaders(identifier) {
    return {
        'X-RateLimit-Limit': rateLimiter['maxRequests'].toString(),
        'X-RateLimit-Remaining': rateLimiter.getRemainingRequests(identifier).toString(),
        'X-RateLimit-Reset': Math.ceil((Date.now() + rateLimiter.getResetTime(identifier)) / 1000).toString()
    };
}
}),
"[project]/src/lib/api-middleware.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ApiException",
    ()=>ApiException,
    "withApiMiddleware",
    ()=>withApiMiddleware,
    "withErrorHandling",
    ()=>withErrorHandling,
    "withRateLimit",
    ()=>withRateLimit,
    "withValidation",
    ()=>withValidation
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/rate-limiter.ts [app-route] (ecmascript)");
;
;
class ApiException extends Error {
    statusCode;
    code;
    details;
    constructor(statusCode, code, message, details){
        super(message), this.statusCode = statusCode, this.code = code, this.details = details;
        this.name = 'ApiException';
    }
}
// Obtenir l'identifiant client pour le rate limiting
function getClientIdentifier(request) {
    // En production, utiliser l'IP réelle
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown';
    return ip;
}
function withRateLimit(handler) {
    return async (request, ...args)=>{
        const identifier = getClientIdentifier(request);
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rateLimiter"].isAllowed(identifier)) {
            const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRateLimitHeaders"])(identifier);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Trop de requêtes',
                message: 'Limite de taux dépassée. Veuillez réessayer plus tard.',
                code: 'RATE_LIMIT_EXCEEDED'
            }, {
                status: 429,
                headers
            });
        }
        const response = await handler(request, ...args);
        // Ajouter les headers de rate limiting à la réponse
        const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRateLimitHeaders"])(identifier);
        Object.entries(headers).forEach(([key, value])=>{
            response.headers.set(key, value);
        });
        return response;
    };
}
function withErrorHandling(handler) {
    return async (request, ...args)=>{
        try {
            return await handler(request, ...args);
        } catch (error) {
            console.error('Erreur API:', error);
            if (error instanceof ApiException) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: error.code,
                    message: error.message,
                    details: error.details
                }, {
                    status: error.statusCode
                });
            }
            // Erreur générique
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'INTERNAL_SERVER_ERROR',
                message: 'Une erreur interne s\'est produite'
            }, {
                status: 500
            });
        }
    };
}
function withValidation(schema) {
    return function(handler) {
        return async (request, ...args)=>{
            try {
                let data = {};
                if (request.method === 'GET') {
                    const { searchParams } = new URL(request.url);
                    data = Object.fromEntries(searchParams.entries());
                } else if (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH') {
                    data = await request.json();
                }
                // Validation basique (peut être étendue avec une bibliothèque comme Zod)
                const validationResult = validateData(data, schema);
                if (!validationResult.isValid) {
                    throw new ApiException(400, 'VALIDATION_ERROR', 'Données invalides', validationResult.errors);
                }
                return await handler(request, ...args);
            } catch (error) {
                if (error instanceof ApiException) {
                    throw error;
                }
                throw new ApiException(400, 'INVALID_REQUEST', 'Requête invalide');
            }
        };
    };
}
// Fonction de validation simple
function validateData(data, schema) {
    const errors = [];
    for (const [field, rules] of Object.entries(schema)){
        const value = data[field];
        const fieldRules = rules;
        if (fieldRules.required && (value === undefined || value === null || value === '')) {
            errors.push(`Le champ ${field} est requis`);
            continue;
        }
        if (value !== undefined && value !== null && value !== '') {
            if (fieldRules.type && typeof value !== fieldRules.type) {
                errors.push(`Le champ ${field} doit être de type ${fieldRules.type}`);
            }
            if (fieldRules.min && typeof value === 'number' && value < fieldRules.min) {
                errors.push(`Le champ ${field} doit être supérieur ou égal à ${fieldRules.min}`);
            }
            if (fieldRules.max && typeof value === 'number' && value > fieldRules.max) {
                errors.push(`Le champ ${field} doit être inférieur ou égal à ${fieldRules.max}`);
            }
            if (fieldRules.pattern && typeof value === 'string' && !fieldRules.pattern.test(value)) {
                errors.push(`Le champ ${field} ne respecte pas le format requis`);
            }
            if (fieldRules.enum && !fieldRules.enum.includes(value)) {
                errors.push(`Le champ ${field} doit être l'une des valeurs: ${fieldRules.enum.join(', ')}`);
            }
        }
    }
    return {
        isValid: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined
    };
}
function withApiMiddleware(handler, options = {}) {
    let wrappedHandler = handler;
    // Appliquer la validation si fournie
    if (options.validation) {
        wrappedHandler = withValidation(options.validation)(wrappedHandler);
    }
    // Appliquer le rate limiting si activé
    if (options.rateLimit !== false) {
        wrappedHandler = withRateLimit(wrappedHandler);
    }
    // Toujours appliquer la gestion d'erreurs
    wrappedHandler = withErrorHandling(wrappedHandler);
    return wrappedHandler;
}
}),
"[project]/src/lib/logger.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "LogLevel",
    ()=>LogLevel,
    "getRequestContext",
    ()=>getRequestContext,
    "logger",
    ()=>logger,
    "withLogging",
    ()=>withLogging
]);
var LogLevel = /*#__PURE__*/ function(LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
    return LogLevel;
}({});
class Logger {
    logLevel;
    constructor(){
        this.logLevel = ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : 3;
    }
    formatTimestamp() {
        return new Date().toISOString();
    }
    shouldLog(level) {
        return level <= this.logLevel;
    }
    createLogEntry(level, message, data, context) {
        return {
            timestamp: this.formatTimestamp(),
            level,
            message,
            data,
            ...context
        };
    }
    writeLog(entry) {
        if (!this.shouldLog(entry.level)) return;
        const levelName = LogLevel[entry.level];
        const logMessage = `[${entry.timestamp}] ${levelName}: ${entry.message}`;
        switch(entry.level){
            case 0:
                console.error(logMessage, entry.data || '');
                break;
            case 1:
                console.warn(logMessage, entry.data || '');
                break;
            case 2:
                console.info(logMessage, entry.data || '');
                break;
            case 3:
                console.debug(logMessage, entry.data || '');
                break;
        }
        // En production, ici on pourrait envoyer les logs vers un service externe
        // comme CloudWatch, Datadog, etc.
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    sendToExternalService(entry) {
    // Implémentation pour envoyer vers un service de logging externe
    // Par exemple: CloudWatch, Datadog, LogRocket, etc.
    }
    error(message, data, context) {
        const entry = this.createLogEntry(0, message, data, context);
        this.writeLog(entry);
    }
    warn(message, data, context) {
        const entry = this.createLogEntry(1, message, data, context);
        this.writeLog(entry);
    }
    info(message, data, context) {
        const entry = this.createLogEntry(2, message, data, context);
        this.writeLog(entry);
    }
    debug(message, data, context) {
        const entry = this.createLogEntry(3, message, data, context);
        this.writeLog(entry);
    }
    // Méthodes spécialisées pour la sécurité
    security(message, data, context) {
        const entry = this.createLogEntry(1, `[SECURITY] ${message}`, data, context);
        this.writeLog(entry);
    }
    trading(message, data, context) {
        const entry = this.createLogEntry(2, `[TRADING] ${message}`, data, context);
        this.writeLog(entry);
    }
    api(message, data, context) {
        const entry = this.createLogEntry(2, `[API] ${message}`, data, context);
        this.writeLog(entry);
    }
}
const logger = new Logger();
function getRequestContext(request) {
    const url = new URL(request.url);
    return {
        ip: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        endpoint: url.pathname,
        method: request.method
    };
}
function withLogging(handler) {
    return async (request, ...args)=>{
        const context = getRequestContext(request);
        const startTime = Date.now();
        logger.api(`Requête reçue: ${context.method} ${context.endpoint}`, null, context);
        try {
            const response = await handler(request, ...args);
            const duration = Date.now() - startTime;
            logger.api(`Requête traitée avec succès: ${context.method} ${context.endpoint}`, {
                duration: `${duration}ms`,
                status: response.status
            }, context);
            return response;
        } catch (error) {
            const duration = Date.now() - startTime;
            logger.error(`Erreur lors du traitement de la requête: ${context.method} ${context.endpoint}`, {
                error: error instanceof Error ? error.message : 'Erreur inconnue',
                duration: `${duration}ms`,
                stack: error instanceof Error ? error.stack : undefined
            }, context);
            throw error;
        }
    };
}
}),
"[project]/src/app/api/bingx/orders/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "DELETE",
    ()=>DELETE,
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/bingx-auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-middleware.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/logger.ts [app-route] (ecmascript)");
;
;
;
;
;
// Récupérer les ordres
const getOrdersHandler = async (request)=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRequestContext"])(request);
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBingXConfig"])();
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');
    const orderId = searchParams.get('orderId');
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].api('Récupération des ordres', {
        symbol,
        orderId
    }, context);
    let endpoint;
    let params = {};
    if (orderId) {
        // Récupérer un ordre spécifique
        endpoint = '/openApi/swap/v2/trade/order';
        params = {
            orderId
        };
        if (symbol) params.symbol = symbol;
    } else {
        // Récupérer tous les ordres ouverts
        endpoint = '/openApi/swap/v2/trade/openOrders';
        if (symbol) params.symbol = symbol;
    }
    const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildSignedUrl"])(endpoint, params, config);
    const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateAuthHeaders"])('GET', endpoint, params, config);
    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
        headers,
        timeout: 10000
    });
    if (response.data.code !== 0) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warn('Erreur API BingX lors de la récupération des ordres', {
            code: response.data.code,
            message: response.data.msg
        }, context);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: response.data.msg
        }, {
            status: 400
        });
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].api('Ordres récupérés avec succès', {
        count: Array.isArray(response.data.data) ? response.data.data.length : 1
    }, context);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response.data.data);
};
const GET = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withApiMiddleware"])(getOrdersHandler);
// Schéma de validation pour la création d'ordres
const createOrderValidation = {
    symbol: {
        required: true,
        type: 'string',
        pattern: /^[A-Z]+-[A-Z]+$/
    },
    side: {
        required: true,
        type: 'string',
        enum: [
            'BUY',
            'SELL'
        ]
    },
    type: {
        required: true,
        type: 'string',
        enum: [
            'MARKET',
            'LIMIT',
            'STOP_MARKET',
            'TAKE_PROFIT_MARKET'
        ]
    },
    quantity: {
        type: 'string',
        pattern: /^\d+(\.\d+)?$/
    },
    price: {
        type: 'string',
        pattern: /^\d+(\.\d+)?$/
    },
    stopPrice: {
        type: 'string',
        pattern: /^\d+(\.\d+)?$/
    }
};
// Créer un nouvel ordre
const createOrderHandler = async (request)=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRequestContext"])(request);
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBingXConfig"])();
    const orderData = await request.json();
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].trading('Création d\'un nouvel ordre', {
        symbol: orderData.symbol,
        side: orderData.side,
        type: orderData.type,
        quantity: orderData.quantity
    }, context);
    // Validation de la quantité pour les ordres non-market
    if (orderData.type !== 'MARKET' && !orderData.quantity && !orderData.quoteOrderQty) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warn('Tentative de création d\'ordre sans quantité', orderData, context);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Quantité requise pour les ordres non-market'
        }, {
            status: 400
        });
    }
    // Validation du prix pour les ordres limit
    if (orderData.type === 'LIMIT' && !orderData.price) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warn('Tentative de création d\'ordre limit sans prix', orderData, context);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Prix requis pour les ordres limit'
        }, {
            status: 400
        });
    }
    const endpoint = '/openApi/swap/v2/trade/order';
    const params = {
        ...orderData,
        timestamp: Date.now()
    };
    const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildSignedUrl"])(endpoint, params, config);
    const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateAuthHeaders"])('POST', endpoint, params, config);
    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(url, null, {
        headers,
        timeout: 10000
    });
    if (response.data.code !== 0) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warn('Erreur API BingX lors de la création d\'ordre', {
            code: response.data.code,
            message: response.data.msg,
            orderData
        }, context);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: response.data.msg
        }, {
            status: 400
        });
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].trading('Ordre créé avec succès', {
        orderId: response.data.data.orderId,
        symbol: response.data.data.symbol,
        side: response.data.data.side,
        type: response.data.data.type
    }, context);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response.data.data);
};
const POST = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withApiMiddleware"])(createOrderHandler, {
    validation: createOrderValidation
});
// Annuler un ordre
const cancelOrderHandler = async (request)=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRequestContext"])(request);
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBingXConfig"])();
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');
    const orderId = searchParams.get('orderId');
    if (!symbol || !orderId) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warn('Tentative d\'annulation d\'ordre sans paramètres requis', {
            symbol,
            orderId
        }, context);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Symbol et orderId sont requis'
        }, {
            status: 400
        });
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].trading('Annulation d\'ordre', {
        symbol,
        orderId
    }, context);
    const endpoint = '/openApi/swap/v2/trade/order';
    const params = {
        symbol,
        orderId
    };
    const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildSignedUrl"])(endpoint, params, config);
    const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$bingx$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateAuthHeaders"])('DELETE', endpoint, params, config);
    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].delete(url, {
        headers,
        timeout: 10000
    });
    if (response.data.code !== 0) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warn('Erreur API BingX lors de l\'annulation d\'ordre', {
            code: response.data.code,
            message: response.data.msg,
            symbol,
            orderId
        }, context);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: response.data.msg
        }, {
            status: 400
        });
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].trading('Ordre annulé avec succès', {
        orderId: response.data.data.orderId,
        symbol: response.data.data.symbol,
        status: response.data.data.status
    }, context);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response.data.data);
};
const DELETE = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withApiMiddleware"])(cancelOrderHandler);
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__a2d6dd90._.js.map