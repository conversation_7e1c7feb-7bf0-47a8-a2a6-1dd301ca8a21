var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/bingx/account/route.js")
R.c("server/chunks/node_modules_ed2014bb._.js")
R.c("server/chunks/[root-of-the-server]__5265f9ea._.js")
R.m("[project]/.next-internal/server/app/api/bingx/account/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bingx/account/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/bingx/account/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
