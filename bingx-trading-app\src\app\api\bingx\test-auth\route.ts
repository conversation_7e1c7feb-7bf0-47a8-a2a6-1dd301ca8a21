import { NextRequest, NextResponse } from 'next/server';
import { getBingXConfig, buildSignedUrl, generateAuthHeaders } from '@/lib/bingx-auth';
import { logger, getRequestContext } from '@/lib/logger';

export async function GET(request: NextRequest) {
  const context = getRequestContext(request);
  
  try {
    const config = getBingXConfig();
    
    logger.info('Test d\'authentification BingX', {
      apiKey: config.apiKey.substring(0, 8) + '...',
      baseUrl: config.baseUrl
    }, context);
    
    // Test avec l'endpoint le plus simple - server time
    const endpoint = '/openApi/swap/v2/server/time';
    const params = {};
    
    const url = buildSignedUrl(endpoint, params, config);
    const headers = generateAuthHeaders('GET', endpoint, params, config);
    
    logger.info('URL de test générée', { url: url.substring(0, 100) + '...' }, context);
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });
    
    const data = await response.json();
    
    logger.info('Réponse du test d\'authentification', {
      status: response.status,
      data: data
    }, context);
    
    return NextResponse.json({
      success: response.ok,
      status: response.status,
      data: data,
      testUrl: url.substring(0, 100) + '...',
    });
    
  } catch (error) {
    logger.error('Erreur lors du test d\'authentification', error, context);
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue' 
      },
      { status: 500 }
    );
  }
}
